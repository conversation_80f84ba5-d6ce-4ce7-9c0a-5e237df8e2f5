package com.wxbc.rhine3.admin.controller.cfca;

import com.github.pagehelper.PageInfo;
import com.wxbc.rhine3.bean.VenaSealInfo;
import com.wxbc.rhine3.bean.request.cfcasignature.VenaDeleteSealReq;
import com.wxbc.rhine3.bean.request.cfcasignature.VenaMakeSealInfoReq;
import com.wxbc.rhine3.bean.request.cfcasignature.VenaQuerySealListReq;
import com.wxbc.rhine3.common.Response;
import com.wxbc.rhine3.admin.constant.ApiDesConst;
import com.wxbc.rhine3.constants.CommonConst;
import com.wxbc.rhine3.constants.url.CfcaSignatureUrl;
import com.wxbc.rhine3.admin.service.cfca.OoCfcaSignatureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;

/**
 * 签章控制类
 */
@RestController
@Validated
@Slf4j
@Api(tags = "cfca签章相关接口")
@RequestMapping("/oo")
public class OoCfcaSignatureController {

    @Autowired
    private OoCfcaSignatureService cfcaSignatureService;

    @ApiOperation(value = ApiDesConst.API_DES_MAKE_SEAL, notes = ApiDesConst.API_DES_MAKE_SEAL)
    @PostMapping(value = CfcaSignatureUrl.API_URL_MAKE_SEAL, produces = {CommonConst.PRODUCE})
    public Response makeSeal(@Valid @RequestBody VenaMakeSealInfoReq venaMakeSealInfoReq) throws IOException {
         return cfcaSignatureService.makeSeal(venaMakeSealInfoReq);
    }

    /**
     * 查询当前系统所有印章图片
     */
    @ApiOperation(value = ApiDesConst.API_URL_GET_FINANCE_LIST_SEALIMAGE, notes = ApiDesConst.API_URL_GET_FINANCE_LIST_SEALIMAGE)
    @PostMapping(value = CfcaSignatureUrl.API_URL_QUERY_SEAL_LIST_BY_PROJECT, produces = {CommonConst.PRODUCE})
    public Response<PageInfo<VenaSealInfo>> querySealImageList(@Valid @RequestBody VenaQuerySealListReq venaQuerySealListReq) throws IOException {
        PageInfo<VenaSealInfo> result = cfcaSignatureService.querySealListByProject(venaQuerySealListReq);
        return Response.success(result);
    }

    /**
     * 刪除印章
     */
    @ApiOperation(value = ApiDesConst.API_DES_DELETE_SEAL_BY_COMPANY, notes = ApiDesConst.API_DES_DELETE_SEAL_BY_COMPANY)
    @PostMapping(value = CfcaSignatureUrl.API_URL_DELETE_SEAL_BY_COMPANY, produces = {CommonConst.PRODUCE})
    public Response<Void> deleteSealByCompany(@Valid @RequestBody VenaDeleteSealReq deleteSealReq) throws IOException {
       cfcaSignatureService.deleteCompanySeal(deleteSealReq);
        return Response.success();
    }



}


