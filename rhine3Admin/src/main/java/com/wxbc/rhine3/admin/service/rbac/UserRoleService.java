package com.wxbc.rhine3.admin.service.rbac;

import com.google.common.collect.Lists;
import com.wxbc.rhine3.api.UserRoleApi;
import com.wxbc.rhine3.common.lob.Lob;
import com.wxbc.rhine3.common.lob.Project;
import com.wxbc.rhine3.exception.BusinessException;
import com.wxbc.rhine3.model.dto.LobEntry;
import com.wxbc.rhine3.model.dto.UpdateUser2AdminReq;
import com.wxbc.rhine3.model.dto.UserRoleUpdateReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * service-vena
 *
 * <AUTHOR> chen cheng
 * Date         : 2023/4/4 14:03
 * Jdk          : 1.8
 * Description  :
 */
@Slf4j
@Service
public class UserRoleService {

    @Resource
    private UserRoleApi userRoleApi;


    public boolean updateUser2Admin(Long userId, String lobs) {
        UpdateUser2AdminReq req = new UpdateUser2AdminReq();
        req.setUserId(userId);
        final String[] lobArr = lobs.split(",");
        List<LobEntry> entryList = Lists.newArrayList();
        for (String str : lobArr) {
            final Lob lob = Lob.of(str);
            LobEntry entry = new LobEntry(lob.name().toLowerCase(), Project.OO.code());
            entryList.add(entry);
        }
        req.setEntryList(entryList);
        userRoleApi.updateUser2Admin(req);
        return true;
    }

    /**
     * 保存用户角色
     * @param userId     user id
     * @param roleIdList 角色列表
     * @return true -成功， false - 失败
     */
    public boolean updateUserRole(Long userId, List<Long> roleIdList) {
        UserRoleUpdateReq req = new UserRoleUpdateReq();
        req.setUserId(userId);
        req.setRoleIdList(roleIdList);
        req.setProjectCode(Project.OO.code());
        boolean success = userRoleApi.updateUserRole(req);
        if(!success) {
            log.error("create user role error, false");
            throw new BusinessException("创建用户失败");
        }
        return true;
    }
}
