buildscript {
    ext {
        springBootVersion = '2.3.5.RELEASE'
    }
    repositories {
        maven {
            allowInsecureProtocol = true
            url "${repo_path}"
        }
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
    }
}

group 'com.wxbc'
version '*******'


subprojects {
    apply plugin: 'java-library'
    apply plugin: 'idea'
    apply plugin: 'io.spring.dependency-management'

    sourceCompatibility = 1.8
    targetCompatibility = 1.8

    ext['log4j2.version'] = '2.16.0'

    jar {
        enabled = true
    }

    [compileJava,compileTestJava,javadoc]*.options*.encoding = 'UTF-8'

    repositories {
        maven {
            allowInsecureProtocol = true
            url "${repo_path}"
        }
    }

    configurations{
        all*.exclude module : 'spring-boot-starter-logging'
    }

    dependencyManagement {
        imports {
            mavenBom 'com.wxbc:wx-parent:0.0.1'
        }

        resolutionStrategy {
            cacheChangingModulesFor 0, 'seconds'
        }

    }
}
