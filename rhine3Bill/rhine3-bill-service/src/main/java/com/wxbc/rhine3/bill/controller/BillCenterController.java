package com.wxbc.rhine3.bill.controller;

import com.wxbc.base.annotations.WxAppController;
import com.wxbc.rhine3.bill.bo.InquiryReq;
import com.wxbc.rhine3.bill.common.constant.CommonConst;
import com.wxbc.rhine3.bill.model.bo.BillPicUploadReq;
import com.wxbc.rhine3.bill.model.vo.BillPicUploadVO;
import com.wxbc.rhine3.bill.service.BillCenterService;
import com.wxbc.rhine3.bill.vo.BankInquiryMessageVO;
import com.wxbc.scaffold.common.definition.response.ResponseFormat;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@WxAppController
public class BillCenterController {

    @Resource
    private BillCenterService billCenterService;

    @ApiOperation("首页询价列表")
    @PostMapping("/bill/anybody/bank-inquiry/list")
    public ResponseFormat<List<BankInquiryMessageVO>> getBankInquiryMessageDatas(@RequestBody @Valid InquiryReq inquiryReq) {

        List<BankInquiryMessageVO> datas = billCenterService.getBankInquiryMessageDatas(inquiryReq);

        return ResponseFormat.success(datas);
    }

    @ApiOperation("票据上传")
    @PostMapping(value = "/bill/pic/upload", produces = {CommonConst.PRODUCE})
    public ResponseFormat<BillPicUploadVO> billPicUpload(@RequestBody @Valid BillPicUploadReq req) throws Exception{

        BillPicUploadVO billPicUploadVO = billCenterService.billPicUpload(req);

        return ResponseFormat.success(billPicUploadVO);
    }
}
