package com.wxbc.rhine3.bill.model.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class BankMessageAddReq implements Serializable {

    /**
     * 银行名称
     */
    @NotBlank(message = "银行名称不能为空")
    @ApiModelProperty(value = "银行名称")
    private String bankName;

    /**
     * 银行行号
     */
    @NotBlank(message = "银行行号不能为空")
    @ApiModelProperty(value = "银行行号")
    @Pattern(regexp = "^[0-9]{12}$",message = "银行行号格式不正确")
    private String bankCode;

    /**
     * 银行logo
     */
    @NotBlank(message = "银行logo不能为空")
    @ApiModelProperty(value = "银行logo")
    private String bankLogo;

    /**
     * 交易时间
     */
    /*@TableField("`transaction_time`")
    @ApiModelProperty(value = "交易时间")
    private String transactionTime;

    *//**
     * 单张金额
     *//*
    @TableField("`single_amount`")
    @NotNull(message = "单张金额不能为空")
    @ApiModelProperty(value = "单张金额")
    private BigDecimal singleAmount;*/

    /**
     * 接入状态：0:未接入，1:已接入
     */
    @TableField("`access_type`")
    private Integer accessType;

    /**
     * 首页推荐：0:不推荐，1:推荐
     */
    @TableField("`home_recommend`")
    private Integer homeRecommend;

    /**
     * 展示询价结果：0:否，1:是
     */
    @TableField("`show_result`")
    private int showResult;

    /**
     * 贴现方式：1:二维码扫描，2:接口对接
     */
    @TableField("`discount_method`")
    private Integer discountMethod;

    /**
     * 贴现二维码url
     */
    @TableField("`qr_code_url`")
    private String qrCodeUrl;
}
