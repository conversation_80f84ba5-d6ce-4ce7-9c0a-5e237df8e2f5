package com.wxbc.rhine3.bill.bean;

import com.wxbc.rhine3.bill.common.bean.BaseInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@ApiModel
public class User extends BaseInfo {
    private String email;
    private String mobile;
    private String username;
    private String password;
    private String companyUuid;
    private BigDecimal quotaInCent;
    @ApiModelProperty(value = "运营机构的公司uuid")
    private String operatingOrganizationUuid;

    //待Vena老用户迁移完成后删除privKeyEnc、companyPrivKeyEnc、pubKey
    private String privKeyEnc;
    private String companyPrivKeyEnc;
    private String pubKey;

    @ApiModelProperty(value = "注册来源/所属项目: 1-vena, 2-bill")
    private String projectCode;

    //以下非数据库字段
    @ApiModelProperty(value = "当前登录的企业身份，管理员可以选择多个身份，其它用户登录时只能选单个企业类型")
    private String loginCompanyType;
    private Company company;
    private List<String> roleNames;
    private List<Role> roleList;
    //方便直接查询企业名称
    private String companyName;
}
