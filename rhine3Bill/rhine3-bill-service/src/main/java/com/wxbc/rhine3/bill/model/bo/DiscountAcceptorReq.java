package com.wxbc.rhine3.bill.model.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class DiscountAcceptorReq implements Serializable {


    /**
     * 企业名称
     */
    @NotBlank(message = "companyName不能为空")
    private String companyName;

    /**
     * 企业类型：1:银行，2:普通企业
     */
    @NotNull(message = "companyType必填")
    private Integer companyType;
}
