package com.wxbc.rhine3.bill.aop;

import com.wxbc.rhine3.bill.common.constant.ReturnCode;
import com.wxbc.scaffold.common.definition.exception.BizException;
import com.wxbc.scaffold.common.definition.response.ResponseFormat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashSet;
import java.util.Set;

@RestControllerAdvice
@Slf4j
public class BillGlobalExceptionHandler {

    /**
     * 拦截表单参数校验
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({BindException.class})
    public ResponseFormat bindException(BindException e) {
        ResponseFormat format = ResponseFormat.fail(ReturnCode.VALIDATE_FAILED);
        format.setReturnDesc(getErrMsg(e.getBindingResult()));
        return format;
    }

    /**
     * 拦截JSON参数校验
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseFormat methodArgumentNotValidException(MethodArgumentNotValidException e) {
        ResponseFormat format = ResponseFormat.fail(ReturnCode.VALIDATE_FAILED);
        format.setReturnDesc(getErrMsg(e.getBindingResult()));
        return format;
    }

    /**
     * 拦截参数类型不正确
     * @param e
     * @return
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ResponseFormat httpMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException e){
        return ResponseFormat.fail(ReturnCode.CONTENT_TYPE_NOT_MATCH, e.getMessage());
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(BizException.class)
    public ResponseFormat serviceException(BizException e) {
        log.error("ERROR BizException:Code:{},errorMsg:{}", e.getReturnCode(), e.getMsg(), e);
        return ResponseFormat.fail(e.getReturnCode());
    }

    /**
     * 声明要捕获的异常
     * @param e
     * @param <T>
     * @return
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(Exception.class)
    public <T> ResponseFormat<?> defaultException(Exception e) {
        log.error("ERROR UnknowError:", e);
        //未知错误
        return ResponseFormat.fail(ReturnCode.SYSTEM_ERROR);
    }

    private String getErrMsg (BindingResult result) {
        if (null == result) {
            return null;
        }
        StringBuilder errorMsg = new StringBuilder();
        Set<String> set = new HashSet<>();
        for (ObjectError error : result.getAllErrors()) {
            if (set.contains(error.getDefaultMessage())) {
                continue;
            }
            set.add(error.getDefaultMessage());
            errorMsg.append(error.getDefaultMessage()).append(",");
        }
        errorMsg.delete(errorMsg.length() - 1, errorMsg.length());
        return errorMsg.toString();
    }

}
