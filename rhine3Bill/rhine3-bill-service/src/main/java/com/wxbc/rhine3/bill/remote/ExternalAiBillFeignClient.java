package com.wxbc.rhine3.bill.remote;

import com.wxbc.rhine3.bill.bo.*;
import com.wxbc.rhine3.bill.common.bean.CommonResponse;
import com.wxbc.rhine3.bill.common.constant.CommonConst;
import com.wxbc.rhine3.bill.vo.AccBankListResponse;
import com.wxbc.rhine3.bill.vo.InquiryManyBankResponse;
import com.wxbc.rhine3.bill.vo.ValidateResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;
import java.util.Map;

@FeignClient(name = "externalAiBill", url = "${external.aiBill.host:https://wxtest.aibill.cn}",
        fallback = ExternalAiBillFeignClientHystrix.class)
public interface ExternalAiBillFeignClient {

    // 对接【果腾】login --> 暂时没用到
    @PostMapping(value = "/api/Bill/login", produces = {CommonConst.PRODUCE})
    CommonResponse<Map<String,Object>> login(ExternalLoginUserReq loginUser);

    // 对接【果腾】获取承兑人列表
    @PostMapping(value = "/api/Bill/accBankList", produces = {CommonConst.PRODUCE})
    CommonResponse<List<AccBankListResponse>> accBankList(ExternalLoginUserReq loginUser);


    // 对接【果腾】询价接口
    @PostMapping(value = "/api/Bill/InquiryManyBank", produces = {CommonConst.PRODUCE})
    CommonResponse<List<InquiryManyBankResponse>> inquiryManyBank(InquiryManyBankReq inquiryReq);

    // 对接【果腾】票据识别风险提示
    @PostMapping(value = "/api/Bill/validate", produces = {CommonConst.PRODUCE})
    CommonResponse<List<ValidateResponse>> validate(BillValidateSecondReq req);

}
