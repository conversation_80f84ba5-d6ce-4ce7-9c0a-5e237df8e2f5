package com.wxbc.rhine3.bill.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wxbc.rhine3.bill.repository.entity.BillUploadRecordEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;


/**
 * Description : auto generated dao
 * JDK         : 1.8
 * ProjectName : rhine3-bill
 * Date        : 2022-11-11 15:38:24
 * <AUTHOR> zhousiqi
 */
public interface BillUploadRecordDao extends BaseMapper<BillUploadRecordEntity> {

    @Update({
            "<script>"+
            "UPDATE bill_upload_record set call_back_info = #{callBackInfo} where pic_id =#{picId}"+
            "</script>"
    })
    int updateUploadRecord(@Param("picId") String picId,@Param("callBackInfo") String callBackInfo);
}