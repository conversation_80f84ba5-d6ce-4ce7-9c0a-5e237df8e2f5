package com.wxbc.rhine3.bill.service;

import com.wxbc.base.util.BeanUtil;
import com.wxbc.base.util.JsonUtil;
import com.wxbc.rhine3.bill.repository.entity.BillOcrAsyncMessageEntity;
import com.wxbc.rhine3.bill.repository.entity.BillUploadRecordEntity;
import com.wxbc.rhine3.bill.bo.OcrAsyncCallBackReq;
import com.wxbc.rhine3.bill.model.bo.QueryUploadRecordReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
public class BillOcrAsyncMessageService extends BillOcrBaseService{


    public void billPicAsyncCallback(OcrAsyncCallBackReq req){
        String picId = req.getPicId();
        String companyUuid = "";
        String uploader = "";
        String organizationUuid = "";
        // 获取 bill_upload_record
        String content = JsonUtil.object2String(req);
        List<BillUploadRecordEntity> billUploadRecordEntities = queryDataInfo(new QueryUploadRecordReq(picId));
        if(!CollectionUtils.isEmpty(billUploadRecordEntities)){
            BillUploadRecordEntity entity = billUploadRecordEntities.get(0);
            companyUuid = entity.getCompanyUuid();
            uploader = entity.getUploader();
            organizationUuid = entity.getOrganizationUuid();
            billUploadRecordDao.updateUploadRecord(picId,content);
        }
        BillOcrAsyncMessageEntity entity = new BillOcrAsyncMessageEntity();
        BeanUtil.copyProperties(req,entity);
        entity.setCompanyUuid(companyUuid);
        entity.setUploader(uploader);
        entity.setOrganizationUuid(organizationUuid);
        billOcrAsyncMessageDao.insert(entity);
    }


}
