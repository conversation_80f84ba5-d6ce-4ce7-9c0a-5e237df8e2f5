package com.wxbc.rhine3.bill.model.bo;

import com.wxbc.scaffold.common.definition.response.BasePaginationRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryDiscountAcceptorReq extends BasePaginationRequest implements Serializable {

    int size = 10;

    private String companyName;

    private List<String> companyNames;

    private String dbCompanyName;

    public QueryDiscountAcceptorReq() {
    }

    public QueryDiscountAcceptorReq(String companyName) {
        this.companyName = companyName;
    }
}
