package com.wxbc.rhine3.bill.common.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @company: 万向区块链
 * @description 分液参数实体类
 * @author: lixingxing
 * @create: 2021-07-27 16:48
 **/
@Data
@ApiModel("分页实体类")
public class PagePara {

    @ApiModelProperty(value = "当前页码")
    private Integer pageNum;
    @ApiModelProperty(value = "每页数据大小")
    private Integer pageSize;
}
