package com.wxbc.rhine3.bill.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Builder
public class InquiryRes  implements Serializable {

    @ApiModelProperty(value = "承兑人")
    private String companyName;

    @ApiModelProperty(value = "贴现率")
    private String discountRate;

    @ApiModelProperty(value = "预计成交价")
    private String transactionPrice;

    private String weekendStr;

    private String workdayStr;

    public InquiryRes() {
    }

    public InquiryRes(String companyName, String discountRate, String transactionPrice, String weekendStr, String workdayStr) {
        this.companyName = companyName;
        this.discountRate = discountRate;
        this.transactionPrice = transactionPrice;
        this.weekendStr = weekendStr;
        this.workdayStr = workdayStr;
    }
}
