package com.wxbc.rhine3.bill.common.bean;

import com.wxbc.rhine3.bill.common.constant.ReturnCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;


/**
 * Created by <PERSON><PERSON>i on 3/27/19
 */

@Getter
@Setter
@ToString
public class Response<T> {
    private Integer returnCode; //状态返回码
    private String returnDesc; //响应状态说明
    private T data; //响应数据
    private List<T> items;

    public Response(){}


    public Response(int returnCode, String returnDesc, T data) {
        this.returnCode = returnCode;
        this.returnDesc = returnDesc;
        this.data = data;
    }

    public static <T> Response<T> of(ReturnCode returnCode){
        return of(returnCode, null);
    }

    public static <T> Response<T> of(ReturnCode returnCode, T data){
        return of(returnCode.getValue(), returnCode.getDesc(), data);
    }

    public static <T> Response<T> of(int code, String msg, T data) {
        return new Response<T>(code, msg, data);
    }

    public static <T> Response<T> of(int code, String msg) {
        return new Response<T>(code, msg, null);
    }

    public static <T> Response<T> fail(){
        return of(ReturnCode.REQUEST_FAILED);
    }

    public static <T> Response<T> fail(String msg){
        return of(ReturnCode.REQUEST_FAILED.getValue(), msg);
    }

    public static <T> Response<T> success(){
        return success(null);
    }

    public static <T> Response<T> success(T data){
        return of(ReturnCode.REQUEST_SUCCESS, data);
    }

    public static  <T> Response<T> forbidden(String msg){
        return of(ReturnCode.FORBIDDEN.getValue(), msg);
    }

    public static <T> Response<T>  unauthorized(String msg){
        return of(ReturnCode.UNAUTHORIZED.getValue(), msg);
    }
}
