spring:
  cloud:
    nacos:
      config:
        #server-addr: ${nacos-server-addr:http://nacos.wx.bc:28848}
        server-addr: ${nacos-server-addr:nacos.i.wxblockchain.com:28848}
        namespace: ${nacos-namespace:57fa7d89-e81c-4a76-8bd0-51bf1917a891}
        group: ${nacos-group:pledge}
        cluster-name: ${nacos-cluster:shanghai}
      discovery:
        enabled: ${nacos-discovery-enabled:true}
#        server-addr: ${nacos-server-addr:http://nacos.wx.bc:28848}
        server-addr: ${nacos-server-addr:nacos.i.wxblockchain.com:28848}
        namespace: ${nacos-namespace:57fa7d89-e81c-4a76-8bd0-51bf1917a891}
        group: ${nacos-group:pledge}
wxbc:
  custom:
    crypto:
      enabled: true
      prefix: '{ENC}'
      password: 'rhine3Asset'
