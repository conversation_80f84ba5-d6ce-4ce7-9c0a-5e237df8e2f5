package com.wxbc.rhine3.schedule;

import com.wxbc.cou.manager.api.domain.Cou;
import com.wxbc.rhine3.constants.LockCouConfigPara;
import com.wxbc.rhine3.constants.LockCouSetConst;
import com.wxbc.rhine3.constants.settlement.SettlementConstants;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.redis.RedisService;
import com.wxbc.rhine3.service.SarahService;
import com.wxbc.rhine3.service.cou.CouPaymentDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @company: 万向区块链
 * @description 融信到期锁定定时任务
 * @author: lixingxing
 * @create: 2022-07-08 14:42
 **/
@Slf4j
@Component
public class CouDueToLock {

    @Autowired
    private RedisService redisService;
    @Autowired
    private LockCouConfigPara lockCouConfigPara;
    @Autowired
    private SarahService sarahService;
    @Autowired
    private CouPaymentDetailService couPaymentDetailService;

    @Scheduled(cron = LockCouSetConst.SCAN_COU_DAY_TIME)
    public void lockCouAndGeneralPayDetails() throws ParameterException {
        log.info("start lock cou schedule.....");
        int count=0;
        if (redisService.setNxLock(SettlementConstants.LOCK_COU_REDIS_LOCK_KEY, 3600)) {
            List<Cou> couList = sarahService.getLockCouList(Integer.parseInt(lockCouConfigPara.getScanCouDueDataAfterSet()));
            if(CollectionUtils.isEmpty(couList)){
                return;
            }

            for (Cou cou : couList) {
                if(!cou.getOriginalUuid().equals(cou.getUuid())){
                    continue;
                }
                try {
                    couPaymentDetailService.generalPaymentDetailsByOriginCou(cou);
                }catch (Exception e){
                    count++;
                    log.error("lockCouAndGeneralPayDetails error by couNo:{}",cou.getCouNo(),e);
                }
            }

            log.info("end lock cou schedule.....general paymentDetail file sucess size:{},",couList.size()-count);
        }
    }

}
