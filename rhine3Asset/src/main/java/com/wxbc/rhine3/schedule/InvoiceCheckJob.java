package com.wxbc.rhine3.schedule;

import com.wxbc.rhine3.config.CommonConfig;
import com.wxbc.rhine3.constants.CommonConst;
import com.wxbc.rhine3.constants.GeneralConfigConstants;
import com.wxbc.rhine3.constants.RedisConst;
import com.wxbc.rhine3.constants.SwitchValueConst;
import com.wxbc.rhine3.constants.loanafter.InvoiceAlarmTaskStatusEnum;
import com.wxbc.rhine3.redis.RedisService;
import com.wxbc.rhine3.service.loanafter.InvoiceAlarmTaskManageService;
import com.wxbc.rhine3.service.loanafter.InvoiceAlarmTaskRunService;
import com.wxbc.rhine3.service.settlement.SettlementCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/7
 */
@Slf4j
@Component
public class InvoiceCheckJob {

    @Autowired
    private InvoiceAlarmTaskRunService invoiceAlarmTaskRunService;
    @Resource
    private RedisService redisService;

    @Resource
    private SettlementCommonService settlementCommonService;


    @Resource
    private CommonConfig commonConfig;

    @Autowired
    private InvoiceAlarmTaskManageService invoiceAlarmTaskManageService;

    /**
     * 每小时的第0分钟执行
     * - 对发票关联的融资记录状态为已放款的发票
     * - 批量调用发票查验接口，并记录查验记录
     */
//    @Scheduled(cron = "0 0 */1 * * ?")
    public void invoiceCheck() {
        if (isOff()) {
            log.info("......invoice check is off.....");
            return;
        }

        log.info("start invoiceCheck");
        if (!Boolean.TRUE.equals(redisService.setNxLock(CommonConst.INVOICE_CHECK_TASK_REDIS_LOCK_KEY, RedisConst.TEN_MIN_MILLISECONDS))) {
            log.info("TASK_REDIS_LOCK_KEY is not get! end invoiceCheck ");
            return;
        }

        log.info("start invoiceCheck and start getStartNewInvoiceAlarmTask");
        Long taskId = invoiceAlarmTaskRunService.getStartNewInvoiceAlarmTask();
        if (null == taskId) {
            log.info("taskId is null! end invoiceCheck");
            redisService.delete(CommonConst.INVOICE_CHECK_TASK_REDIS_LOCK_KEY);
            return;
        }

        log.info("invoiceCheck getStartNewInvoiceAlarmTask success! taskId ={}", taskId);
        if (Boolean.TRUE.equals(redisService.setNxLock(CommonConst.INVOICE_CHECK_REDIS_LOCK_KEY, RedisConst.ONE_DAY_MILLISECONDS))) {
            log.info("start invoiceCheck and get lock success ! taskId={}", taskId);
            invoiceAlarmTaskRunService.batchInvoiceCheck(taskId);
            redisService.delete(CommonConst.INVOICE_CHECK_REDIS_LOCK_KEY);
            log.info("invoiceCheck success! taskId={}", taskId);
        }

        log.info("end invoiceCheck ! taskId={}", taskId);
    }

    /**
     * 每小时的第0分钟执行
     * - 对发票关联的融资记录状态为已放款的发票
     * - 批量调用发票查验接口，并记录查验记录
     */
    @Scheduled(cron = "0 0 */1 * * ?")
    public void invoiceCheckAsync() {
        if (isOff()) {
            log.info("......invoice check is off.....");
            return;
        }

        log.info("start invoiceCheckAsync");
        if (!Boolean.TRUE.equals(redisService.setNxLock(CommonConst.INVOICE_CHECK_TASK_REDIS_LOCK_KEY, RedisConst.TEN_MIN_MILLISECONDS))) {
            log.info("TASK_REDIS_LOCK_KEY is not get! end invoiceCheck ");
            return;
        }

        log.info("start invoiceCheckAsync and start getStartNewInvoiceAlarmTask");
        Long taskId = invoiceAlarmTaskRunService.getStartNewInvoiceAlarmTask();
        if (null == taskId) {
            log.info("taskId is null! end invoiceCheck");
            redisService.delete(CommonConst.INVOICE_CHECK_TASK_REDIS_LOCK_KEY);
            return;
        }

        log.info("invoiceCheckAsync getStartNewInvoiceAlarmTask success! taskId ={}", taskId);
        if (Boolean.TRUE.equals(redisService.setNxLock(CommonConst.INVOICE_CHECK_REDIS_LOCK_KEY, RedisConst.ONE_DAY_MILLISECONDS))) {
            log.info("start invoiceCheckAsync and get lock success ! taskId={}", taskId);
            try {
                invoiceAlarmTaskRunService.batchInvoiceCheckNew(taskId);
                log.info("invoiceCheckAsync success! taskId={}", taskId);
            } catch (Exception e) {
                invoiceAlarmTaskRunService.setTaskStatus(taskId, InvoiceAlarmTaskStatusEnum.FAIL, LocalDateTime.now());
                log.error("invoiceCheckAsync failed! taskId={}", taskId, e);
            } finally {
                // 自动创建下一轮任务
                invoiceAlarmTaskManageService.deriveInvoiceAlarmTask(taskId);
                redisService.delete(CommonConst.INVOICE_CHECK_REDIS_LOCK_KEY);
            }
        }

        log.info("end invoiceCheckAsync ! taskId={}", taskId);
    }

    @Scheduled(cron = "0 */10 * * * ?")
    public void pullInvoiceCheckResult() {
        if (isOff()) {
            log.info("......invoice check is off.....");
            return;
        }
        invoiceAlarmTaskRunService.invoiceCheckResultHandler();
    }

    private boolean isOff() {
        String value = settlementCommonService.queryGeneralConfigOfSwitch(GeneralConfigConstants.INVOICE_CHECK_SWITCH);
        return !Objects.equals(value, SwitchValueConst.ON.name());
    }
}
