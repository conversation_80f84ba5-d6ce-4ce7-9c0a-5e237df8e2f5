package com.wxbc.rhine3.filter;

import com.wxbc.base.util.JsonUtil;
import com.wxbc.base.util.StringUtil;
import com.wxbc.rhine3.bean.User;
import com.wxbc.rhine3.service.AssetCacheLoginUserService;
import com.wxbc.venus.user.dto.AccountUserDTO;
import com.wxbc.venus.user.util.UserUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.lang.Nullable;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.security.InvalidParameterException;
import java.util.*;

@Slf4j
@RequiredArgsConstructor
public class UserFillInterceptor extends HandlerInterceptorAdapter {

    private static final List<String> whiteListUrl = Arrays.asList("/**/**actuator","/anybody/**","/error","/**/**swagger**", "/**/**login","/internal/**");
    private static final AntPathMatcher antPathMatcher = new AntPathMatcher();

    private final AssetCacheLoginUserService assetCacheLoginUserService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        //挡住404等非法url请求和WxContextInterceptor保持一致
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        String requestURI = request.getRequestURI();
        if (Boolean.TRUE.equals(isMatch(requestURI))) {
            return true;
        }
        log.info("开始计算接口耗时：{}", requestURI);
        AccountUserDTO user = null;
        try {
            Enumeration<String> headerNames = request.getHeaderNames();
            Map<String, String> headMap = new LinkedHashMap<>();
            while (headerNames.hasMoreElements()) {
                String s = headerNames.nextElement();
                headMap.put(s, request.getHeader(s));
            }
            log.info("headerMap:{}", JsonUtil.object2String(headMap));

            String userId = request.getHeader("X-Test-UserId");
            if (StringUtils.isEmpty(userId)) {
                //网关会透传这个
                userId = request.getHeader("id");
            }
            if(StringUtils.isNotEmpty(userId)){
                user = assetCacheLoginUserService.getAccountFromCache(Long.valueOf(userId));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return true;
        }

        UserUtil.set(user);
        return true;
    }

    public void test1(HttpServletRequest request) {
        try {
            String contextHeaderName = request.getHeader("wx-gw-h-context-name");
            log.info("contextHeaderName:{}", contextHeaderName);
            String json;
            if (StringUtil.isBlank(contextHeaderName)) {
                json = request.getHeader("wx-gw-account-context");
            } else {
                json = request.getHeader(contextHeaderName);
            }
            log.info("json1:{}", json);
            if (StringUtil.isNoneBlank(new CharSequence[]{json})) {
                if (!json.startsWith("{")) {
                    json = new String(Base64.getDecoder().decode(json));
                }
                log.info("json2:{}", json);
                Map map = (Map) JsonUtil.jsonStr2Object(json, Map.class);
                if (!map.containsKey("id")) {
                    throw new InvalidParameterException("上下文必须包含id字段");
                }
                log.info("json3:{}", json);

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           @Nullable ModelAndView modelAndView) throws Exception {
        UserUtil.clearContext();
    }

    private Boolean isMatch(String requestURI) {
        for (String pattern : whiteListUrl) {
            if (antPathMatcher.match(pattern, requestURI)) {
                return true;
            }
        }
        return false;
    }
}
