package com.wxbc.rhine3.controller.zdw.terminate;

import com.github.pagehelper.PageInfo;
import com.wxbc.rhine3.bean.zdw.*;
import com.wxbc.rhine3.bean.zdw.change.InitRegChangeListPara;
import com.wxbc.rhine3.bean.zdw.change.InitRegChangeListRes;
import com.wxbc.rhine3.common.PagePara;
import com.wxbc.rhine3.common.Response;
import com.wxbc.rhine3.config.annotations.MultiRequestBody;
import com.wxbc.rhine3.service.zdw.terminate.ZdwTerminateRegQueryService;
import com.wxbc.rhine3.service.zdw.terminate.ZdwTerminateRegService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @company: 万向区块链
 * @description 中登网注销登记管理
 * @author: lixingxing
 * @create: 2024-03-01 14:48
 **/
@RestController
@RequestMapping("/zdw")
@Api(tags = "中登网注销登记操作接口")
public class ZdwTerminateRegController {

    @Resource
    private ZdwTerminateRegQueryService zdwTerminateRegQueryService;

    @Resource
    private ZdwTerminateRegService zdwTerminateRegService;

    @ApiOperation(value = "待注销登记列表", notes = "待注销登记列表")
    @PostMapping(value = "/wait/terminate/reg/list")
    public Response<PageInfo<WaitTerminateRegRes>> waitTerminateRegList(@MultiRequestBody PagePara pagePara, @MultiRequestBody(required = false) String applicationNumber) {
        return Response.success(zdwTerminateRegQueryService.waitTerminateRegList(pagePara, applicationNumber));
    }

    @ApiOperation(value = "注销登记详情", notes = "变更记录在188版本只有注销登记一种数据")
    @PostMapping(value = "/terminate/detail")
    public Response<ZdwTerminateRegRes> zdwTerminateRegDetail(@Valid @RequestBody ZdwTerminateRegQueryReq para) {
        return Response.success(zdwTerminateRegQueryService.zdwTerminateRegDetail(para));
    }

    @ApiOperation(value = "运营端：线上注销登记", notes = "运营端：线上注销登记")
    @PostMapping(value = "/terminate/reg/online")
    public Response<Boolean> zdwTerminateOnline(@Valid @RequestBody ZdwTerminateRegReq para) {
        return Response.success(zdwTerminateRegService.zdwTerminateOnline(para));
    }

    @ApiOperation(value = "运营端：线下注销登记", notes = "运营端：线下注销登记")
    @PostMapping(value = "/terminate/reg/offline")
    public Response<Boolean> zdwTerminateOffline(@Valid @RequestBody ZdwTerminateRegReq para) {
        return Response.success(zdwTerminateRegService.zdwTerminateOffline(para));
    }

    @ApiOperation(value = "初始登记变更列表", notes = "初始登记变更列表")
    @PostMapping(value = "/init-registration/change/list")
    public Response<List<InitRegChangeListRes>> changeList(@Valid @RequestBody InitRegChangeListPara initRegChangeListPara) {
        return Response.success(zdwTerminateRegQueryService.initRegChangeList(initRegChangeListPara));
    }

}
