package com.wxbc.rhine3.controller;

import com.github.pagehelper.PageInfo;
import com.wxbc.rhine3.bean.User;
import com.wxbc.rhine3.bean.UuidPara;
import com.wxbc.rhine3.bean.contract.*;
import com.wxbc.rhine3.bean.cou.CouContractResponse;
import com.wxbc.rhine3.bean.request.ContractCodeListRequest;
import com.wxbc.rhine3.common.Response;
import com.wxbc.rhine3.constants.ApiDesConst;
import com.wxbc.rhine3.constants.url.AssetUrl;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.service.contract.ContractService;
import com.wxbc.rhine3.service.contract.QueryContract4Finance;
import com.wxbc.venus.user.dto.AccountUserDTO;
import com.wxbc.venus.user.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

@RestController
@Validated
@Api(tags = "合同相关接口")
public class ContractController {
    @Autowired
    private QueryContract4Finance queryContract4Finance;

    @Autowired
    private ContractService contractService;

    @ApiOperation(value = ApiDesConst.API_LIST_CONTRACT, notes = ApiDesConst.API_LIST_CONTRACT)
    @PostMapping(value = AssetUrl.API_URL_LIST_CONTRACT)
    public Response<PageInfo<Contract>> queryContracts(@RequestBody ContractQueryPara queryPara) {
        PageInfo<Contract> result = contractService.queryContracts(queryPara);
        return Response.success(result);
    }

    @ApiOperation(value = ApiDesConst.API_LIST_B_CONTRACT, notes = ApiDesConst.API_LIST_B_CONTRACT)
    @PostMapping(value = AssetUrl.API_URL_LIST_B_CONTRACT)
    public Response<PageInfo<Contract>> queryBuildOperatorContracts(@RequestBody ContractQueryPara queryPara) {
        PageInfo<Contract> result = contractService.queryBuildContracts(queryPara);
        return Response.success(result);
    }

    @ApiOperation(value = ApiDesConst.API_LIST_TEAM_CONTRACT, notes = ApiDesConst.API_LIST_TEAM_CONTRACT)
    @PostMapping(value = AssetUrl.API_URL_LIST_TEAM_CONTRACT)
    public Response<PageInfo<Contract>> queryTeamOperatorContracts(@RequestBody ContractQueryPara queryPara) {
        PageInfo<Contract> result = contractService.queryTeamContracts(queryPara);
        return Response.success(result);
    }


    @ApiOperation(value = ApiDesConst.API_CREATE_CONTRACT, notes = ApiDesConst.API_CREATE_CONTRACT)
    @PostMapping(value = AssetUrl.API_URL_CREATE_CONTRACT)
    public Response<String> createContract(@Valid @RequestBody ContractCreatePara createPara) {
        return Response.success(contractService.createContract(createPara));
    }

    @ApiOperation(value = ApiDesConst.API_LIST_REFUND_CONTRACT, notes = ApiDesConst.API_LIST_REFUND_CONTRACT)
    @PostMapping(value = AssetUrl.API_URL_LIST_REFUND_CONTRACT)
    public Response<PageInfo<Contract>> refundContracts(@RequestBody ContractQueryPara queryPara) {
        PageInfo<Contract> result = contractService.refundContract(queryPara);
        return Response.success(result);
    }

    @ApiOperation(value = ApiDesConst.COU_CONTRACT_CODE_LIST, notes = ApiDesConst.COU_CONTRACT_CODE_LIST)
    @PostMapping(AssetUrl.API_URL_COU_CONTRACT_CODE_LIST)
    public Response<List<CouContractResponse>> couContractCodeList(@RequestBody @Valid ContractCodeListRequest request) {
        List<CouContractResponse> result = queryContract4Finance.getContractList(request);
        return Response.success(result);
    }

    @ApiOperation(value = ApiDesConst.API_MODIFY_CONTRACT, notes = ApiDesConst.API_MODIFY_CONTRACT)
    @PostMapping(value = AssetUrl.API_URL_MODIFY_CONTRACT)
    public Response<Void> modifyContract(@Valid @RequestBody ContractCreatePara createPara) throws ParameterException {
        AccountUserDTO user = UserUtil.get();
        createPara.setOperator(user.getUsername());
        contractService.modifyContract(createPara);
        return Response.success();
    }

    @ApiOperation(value = ApiDesConst.API_GET_CONTRACT, notes = ApiDesConst.API_GET_CONTRACT)
    @PostMapping(value = AssetUrl.API_URL_GET_CONTRACT)
    public Response<Contract> getContract(@RequestBody UuidPara para) throws ParameterException {
        Contract contract = contractService.findContractByUuid(para.getUuid());
        return Response.success(contract);
    }

    @ApiOperation(value = ApiDesConst.API_ASSET_LIST_CONTRACT, notes = ApiDesConst.API_ASSET_LIST_CONTRACT)
    @PostMapping(value = AssetUrl.API_URL_ASSET_LIST_CONTRACT)
    public Response<PageInfo<Contract>> queryAssetPackageContracts(@Valid @RequestBody QueryAssetContractPara param) {
        PageInfo<Contract> result = contractService.queryAssetPackageContracts(param);
        return Response.success(result);
    }


    @ApiOperation(value = ApiDesConst.API_SELECT_ASSET_CONTRACT, notes = ApiDesConst.API_SELECT_ASSET_CONTRACT)
    @PostMapping(value = AssetUrl.API_URL_SELECT_ASSET_CONTRACT)
    public Response<PageInfo<Contract>> selectAssetPackageContracts(@Valid @RequestBody AssetContractSelectPara param) {
        PageInfo<Contract> result = contractService.selectAssetPackageContracts(param);
        return Response.success(result);
    }

    @ApiOperation(value = ApiDesConst.API_OO_QUERY_CONTRACT_LIST, notes = ApiDesConst.API_OO_QUERY_CONTRACT_LIST)
    @PostMapping(value = AssetUrl.API_OO_QUERY_CONTRACT_LIST)
    public Response<PageInfo<Contract>> ooQueryContractList(@Valid @RequestBody OoQueryContractListPara ooQueryContractListPara) {
        return Response.success(contractService.ooQueryContractList(ooQueryContractListPara));
    }


    @ApiOperation(value = ApiDesConst.API_OO_EXPORT_CONTRACT_LIST, notes = ApiDesConst.API_OO_EXPORT_CONTRACT_LIST)
    @PostMapping(value = AssetUrl.API_OO_EXPORT_CONTRACT_LIST)
    public void ooExportContractList(@Valid @RequestBody OoQueryContractListPara ooQueryContractListPara,HttpServletResponse response) throws IOException, ParameterException {
        contractService.ooExportContractList(ooQueryContractListPara,response);
    }
}
