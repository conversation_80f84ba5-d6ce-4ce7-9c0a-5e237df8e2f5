package com.wxbc.rhine3.controller;

import com.wxbc.rhine3.bean.ChartsResult;
import com.wxbc.rhine3.bean.FinanceServiceFeeStatistical;
import com.wxbc.rhine3.bean.StatisticalCorePara;
import com.wxbc.rhine3.bean.StatisticalQueryPara;
import com.wxbc.rhine3.bean.response.StatisticalCoreResponse;
import com.wxbc.rhine3.bean.statistical.FinanceTotalStatistical;
import com.wxbc.rhine3.common.Response;
import com.wxbc.rhine3.constants.ApiDesConst;
import com.wxbc.rhine3.constants.url.AssetUrl;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.service.statistical.StatisticalAssetService;
import com.wxbc.rhine3.service.statistical.StatisticalCoreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@Validated
@Api(tags = "Asset模块图表统计相关")
public class StatisticalAssetController {

    @Autowired
    private StatisticalAssetService statisticalAssetService;

    @Autowired
    private StatisticalCoreService statisticalCoreService;

    @ApiOperation(value = ApiDesConst.API_URL_QUERY_FINANCE_STATISTICAL, notes = ApiDesConst.API_URL_QUERY_FINANCE_STATISTICAL)
    @GetMapping(value = AssetUrl.API_URL_QUERY_FINANCE_STATISTICAL)
    public Response<FinanceTotalStatistical> queryFinanceStatistical(){
        return Response.success(statisticalAssetService.queryFinanceStatistical());
    }

    @ApiOperation(value = ApiDesConst.API_URL_QUERY_FINANCE_FOR_CHART, notes = ApiDesConst.API_URL_QUERY_FINANCE_FOR_CHART)
    @PostMapping(value = AssetUrl.API_URL_QUERY_FINANCE_FOR_CHART)
    public Response<List<ChartsResult>> queryFinanceChart(@Valid  @RequestBody StatisticalQueryPara statisticalQueryPara) throws ParameterException {
        return Response.success(statisticalAssetService.queryFianceChartData(statisticalQueryPara));
    }

    @ApiOperation(value = ApiDesConst.API_QUERY_FINANCE_SERVICE_FEE_STATISTICAL, notes = ApiDesConst.API_QUERY_FINANCE_SERVICE_FEE_STATISTICAL)
    @GetMapping(value = AssetUrl.API_URL_QUERY_FINANCE_SERVICE_FEE_STATISTICAL)
    public Response<FinanceServiceFeeStatistical> queryFinanceServiceFeeTotalStatistical(){
        return Response.success(statisticalAssetService.queryFinanceServiceFeeStatistical());
    }


    @ApiOperation(value = ApiDesConst.API_QUERY_CORE_STATISTICAL, notes = ApiDesConst.API_QUERY_CORE_STATISTICAL)
    @PostMapping(value = AssetUrl.API_URL_QUERY_CORE_STATISTICAL)
    public Response<StatisticalCoreResponse> queryCoreStatistical(@RequestBody StatisticalCorePara para) throws ParameterException {
        return Response.success(statisticalCoreService.queryStatisticalCoreDetail(para));
    }
}
