package com.wxbc.rhine3.controller.financeproduct;

import com.wxbc.rhine3.bean.product.OrderFinanceProductDetailVo;
import com.wxbc.rhine3.service.FinanceProductService;
import com.wxbc.scaffold.common.definition.response.ResponseFormat;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Slf4j
@RestController
@Api(tags = "企业端金融产品管理")
public class CcFinanceProductController {


    @Autowired
    private FinanceProductService financeProductService;

    @ApiOperation(value = "企业端金融产品列表")
    @GetMapping(value = "/cc/finance/product/list")
    public ResponseFormat<List<OrderFinanceProductDetailVo>> queryOrderFinanceProductList() {
        return ResponseFormat.success(financeProductService.queryOrderFinanceProductList());
    }

}
