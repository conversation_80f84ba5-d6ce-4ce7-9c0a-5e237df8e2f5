package com.wxbc.rhine3.controller.finance;

import com.wxbc.rhine3.bean.finance.FinanceCreatePara;
import com.wxbc.rhine3.bean.finance.FinanceDetailRequest;
import com.wxbc.rhine3.bean.response.FinanceCreateResponse;
import com.wxbc.rhine3.common.Response;
import com.wxbc.rhine3.constants.ApiDesConst;
import com.wxbc.rhine3.constants.url.AssetUrl;
import com.wxbc.rhine3.exception.BankBusinessException;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.service.finance.FinanceCreateService;
import com.wxbc.rhine3.service.workflow.financing.submit2bank.FinanceSubmitToBank;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.IOException;

@RestController
@Validated
@Api(tags = "融资操作接口")
public class FinanceController {
    @Autowired
    private FinanceSubmitToBank financeSubmitToBank;

    @Autowired
    private FinanceCreateService financeCreateService;

    @ApiOperation(value = ApiDesConst.API_CREATE_FINANCE_REQ, notes = ApiDesConst.API_CREATE_FINANCE_REQ)
    @PostMapping(value = AssetUrl.API_URL_CREATE_FINANCE)
    public Response<FinanceCreateResponse> createFinanceReq(@Valid @RequestBody FinanceCreatePara frPara) throws ParameterException, IOException {
        //financeFromRole是支付发起方的角色类型:0-普通操作员 1-限额操作员 2-项目操作员
        return Response.success(financeCreateService.createFinanceReq(frPara, "0"));
    }

    @ApiOperation(value = ApiDesConst.API_CREATE_FINANCE_REQ_FOR_BUILD, notes = ApiDesConst.API_CREATE_FINANCE_REQ_FOR_BUILD)
    @PostMapping(value = AssetUrl.API_URL_CREATE_FINANCE_FOR_BUILD)
    public Response<FinanceCreateResponse> createFinanceReqForBuild(@Valid @RequestBody FinanceCreatePara frPara) throws ParameterException, IOException {
        //financeFromRole是支付发起方的角色类型:0-普通操作员 1-限额操作员 2-项目操作员
        return Response.success(financeCreateService.createFinanceReq(frPara, "1"));
    }

    @ApiOperation(value = ApiDesConst.API_CREATE_FINANCE_REQ_FOR_TEAM, notes = ApiDesConst.API_CREATE_FINANCE_REQ_FOR_TEAM)
    @PostMapping(value = AssetUrl.API_URL_CREATE_FINANCE_FOR_TEAM)
    public Response<FinanceCreateResponse> createFinanceReqForTeam(@Valid @RequestBody FinanceCreatePara frPara) throws ParameterException, IOException {
        //financeFromRole是支付发起方的角色类型:0-普通操作员 1-限额操作员 2-项目操作员
        return Response.success(financeCreateService.createFinanceReq(frPara, "2"));
    }

    @ApiOperation(value = ApiDesConst.API_FINANCE_SECOND_SUBMIT, notes = ApiDesConst.API_FINANCE_SECOND_SUBMIT)
    @PostMapping(value = AssetUrl.API_FINANCE_SECOND_SUBMIT)
    public Response<Void> secondFinanceSubmit(@Valid @RequestBody FinanceDetailRequest para) throws ParameterException, BankBusinessException {
        financeSubmitToBank.financeSubmitToBank(para.getUuidOrNumber(), null);
        return Response.success();
    }


    //todo pledge-融资流程：银行协议签署完成后回调我方接口，我方代码触发工作流节点【SignEbankProtocol】任务。
    /** 1、手动调用 com.wxbc.workflow.service.WorkFlowService.completeTask(doUserTaskDto);方法。
     *
     */


    /**
     * 2、工作流节点 【SignEbankProtocol】的flow_audit 配置为 role_name = SOperator, audit_param = fromCompanyUuid，前端页面【待签署协议】可查询process/task/loadCurrentUserTask接口，roleNameList参数为SOperator。
     *      取消融资 入参：outcome=reject。
     *
     */

}
