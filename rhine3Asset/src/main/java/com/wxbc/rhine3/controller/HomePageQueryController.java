package com.wxbc.rhine3.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wxbc.rhine3.bean.HomeStatisticsInfo;
import com.wxbc.rhine3.common.Response;
import com.wxbc.rhine3.constants.ApiDesConst;
import com.wxbc.rhine3.constants.url.AssetUrl;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.service.QueryHomePageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Validated
@RequiredArgsConstructor
@Api(tags = "首页查询接口")
public class HomePageQueryController {

    @Autowired
    private QueryHomePageService queryHomePageService;

    @ApiOperation(value = ApiDesConst.QUERY_STATISTICS_INFO, notes = ApiDesConst.QUERY_STATISTICS_INFO)
    @PostMapping(value = AssetUrl.API_URL_QUERY_STATISTICS)
    public Response<HomeStatisticsInfo> queryStatisticsInfo() throws ParameterException, JsonProcessingException {
        return Response.success(queryHomePageService.queryStatisticsInfo());
    }
}
