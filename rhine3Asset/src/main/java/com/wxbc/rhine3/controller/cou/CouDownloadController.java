package com.wxbc.rhine3.controller.cou;

import com.github.pagehelper.PageInfo;
import com.wxbc.cou.manager.api.constant.CouCashStatus;
import com.wxbc.rhine3.bean.UuidListPara;
import com.wxbc.rhine3.bean.cou.CouExport;
import com.wxbc.rhine3.bean.cou.ExportMyCouListRequest;
import com.wxbc.rhine3.bean.cou.OriginalCouQueryPara;
import com.wxbc.rhine3.bean.cou.SpOriginalCouQueryPara;
import com.wxbc.rhine3.bean.response.CouBillExt;
import com.wxbc.rhine3.common.Response;
import com.wxbc.rhine3.constants.ApiDesConst;
import com.wxbc.rhine3.constants.CommonConst;
import com.wxbc.rhine3.constants.url.AssetUrl;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.service.cou.CouQueryService;
import com.wxbc.rhine3.util.FileUtil;
import com.wxbc.rhine3.util.NumberFormatUtil;
import com.wxbc.rhine3.util.TimeUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.utils.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@Validated
@Api(tags = "融信下载相关接口")
@Slf4j
public class CouDownloadController {
    @Autowired
    private CouQueryService couQueryService;

    private void setResponseHeader(HttpServletResponse response, String name) {
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + name);
    }

    private String transferAmount(BigDecimal couAmountInCent) {
        return NumberFormatUtil.thousandsFormatAnd2f(couAmountInCent.divide(CommonConst.HUNDRED, RoundingMode.HALF_UP));
    }

    private String transferDueDateStr(Date dueDate) {
        return DateUtils.formatDate(TimeUtils.addDay(dueDate, 1), TimeUtils.DATA_FORMAT_YYYY_MM_DD);
    }

    private String transferCreateTimeStr(Date createTime) {
        return DateUtils.formatDate(createTime, TimeUtils.DATA_FORMAT_YYYY_MM_DD);
    }

    @ApiOperation(value = ApiDesConst.API_URL_SP_ORIGINAL_LIST2, notes = ApiDesConst.API_URL_SP_ORIGINAL_LIST2)
    @PostMapping(value = AssetUrl.API_URL_GET_C_ORIGINAL_COU_EXPORT)
    public void exportOriginalCouList(@Valid @RequestBody SpOriginalCouQueryPara para, HttpServletResponse response) throws ParameterException, IOException {
        PageInfo<CouBillExt> result = couQueryService.queryAllOriginalCouList(para);

        String name = new String(("兑付列表导出-" + LocalDateTime.now() + ".xls").getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1.name());
        setResponseHeader(response, name);
        List<CouBillExt> bills = result.getList();
        List<CouExport> billList = null;

        if (!CollectionUtils.isEmpty(bills)) {
            billList = bills.stream().map(bill -> {
                CouExport couBill = new CouExport();
                BeanUtils.copyProperties(bill, couBill);
                if (bill.getCouCreateTime() != null) {
                    couBill.setReceiveTime(DateUtils.formatDate(bill.getCouCreateTime(), TimeUtils.DATA_FORMAT_YYYY_MM_DD));
                }
                couBill.setCouAmountInYuan(transferAmount(bill.getCouAmountInCent()));
                couBill.setCreateTimeStr(transferCreateTimeStr(bill.getCreateTime()));
                couBill.setDueDateStr(transferDueDateStr(bill.getDueDate()));
                couBill.setCashStatus(CouCashStatus.getDescByName(couBill.getCashStatus()));
                return couBill;
            }).collect(Collectors.toList());
        }
        FileUtil.downloadExcel(billList, response);

    }


    @ApiOperation(value = ApiDesConst.API_URL_ORIGINAL_LIST_EXPORT, notes = ApiDesConst.API_URL_ORIGINAL_LIST_EXPORT)
    @PostMapping(value = AssetUrl.API_URL_GET_ORIGINAL_COU_EXPORT)
    public void exportOriginalCouList(@Valid @RequestBody OriginalCouQueryPara para, HttpServletResponse response) throws ParameterException, IOException {
        couQueryService.exportOriginalCouList(para, response);
    }

    @ApiOperation(value = ApiDesConst.API_GET_COU_LIST_TRANSFER_PATH,
            notes = ApiDesConst.API_GET_COU_LIST_TRANSFER_PATH)
    @PostMapping(value = AssetUrl.API_URL_GET_COU_LIST_TRANSFER_PATH_EXCEL)
    public Response<Map<String,String>> getCouListTransferPathExcel(@Valid @RequestBody UuidListPara para)
            throws ParameterException, IOException {
        return Response.success(couQueryService.downloadCouList(para));
    }

    @ApiOperation(value = ApiDesConst.COU_EXPORT_LIST, notes = ApiDesConst.COU_EXPORT_LIST)
    @PostMapping(AssetUrl.API_URL_COU_EXPORT_LIST)
    public void exportMyCouList(@RequestBody @Valid ExportMyCouListRequest request,
                              HttpServletResponse response) throws ParameterException, IOException {
        couQueryService.exportMyCouList(request, response);
    }

}
