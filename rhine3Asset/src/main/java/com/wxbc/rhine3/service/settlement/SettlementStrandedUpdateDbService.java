package com.wxbc.rhine3.service.settlement;

import com.wxbc.rhine3.bean.settlement.SettlementStranded;
import com.wxbc.rhine3.exception.UpdateFailedException;
import com.wxbc.rhine3.repository.mapper.SettlementStrandedModifyDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import static com.wxbc.rhine3.constants.ExceptionBaseConstants.BASE_DUPLICATE_KEY_ERROR;
import static com.wxbc.rhine3.constants.ExceptionBaseConstants.PARA_VALIDATE_FAILED;

/**
 * User: yanhengfu
 * Date: 2022/11/18
 * Time: 10:38
 * Description: des
 */
@Slf4j
@Service
public class SettlementStrandedUpdateDbService {

    @Autowired
    private SettlementStrandedModifyDao settlementStrandedModifyDao;


    public void insertSettlementStranded(SettlementStranded settlementStranded){
        try{
            settlementStrandedModifyDao.insertSettlementStranded(settlementStranded);
         } catch (DuplicateKeyException duplicateKeyException) {
            log.error("insertSettlementStranded error ! duplicateKeyException ! settlementStranded = {}", settlementStranded, duplicateKeyException);
            throw new UpdateFailedException(BASE_DUPLICATE_KEY_ERROR);
        } catch (Exception e){
            log.error("insertSettlementStranded error ! settlementStranded = {}", settlementStranded, e);
            throw new UpdateFailedException(PARA_VALIDATE_FAILED);
        }
    }
    public void insertSettlementStrandedHistory(SettlementStranded settlementStranded){
        try{
            settlementStrandedModifyDao.insertSettlementStrandedHistory(settlementStranded);
        } catch (DuplicateKeyException duplicateKeyException) {
            log.error("insertSettlementStrandedHistory error ! duplicateKeyException ! " +
                    "settlementStranded = {}", settlementStranded, duplicateKeyException);
            throw new UpdateFailedException(BASE_DUPLICATE_KEY_ERROR);
        } catch (Exception e){
            log.error("insertSettlementStrandedHistory error ! settlementStranded = {}",
                    settlementStranded, e);
            throw new UpdateFailedException(PARA_VALIDATE_FAILED);
        }
    }
    public void updateStatusAndSettleNoByUuidId(SettlementStranded settlementStranded){
        int rs = settlementStrandedModifyDao.updateStatusAndSettleNoByUuidId(settlementStranded);
        if(rs < 1){
            log.error("updateStatusAndSettleNoByUuidId error ! settlementStranded = {},rs={}", settlementStranded, rs);
            throw new UpdateFailedException(BASE_DUPLICATE_KEY_ERROR);
        }

    }

    public void deleteByUuid(String uuid){
        int rs = settlementStrandedModifyDao.deleteByUuid(uuid);
        if(rs < 1){
            log.error("deleteByUuid error ! uuid = {},rs={}", uuid, rs);
            throw new UpdateFailedException(BASE_DUPLICATE_KEY_ERROR);
        }
    }





}