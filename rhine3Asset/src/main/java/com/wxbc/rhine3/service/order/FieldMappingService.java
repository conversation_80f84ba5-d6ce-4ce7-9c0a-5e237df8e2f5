package com.wxbc.rhine3.service.order;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.wxbc.base.util.BeanUtil;
import com.wxbc.rhine3.common.constant.EntityType;
import com.wxbc.rhine3.common.constant.MappingWay;
import com.wxbc.rhine3.model.order.dto.MappingDTO;
import com.wxbc.rhine3.model.order.dto.MappingItemDTO;
import com.wxbc.rhine3.model.order.vo.req.FieldMappingAddReq;
import com.wxbc.rhine3.model.order.vo.req.FieldMappingItemAddReq;
import com.wxbc.rhine3.model.order.vo.req.FieldMappingQueryReq;
import com.wxbc.rhine3.model.order.vo.res.FieldMappingListRes;
import com.wxbc.rhine3.repository.entity.FieldMappingEntity;
import com.wxbc.rhine3.repository.mapper.FieldMappingDao;
import com.wxbc.rhine3.repository.table.FieldMapping;
import com.wxbc.rhine3.utils.PageUtil;
import com.wxbc.scaffold.common.definition.enums.CommonStatus;
import com.wxbc.scaffold.common.definition.exception.BizException;
import com.wxbc.scaffold.common.definition.response.ReturnCode;
import com.wxbc.wave.WaveId;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * financing-service
 *
 * <AUTHOR> chen cheng
 * Date         : 2022/11/23 14:50
 * Jdk          : 1.8
 * Description  :
 */
@Service
@Slf4j
public class FieldMappingService {

    @Resource
    private FieldMappingDao dao;

    @Resource
    private WaveId waveId;

    @Resource
    private FieldMappingItemService itemService;


    public boolean addFieldMapping(FieldMappingAddReq req, Long accountId) {
        FieldMappingEntity entity = new FieldMappingEntity();
        BeanUtil.copyProperties(req, entity);
        Long mappingId = waveId.nextId();
        entity.setId(mappingId);
        entity.setCreatorId(accountId);
        dao.insert(entity);
        itemService.addMappingItem(mappingId, req.getFieldList());
        return true;
    }

    /**
     * 添加映射明细配置
     * @param req item info
     * @return true - success
     */
    public boolean addFieldMappingItem(FieldMappingItemAddReq req) {
        final FieldMappingEntity entity = dao.selectById(req.getMappingId());
        if (entity == null) {
            log.error("add field mapping item fail, mapping with id:{} not exist", req.getMappingId());
            throw new BizException(ReturnCode.UNI_PARAMETER_FAILED);
        }
        itemService.addMappingItem(req.getMappingId(), req.getFieldList());
        return true;
    }

    public PageInfo<FieldMappingListRes> queryMapping(FieldMappingQueryReq req) {
        QueryWrapper<FieldMappingEntity> query = new QueryWrapper<>();
        query.eq(req.getEntityType() != null, FieldMapping.ENTITY_TYPE, req.getEntityType());
        final IPage<FieldMappingEntity> queryPage = PageUtil.requestToIPage(req);
        final IPage<FieldMappingEntity> retPage = dao.selectPage(queryPage, query);
        return PageUtil.page2Response(retPage, FieldMappingListRes::new);
    }


    /**
     * 查询订单
     * @param companyId 所属公司id
     * @param tenantId  租户
     * @return List
     */
    public MappingDTO queryOrderMapping(String companyId, String tenantId, MappingWay way) {
        return findMatchOne(companyId, tenantId, EntityType.ORDER, way);
    }

    public MappingDTO queryFinanceMapping(String companyId, String tenantId, MappingWay way) {
        return findMatchOne(companyId, tenantId, EntityType.FINANCE, way);
    }

    private MappingDTO findMatchOne(String companyId, String tenantId, EntityType entityType, MappingWay way) {
        final List<MappingDTO> dtoList = queryMapping(companyId, tenantId, entityType, way);
        if (CollectionUtils.isEmpty(dtoList)) {
            return null;
        }
        Stream<MappingDTO> stream = dtoList.stream();
        if (companyId != null) {
            stream = stream.filter(dto -> companyId.equals(dto.getCompanyId()));
        }

        final Optional<MappingDTO> ret = stream.findFirst();
        if(ret.isPresent()) {
            final MappingDTO mappingDTO = ret.get();
            final List<MappingItemDTO> itemList = itemService.queryMappingItem(mappingDTO.getId());
            mappingDTO.setItemList(itemList);
            return mappingDTO;
        }
        return null;
    }

    private List<MappingDTO> queryMapping(String companyId, String tenantId,
                                          EntityType entityType,
                                          MappingWay way) {
        log.debug("query meta by:{}, {}, {}", companyId, tenantId, entityType);
        QueryWrapper<FieldMappingEntity> query = new QueryWrapper<>();
        query.eq(FieldMapping.ENTITY_TYPE, entityType.value())
                .eq(FieldMapping.STATUS, CommonStatus.NORMAL.value())
                .eq(FieldMapping.MAPPING_WAY, way.value())
                .and(q -> q.eq(companyId != null, FieldMapping.COMPANY_ID, companyId)
                        .eq(FieldMapping.TENANT_ID, tenantId == null ? 0 : tenantId));



        final List<FieldMappingEntity> entityList = dao.selectList(query);
        if (!CollectionUtils.isEmpty(entityList)) {
            final List<Long> idList = entityList.stream().map(FieldMappingEntity::getId).collect(Collectors.toList());
            final Map<Long, List<MappingItemDTO>> detailMap = itemService.queryMappingItem(idList);
            return BeanUtil.copyList(entityList, () -> {
                MappingDTO dto = new MappingDTO();
                dto.setItemList(detailMap.get(dto.getId()));
                return dto;
            });
        }
        return Lists.newArrayList();
    }
}
