package com.wxbc.rhine3.service;

import com.github.pagehelper.PageInfo;
import com.wxbc.base.util.JsonUtil;
import com.wxbc.base.util.StringUtil;
import com.wxbc.cou.manager.api.bean.AvailableLimitMoneyPara;
import com.wxbc.cou.manager.api.bean.CouBean;
import com.wxbc.cou.manager.api.bean.CouBill;
import com.wxbc.cou.manager.api.bean.CouFirsthandVo;
import com.wxbc.cou.manager.api.bean.CouParentPath;
import com.wxbc.cou.manager.api.bean.CouStatistics;
import com.wxbc.cou.manager.api.bean.CouTransferPath;
import com.wxbc.cou.manager.api.bean.CountCouByDueDateVO;
import com.wxbc.cou.manager.api.bean.LeafCou;
import com.wxbc.cou.manager.api.bean.LeafCouList;
import com.wxbc.cou.manager.api.bean.PreTransferCouRes;
import com.wxbc.cou.manager.api.bean.QueryCouPayAndCreatePara;
import com.wxbc.cou.manager.api.bean.Statistical;
import com.wxbc.cou.manager.api.bean.StatisticalBalance;
import com.wxbc.cou.manager.api.bean.StatisticalBalancePara;
import com.wxbc.cou.manager.api.bean.StatisticsInfo;
import com.wxbc.cou.manager.api.bean.TradeVerifiable;
import com.wxbc.cou.manager.api.bean.TransferCountGroupByStatusDTO;
import com.wxbc.cou.manager.api.constant.CouCashStatus;
import com.wxbc.cou.manager.api.constant.CouStatus;
import com.wxbc.cou.manager.api.constant.url.ApiUrl;
import com.wxbc.cou.manager.api.domain.Cou;
import com.wxbc.cou.manager.api.domain.Transfer;
import com.wxbc.cou.manager.api.requestPara.BatchUpdateCashStatusByUuidsPara;
import com.wxbc.cou.manager.api.requestPara.Complete4WithoutAuditDeferPara;
import com.wxbc.cou.manager.api.requestPara.CouPara;
import com.wxbc.cou.manager.api.requestPara.CouStatusChangeByCheckPara;
import com.wxbc.cou.manager.api.requestPara.CountCouAmountPara;
import com.wxbc.cou.manager.api.requestPara.CountCouByDueDatePara;
import com.wxbc.cou.manager.api.requestPara.CountCouCreatePara;
import com.wxbc.cou.manager.api.requestPara.CountTransferGroupByStatus;
import com.wxbc.cou.manager.api.requestPara.CreateApplyPara;
import com.wxbc.cou.manager.api.requestPara.CreditAndPublishQueryPara;
import com.wxbc.cou.manager.api.requestPara.DepositPara;
import com.wxbc.cou.manager.api.requestPara.DestroyCouByOneSignPara;
import com.wxbc.cou.manager.api.requestPara.DestroySameCenterCouPara;
import com.wxbc.cou.manager.api.requestPara.GetLastChainByRelationUuidPara;
import com.wxbc.cou.manager.api.requestPara.GetLastChainByTransferUuidPara;
import com.wxbc.cou.manager.api.requestPara.QueryAllOriginalCouPara;
import com.wxbc.cou.manager.api.requestPara.QueryCouAmountStatisticsPara;
import com.wxbc.cou.manager.api.requestPara.QueryCouCashStatsDataPara;
import com.wxbc.cou.manager.api.requestPara.QueryCouCashStatusPara;
import com.wxbc.cou.manager.api.requestPara.QueryCouDetailsByUuidPara;
import com.wxbc.cou.manager.api.requestPara.QueryCouListByNoPara;
import com.wxbc.cou.manager.api.requestPara.QueryCouListByTransferUuidPara;
import com.wxbc.cou.manager.api.requestPara.QueryCouListByUuidsPara;
import com.wxbc.cou.manager.api.requestPara.QueryCouListPara;
import com.wxbc.cou.manager.api.requestPara.QueryCouListTransferPathPara;
import com.wxbc.cou.manager.api.requestPara.QueryCouNoPara;
import com.wxbc.cou.manager.api.requestPara.QueryHolderCouPara;
import com.wxbc.cou.manager.api.requestPara.QueryHolderCouStatisticsPara;
import com.wxbc.cou.manager.api.requestPara.QueryOriginalCouCreateStatsDataPara;
import com.wxbc.cou.manager.api.requestPara.QueryOriginalCouDetailPara;
import com.wxbc.cou.manager.api.requestPara.QueryOriginalCouPara;
import com.wxbc.cou.manager.api.requestPara.QueryOriginalCouRelPara;
import com.wxbc.cou.manager.api.requestPara.QueryOriginalCouStatsDataPara;
import com.wxbc.cou.manager.api.requestPara.QueryPageCouLeaves;
import com.wxbc.cou.manager.api.requestPara.QueryTransferDetailPara;
import com.wxbc.cou.manager.api.requestPara.QueryTransferFromListPara;
import com.wxbc.cou.manager.api.requestPara.QueryTransferListPara;
import com.wxbc.cou.manager.api.requestPara.QueryTransferSumByAcceptOrPayPara;
import com.wxbc.cou.manager.api.requestPara.QueryTransferToListPara;
import com.wxbc.cou.manager.api.requestPara.TeamTransferStatisticsPara;
import com.wxbc.cou.manager.api.requestPara.TransferCouPara;
import com.wxbc.cou.manager.api.requestPara.TransferUpdateAssetUuidsPara;
import com.wxbc.cou.manager.api.requestPara.UpdateCouCashStatusPara;
import com.wxbc.cou.manager.api.requestPara.UpdateTransferToVisibleFlagPara;
import com.wxbc.cou.manager.api.requestPara.UserUuidsPara;
import com.wxbc.cou.manager.api.requestPara.UuidsBean;
import com.wxbc.cou.manager.api.response.ChainTransferResponse;
import com.wxbc.cou.manager.api.response.CoreCompanyCouDueStatisticalResp;
import com.wxbc.cou.manager.api.response.CouBillCreateStatsResponse;
import com.wxbc.cou.manager.api.response.CouCreditPublishListResponse;
import com.wxbc.cou.manager.api.response.CouDetailResponse;
import com.wxbc.cou.manager.api.response.CreateCouAmountStatisticVo;
import com.wxbc.cou.manager.api.response.CreateCouStatisticVo;
import com.wxbc.cou.manager.api.response.HolderCouStatisticVO;
import com.wxbc.cou.manager.api.response.PayCouAmountStatisticVo;
import com.wxbc.cou.manager.api.response.QueryCouCashStatsDataResponse;
import com.wxbc.cou.manager.api.response.TeamTransferStatisticsRes;
import com.wxbc.cou.manager.api.response.TransferResponse;
import com.wxbc.cou.manager.api.service.CouQueryApiService;
import com.wxbc.cou.manager.api.service.CouTransferApiService;
import com.wxbc.rhine3.bean.CreateCouRequest;
import com.wxbc.rhine3.bean.CreateCouStatisticsPara;
import com.wxbc.rhine3.bean.FinanceBusinessConfig;
import com.wxbc.rhine3.bean.HolderCouQueryPara;
import com.wxbc.rhine3.bean.HolderCouStatisticsPara;
import com.wxbc.rhine3.bean.QuotaStatisticsInfo;
import com.wxbc.rhine3.bean.ThirdInvokeLogEntity;
import com.wxbc.rhine3.bean.cou.OriginalCouQueryPara;
import com.wxbc.rhine3.bean.cou.SpOriginalCouQueryPara;
import com.wxbc.rhine3.bean.destroy.DestroyQueryCouLeaves;
import com.wxbc.rhine3.bean.refund.Refund;
import com.wxbc.rhine3.common.lob.CompanyType;
import com.wxbc.rhine3.constants.LockCouSetConst;
import com.wxbc.rhine3.constants.ReturnCode;
import com.wxbc.rhine3.constants.ThirdInvokeLogConstants;
import com.wxbc.rhine3.constants.TransferAssetConst;
import com.wxbc.rhine3.convert.CouLeavesConvert;
import com.wxbc.rhine3.exception.BusinessException;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.repository.mapper.RefundQueryDao;
import com.wxbc.rhine3.service.team.TeamUserService;
import com.wxbc.rhine3.util.TimeUtils;
import com.wxbc.rhine3.utils.TradeVerifiableFactory;
import com.wxbc.venus.user.dto.AccountUserDTO;
import com.wxbc.venus.user.dto.OrgExtDTO;
import com.wxbc.venus.user.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

import static com.wxbc.rhine3.constants.CommonConst.PROJECT;
import static com.wxbc.rhine3.constants.ExceptionConstants.COU_CREATE_FAIL;

//直接对接sarah
@Slf4j
@Service
public class SarahService {

    @Autowired
    private CouTransferApiService couTransferApiService;

    @Autowired
    private CouQueryApiService couQueryApiService;

    @Autowired
    private ThirdInvokeLogService thirdInvokeLogService;

    @Autowired
    private TeamUserService teamUserService;

    @Autowired
    private RefundQueryDao refundQueryDao;

    public void depositSetData(String key, String value, String accessKey) throws ParameterException {
        DepositPara para = new DepositPara();
        para.setProject(PROJECT);
        para.setKey(key);
        para.setValue(value);
        para.setAccessKey(accessKey);

        try {
            couTransferApiService.depositSet(para);
        } catch (Exception e) {
            log.error("depositSetData", e);
            throw new ParameterException(e.getMessage());
        }
    }


    public PageInfo<Cou> queryPageCouLeaves(DestroyQueryCouLeaves para, String publishPubKey) throws ParameterException {
        QueryPageCouLeaves queryPageCouLeaves = CouLeavesConvert.instance.source2target(para, publishPubKey, PROJECT);
        try {
            return couQueryApiService.queryPageCouLeaves(queryPageCouLeaves);
        } catch (Exception e) {
            log.error("queryPageCouLeaves", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public LeafCouList queryLeafCouList(QueryOriginalCouDetailPara para) throws ParameterException {
        para.setProject(PROJECT);
        try {
            return couQueryApiService.queryLeafCouList(para);
        } catch (Exception e) {
            log.error("queryLeafCouList", e);
            throw new ParameterException(e.getMessage());
        }
    }


    public TransferResponse createCouApply(CreateCouRequest request, AccountUserDTO currUser,
                                           String toPubKey, FinanceBusinessConfig fiConfig) throws ParameterException {
        CreateApplyPara couPara = new CreateApplyPara();
        couPara.setToPubKey(toPubKey);

        couPara.setOperatingOrganizationUuid(StringUtil.valueOf(currUser.getCompany().getOperatingOrgId()));
        couPara.setTradeVerifiable(TradeVerifiableFactory.genCreate(currUser.getCompany(),
                toPubKey, request.getCreateBase().getCreateCouAmountInCent().longValue()));

        CouBean couBean = new CouBean();
        couBean.setCouAmountInCent(request.getCreateBase().getCreateCouAmountInCent());
        couBean.setDueDate(TimeUtils.getFormatTime(request.getCreateBase().getDueDate(),
                TimeUtils.DATA_FORMAT_YYYY_MM_DD));
        couBean.setCouNo(request.getCouNo());
        couBean.setCreditPubKey(request.getCreditorPubKey());
        couPara.setCouBean(couBean);

        couPara.setToChainDefer(true);
        couPara.setAssetType(TransferAssetConst.CONTRACT.name());
       /* List<String> invoiceUuIds=request.getInvoiceList().stream().map(Invoice::getUuid).collect(Collectors.toList());
        invoiceUuIds.add(request.getContractUuid());*/
        //待确定这一块是否需要放关联的发票uuid,暂时和原来保持一致怕导致其他问题产生,因为关联的发票uuid关联表可以查询
        couPara.setAssetUuids(request.getContractUuid());
        couPara.setOperateUuid(StringUtil.valueOf(currUser.getAccountId()));
        couPara.setTeamNo(currUser.getTeamNo());

        couPara.setAccessKey(fiConfig.getChainAccessKey());

        couPara.setProject(PROJECT);

        TransferResponse response = null;
        ThirdInvokeLogEntity thirdInvokeLogEntity = thirdInvokeLogService.generalThirdInvoiceLogEntity(ApiUrl.TRANSFER_APPLY_WITHOUT_AUDIT,
                ThirdInvokeLogConstants.COU_MANAGER, couPara.getTransferNo(), JsonUtil.object2String(couPara));
        try {
            response = couTransferApiService.createCouWithoutAudit(couPara);
            thirdInvokeLogService.fillReturnInfo(JsonUtil.object2String(response), StringUtil.valueOf(ReturnCode.REQUEST_SUCCESS.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
        } catch (Exception e) {
            log.error("", e);
            thirdInvokeLogService.fillReturnInfo(e.getMessage(), StringUtil.valueOf(ReturnCode.REQUEST_FAILED.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
            throw new ParameterException(e.getMessage());
        }

        if (CollectionUtils.isEmpty(response.getCouList()) || 1 != response.getCouList().size()) {
            throw new ParameterException(COU_CREATE_FAIL);
        }

        return response;
    }

    public TransferResponse transferCouApply(TransferCouPara para, FinanceBusinessConfig fiConfig) throws ParameterException {
        para.setAccessKey(fiConfig.getChainAccessKey());

        para.setToChainDefer(true);
        para.setProject(PROJECT);
        ThirdInvokeLogEntity thirdInvokeLogEntity = thirdInvokeLogService.generalThirdInvoiceLogEntity(ApiUrl.TRANSFER_APPLY_WITHOUT_AUDIT,
                ThirdInvokeLogConstants.COU_MANAGER, para.getTransferNo(), JsonUtil.object2String(para));
        try {
            TransferResponse transferResponse = couTransferApiService.transferCouWithoutAudit(para);
            thirdInvokeLogService.fillReturnInfo(JsonUtil.object2String(transferResponse), StringUtil.valueOf(ReturnCode.REQUEST_SUCCESS.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
            return transferResponse;
        } catch (Exception e) {
            log.error("", e);
            thirdInvokeLogService.fillReturnInfo(e.getMessage(), StringUtil.valueOf(ReturnCode.REQUEST_FAILED.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
            throw new ParameterException(e.getMessage());
        }
    }

    public void destroySameCenterCou(List<String> couUuidList, String reFundUuid,
                                     OrgExtDTO signer, String issuePubKey, BigDecimal reFundAmountInCent,
                                     FinanceBusinessConfig fiConfig) throws ParameterException {
        DestroySameCenterCouPara para = new DestroySameCenterCouPara();
        para.setCouUuidList(new HashSet<>(couUuidList));
        para.setRelationUuid(reFundUuid);
        para.setTradeVerifiable(TradeVerifiableFactory
                .genTransfer(signer, issuePubKey, reFundAmountInCent.longValue()));

        para.setAccessKey(fiConfig.getChainAccessKey());
        para.setProject(PROJECT);
        ThirdInvokeLogEntity thirdInvokeLogEntity = thirdInvokeLogService.generalThirdInvoiceLogEntity(ApiUrl.DESTROY_SAME_CENTER_COU,
                ThirdInvokeLogConstants.COU_MANAGER, para.getRelationUuid(), JsonUtil.object2String(para));

        try {
            couTransferApiService.destroySameCenterCou(para);
            thirdInvokeLogService.fillReturnInfo(null, StringUtil.valueOf(ReturnCode.REQUEST_SUCCESS.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
        } catch (Exception e) {
            log.error("", e);
            thirdInvokeLogService.fillReturnInfo(e.getMessage(), StringUtil.valueOf(ReturnCode.REQUEST_FAILED.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
            throw new ParameterException(e.getMessage());
        }
    }

    public void transferUpdateAssetUuids(String assetUuids, String transferUuid) throws ParameterException {
        TransferUpdateAssetUuidsPara para = new TransferUpdateAssetUuidsPara();
        para.setProject(PROJECT);
        para.setAssetUuids(assetUuids);
        para.setTransferUuid(transferUuid);
        try {
            couTransferApiService.transferUpdateAssetUuids(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public void auditCou(String transferUuid, String toPubKey, boolean isAgree, boolean isCreate) throws ParameterException {
        Complete4WithoutAuditDeferPara para = new Complete4WithoutAuditDeferPara();
        para.setProject(PROJECT);
        para.setAgree(isAgree);
        para.setTransferUuid(transferUuid);
        para.setToPubKey(toPubKey);
        ThirdInvokeLogEntity thirdInvokeLogEntity = thirdInvokeLogService.generalThirdInvoiceLogEntity(null,
                ThirdInvokeLogConstants.COU_MANAGER, para.getTransferUuid(), JsonUtil.object2String(para));
        try {
            if (isCreate) {
                thirdInvokeLogEntity.setApiUrl(ApiUrl.CREATE_COMPLETE_BY_DEFER);
                couTransferApiService.createComplete4WithoutAuditInDeferCase(para);

            } else {
                thirdInvokeLogEntity.setApiUrl(ApiUrl.TRANSFER_COMPLETE_4WITHOUT_AUDIT_IN_DEFER_CASE);
                couTransferApiService.transferComplete4WithoutAuditInDeferCase(para);
            }
            thirdInvokeLogService.fillReturnInfo(null, StringUtil.valueOf(ReturnCode.REQUEST_SUCCESS.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
        } catch (Exception e) {
            log.error("auditCou,transferUuid= {},toPubKey={},isAgree={},isCreate={}",
                    transferUuid, toPubKey, isAgree, isCreate, e);
            thirdInvokeLogService.fillReturnInfo(e.getMessage(), StringUtil.valueOf(ReturnCode.REQUEST_FAILED.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
            throw new ParameterException(e.getMessage());
        }

    }

    public void batchUpdateCashStatusByUuids(List<String> couUuids, CouCashStatus couCashStatus) throws ParameterException {
        BatchUpdateCashStatusByUuidsPara para = new BatchUpdateCashStatusByUuidsPara();
        para.setCouUuids(couUuids);
        para.setStatus(couCashStatus.name());
        para.setProject(PROJECT);
        ThirdInvokeLogEntity thirdInvokeLogEntity = thirdInvokeLogService.generalThirdInvoiceLogEntity(ApiUrl.BATCH_UPDATE_CASH_STATUS_BY_UUIDS,
                ThirdInvokeLogConstants.COU_MANAGER, null, JsonUtil.object2String(para));
        try {
            couTransferApiService.batchUpdateCashStatusByUuids(para);
            thirdInvokeLogService.fillReturnInfo(null, StringUtil.valueOf(ReturnCode.REQUEST_SUCCESS.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
        } catch (Exception e) {
            log.error("", e);
            thirdInvokeLogService.fillReturnInfo(e.getMessage(), StringUtil.valueOf(ReturnCode.REQUEST_FAILED.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
            throw new ParameterException(e.getMessage());
        }
    }

    public List<Cou> getLockCouList(int scanCouDaySet) throws ParameterException {
        UpdateCouCashStatusPara lockCouPara = new UpdateCouCashStatusPara();

        lockCouPara.setProject(PROJECT);
        lockCouPara.setScanCouDaySet(scanCouDaySet);
        lockCouPara.setStatus(LockCouSetConst.TARGET_CASH_STATUS);
        lockCouPara.setStatusList(Collections.singletonList(CouCashStatus.NO_CASH.name()));
        ThirdInvokeLogEntity thirdInvokeLogEntity = thirdInvokeLogService.generalThirdInvoiceLogEntity(ApiUrl.BATCH_UPDATE_COU_CASH_STATUS,
                ThirdInvokeLogConstants.COU_MANAGER, null, JsonUtil.object2String(lockCouPara));

        try {
            List<Cou> couList = couTransferApiService.getLockCouListAndUpdate(lockCouPara);
            thirdInvokeLogService.fillReturnInfo(JsonUtil.object2String(couList), StringUtil.valueOf(ReturnCode.REQUEST_SUCCESS.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
            return couList;
        } catch (Exception e) {
            log.error("", e);
            thirdInvokeLogService.fillReturnInfo(e.getMessage(), StringUtil.valueOf(ReturnCode.REQUEST_FAILED.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
            throw new ParameterException(e.getMessage());
        }
    }

    public void destroyCouByOneSign(FinanceBusinessConfig fiConfig,
                                    TradeVerifiable tradeVerifiable, String destroyCouUuid, boolean ifRefund) throws ParameterException {
        DestroyCouByOneSignPara destroyCouByOneSignPara = new DestroyCouByOneSignPara();
        destroyCouByOneSignPara.setTradeVerifiable(tradeVerifiable);
        destroyCouByOneSignPara.setCouUuid(destroyCouUuid);
        destroyCouByOneSignPara.setProject(PROJECT);
        destroyCouByOneSignPara.setAccessKey(fiConfig.getChainAccessKey());
        destroyCouByOneSignPara.setIfRefund(ifRefund);
        ThirdInvokeLogEntity thirdInvokeLogEntity = thirdInvokeLogService.generalThirdInvoiceLogEntity(ApiUrl.DESTROY_BY_ONE_SIGN,
                ThirdInvokeLogConstants.COU_MANAGER, destroyCouByOneSignPara.getCouUuid(), JsonUtil.object2String(destroyCouByOneSignPara));
        try {
            couTransferApiService.destroyCouByOneSign(destroyCouByOneSignPara);
            thirdInvokeLogService.fillReturnInfo(null, StringUtil.valueOf(ReturnCode.REQUEST_SUCCESS.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
        } catch (Exception e) {
            log.error("destroyCouByOneSign", e);
            thirdInvokeLogService.fillReturnInfo(e.getMessage(), StringUtil.valueOf(ReturnCode.REQUEST_FAILED.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
            throw new ParameterException(e.getMessage());
        }
    }

    public PageInfo<Cou> queryCouList(QueryCouListPara para) throws ParameterException {
        para.setProject(PROJECT);
        try {
            return couQueryApiService.queryOnlyCouList(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    /**
     * COU流转-查询cou统计金额
     */
    public QuotaStatisticsInfo couStatisticsAmountQuery() throws ParameterException {
        AccountUserDTO user = UserUtil.get();
        QuotaStatisticsInfo quotaStatisticsInfo = new QuotaStatisticsInfo();
        QueryCouAmountStatisticsPara para = new QueryCouAmountStatisticsPara();
        para.setHolderPubKey(user.getCompany().getPubKey());
        CouStatistics couStatistics = couStatisticsAmountQuery(para);
        BeanUtils.copyProperties(couStatistics, quotaStatisticsInfo);
        BigDecimal usedMoney = getUsedAmount(user.getUuid());
        if (user.getQuotaInCent() != null) {
            quotaStatisticsInfo.setUserAvailableQuotaInCent(user.getQuotaInCent().subtract(usedMoney));
        }
        return quotaStatisticsInfo;
    }

    public CouCreditPublishListResponse queryCouCreditAndPublishList(String companyPubKey) throws ParameterException {
        CreditAndPublishQueryPara para = new CreditAndPublishQueryPara();
        para.setPubKey(companyPubKey);
        para.setProject(PROJECT);
        try {
            return couQueryApiService.queryCouCreditAndPublishList(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public ChainTransferResponse queryChainLastByTransferUuid(String transferUuid) throws ParameterException {
        GetLastChainByTransferUuidPara para = new GetLastChainByTransferUuidPara();
        para.setTransferUuid(transferUuid);
        para.setProject(PROJECT);
        try {
            return couQueryApiService.queryChainLastByTransferUuid(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public CouStatistics couStatisticsAmountQuery(QueryCouAmountStatisticsPara para) throws ParameterException {
        para.setProject(PROJECT);
        try {
            return couQueryApiService.couStatisticsAmountQuery(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public List<CouDetailResponse> queryCouDetail(String couUuid) throws ParameterException {
        QueryCouDetailsByUuidPara para = new QueryCouDetailsByUuidPara();
        para.setCouUuidList(Collections.singletonList(couUuid));
        para.setProject(PROJECT);
        try {
            return couQueryApiService.queryCouDetailsByUuid(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public List<Cou> couListByTransferUuid(String transferUuid) throws ParameterException {
        QueryCouListByTransferUuidPara request = new QueryCouListByTransferUuidPara();
        request.setTransferUuid(transferUuid);
        request.setProject(PROJECT);
        try {
            return couQueryApiService.couListByTransferUuid(request);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public List<Cou> couListByUuids(List<String> couUuids) throws ParameterException {
        QueryCouListByUuidsPara request = new QueryCouListByUuidsPara();
        request.setCouUuidList(couUuids);
        request.setProject(PROJECT);
        try {
            return couQueryApiService.couListByUuids(request);
        } catch (Exception e) {
            log.error("couListByUuids", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public List<Cou> couListByNos(List<String> couNoList) throws ParameterException {
        QueryCouListByNoPara request = new QueryCouListByNoPara();
        request.setCouNoList(couNoList);
        request.setProject(PROJECT);
        try {
            return couQueryApiService.couListByNos(request);
        } catch (Exception e) {
            log.error("couListByNos", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public PageInfo<String> couNoList(List<CouStatus> statusList, Integer pageNum, Integer pageSize) {
        QueryCouNoPara request = new QueryCouNoPara();
        request.setStatusList(statusList);
        request.setProject(PROJECT);
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        try {
            return couQueryApiService.couNoList(request);
        } catch (Exception e) {
            log.error("couNoList", e);
            throw new BusinessException(e.getMessage());
        }
    }

    public List<Cou> couListByUuidsAndTransfer(List<String> couUuids) throws ParameterException {
        QueryCouListByUuidsPara request = new QueryCouListByUuidsPara();
        request.setCouUuidList(couUuids);
        request.setNeedQueryTransfer(true);
        request.setProject(PROJECT);
        try {
            return couQueryApiService.couListByUuids(request);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public PageInfo<Transfer> getTransferListNew(QueryTransferListPara para)
            throws ParameterException {
        para.setProject(PROJECT);
        PageInfo<Transfer> transferPageInfo = null;
        try {
            transferPageInfo = couQueryApiService.getTransferListNew(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
        if (transferPageInfo == null || CollectionUtils.isEmpty(transferPageInfo.getList())) {
            return new PageInfo<>(Collections.emptyList());
        }
        return transferPageInfo;
    }

    public PageInfo<Transfer> getTransferListForRefund(QueryTransferListPara para, AccountUserDTO user)
            throws ParameterException {
        para.setProject(PROJECT);
        PageInfo<Transfer> transferPageInfo = null;
        List<Refund> refundList =
                refundQueryDao.selectRefundNotRejectListByRefundCompanyUuid(String.valueOf(user.getCompany().getId()));
        List<String> oldTransferUuidList = refundList.stream().map(Refund::getOldTransferUuid).collect(Collectors.toList());
        para.setFilterUuids(oldTransferUuidList);
        try {
            transferPageInfo = couQueryApiService.queryTransferListForRefund(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
        if (transferPageInfo == null || CollectionUtils.isEmpty(transferPageInfo.getList())) {
            return new PageInfo<>(Collections.emptyList());
        }
        return transferPageInfo;
    }

    public PageInfo<Transfer> getTransferListByUuids(List<String> transferUuids)
            throws ParameterException {
        UuidsBean uuidsBean = new UuidsBean();
        uuidsBean.setProject(PROJECT);
        uuidsBean.setTransferUuids(transferUuids);

        PageInfo<Transfer> transferPageInfo = null;
        try {
            transferPageInfo = couQueryApiService.getTransferUuids(uuidsBean);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
        if (transferPageInfo == null || CollectionUtils.isEmpty(transferPageInfo.getList())) {
            return new PageInfo<>(Collections.emptyList());
        }
        return transferPageInfo;
    }

    public BigDecimal getUsedAmount(String uuid) throws ParameterException {
        AvailableLimitMoneyPara para = new AvailableLimitMoneyPara();
        para.setOperateUuid(uuid);
        try {
            return couQueryApiService.queryUsedMoney(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public Transfer getTransferDetail(QueryTransferDetailPara para)
            throws ParameterException {
        try {
            return couQueryApiService.getTransferDetail(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public Transfer getTransferByUuid(String transferUuid) throws ParameterException {
        QueryTransferDetailPara transferDetailPara = new QueryTransferDetailPara();
        transferDetailPara.setProject(PROJECT);
        transferDetailPara.setTransferUuid(transferUuid);
        try {
            return couQueryApiService.getTransferDetail(transferDetailPara);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public List<String> getTransferFromList(String pubKey) throws ParameterException {
        QueryTransferFromListPara para = new QueryTransferFromListPara();
        para.setToPubKey(pubKey);
        para.setProject(PROJECT);
        try {
            return couQueryApiService.getTransferFromList(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public List<String> getTransferToList(String fromPubKey) throws ParameterException {
        QueryTransferToListPara para = new QueryTransferToListPara();
        para.setFromPubKey(fromPubKey);
        para.setProject(PROJECT);
        try {
            return couQueryApiService.getTransferToList(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public List<CouTransferPath> getCouListTransferPath(List<String> couUuidList) throws ParameterException {
        QueryCouListTransferPathPara para = new QueryCouListTransferPathPara();
        para.setCouUuidList(couUuidList);
        para.setProject(PROJECT);
        List<CouTransferPath> resList = null;
        try {
            resList = couQueryApiService.getCouListTransferPath(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
        if (CollectionUtils.isEmpty(resList)) {
            return Collections.emptyList();
        }
        return resList;
    }

    public List<CouParentPath> getCouListParentPath(List<String> couUuidList, Boolean isRelationTransfer) throws ParameterException {
        QueryCouListTransferPathPara para = new QueryCouListTransferPathPara();
        para.setCouUuidList(couUuidList);
        para.setRelationTransfer(isRelationTransfer);
        para.setProject(PROJECT);
        List<CouParentPath> resList = null;
        try {
            resList = couQueryApiService.queryCouListParentPath(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
        if (CollectionUtils.isEmpty(resList)) {
            return Collections.emptyList();
        }
        return resList;
    }

    public PageInfo<CouBill> queryOriginalCouList(OriginalCouQueryPara para, AccountUserDTO user) throws ParameterException {
        QueryOriginalCouPara couRequestPara = new QueryOriginalCouPara();
        BeanUtils.copyProperties(para, couRequestPara);
        couRequestPara.setProject(PROJECT);
        couRequestPara.setPublishPubKey(user.getCompany().getPubKey());
        couRequestPara.setStatusList(para.getCouStatusList());
        //couRequestPara.setTeamNo(teamUserService.queryTeamNoByUserId(user.getId()));
        // 设置质押状态
        couRequestPara.setPledgeStatus(para.getPledgeStatus());
        if (para.getLimitOperator()) {
            couRequestPara.setOperateUuid(user.getUuid());
        }
        try {
            return couQueryApiService.queryOriginalCouList(couRequestPara);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public CoreCompanyCouDueStatisticalResp queryOriginalCouStatsData(QueryOriginalCouStatsDataPara para) throws ParameterException {
        try {
            return couQueryApiService.queryOriginalCouStatsData(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public List<CouBillCreateStatsResponse> queryOriginalCouCreateStatsData(QueryOriginalCouCreateStatsDataPara para) throws ParameterException {
        try {
            return couQueryApiService.queryOriginalCouCreateStatsData(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public PageInfo<CouBill> queryAllOriginalCouList(SpOriginalCouQueryPara para, AccountUserDTO user) throws ParameterException {
        QueryAllOriginalCouPara couRequestPara = new QueryAllOriginalCouPara();
        BeanUtils.copyProperties(para, couRequestPara);
        if (CompanyType.OO.name().equals(user.getCompany().getType())) {
            couRequestPara.setOperatingOrganizationUuid(user.getCompany().getUuid());
        }
        if (para.getLimitOperator()) {
            couRequestPara.setOperateUuid(user.getUuid());
        }
        couRequestPara.setTeamNo(teamUserService.queryTeamNoByUserId(user.getId()));
        couRequestPara.setProject(PROJECT);
        try {
            return couQueryApiService.QueryAllOriginalCouPara(couRequestPara);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public PageInfo<CouBill> querySpOriginalCou4DealCouQuota(SpOriginalCouQueryPara para, AccountUserDTO user) throws ParameterException {
        QueryAllOriginalCouPara couRequestPara = new QueryAllOriginalCouPara();
        BeanUtils.copyProperties(para, couRequestPara);
        if (CompanyType.OO.name().equals(user.getCompany().getType())) {
            couRequestPara.setOperatingOrganizationUuid(user.getCompany().getUuid());
        }
        if (para.getLimitOperator()) {
            couRequestPara.setOperateUuid(user.getUuid());
        }
        couRequestPara.setTeamNo(teamUserService.queryTeamNoByUserId(user.getId()));
        couRequestPara.setProject(PROJECT);
        try {
            return couQueryApiService.querySpOriginalCou4DealCouQuota(couRequestPara);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public PageInfo<CreateCouStatisticVo> queryCreateCouStatistics(CreateCouStatisticsPara para, AccountUserDTO user) throws Exception {
        CountCouCreatePara couRequestPara = new CountCouCreatePara();
        BeanUtils.copyProperties(para, couRequestPara);
        if (CompanyType.OO.name().equals(user.getCompany().getType())) {
            couRequestPara.setOperatingOrganizationUuid(user.getCompany().getUuid());
        }
        if (para.getLimitOperator()) {
            couRequestPara.setOperateUuid(user.getUuid());
        }
        couRequestPara.setTeamNo(teamUserService.queryTeamNoByUserId(user.getId()));
        couRequestPara.setProject(PROJECT);
        try {
            return couQueryApiService.createCouCreate(couRequestPara);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public PageInfo<LeafCou> queryHolderCouList(HolderCouQueryPara para, AccountUserDTO user) throws ParameterException {
        QueryHolderCouPara couRequestPara = new QueryHolderCouPara();
        BeanUtils.copyProperties(para, couRequestPara);
        if (CompanyType.OO.name().equals(user.getCompany().getType())) {
            couRequestPara.setOperatingOrganizationUuid(user.getCompany().getUuid());
        }
        couRequestPara.setProject(PROJECT);
        if (para.getLimitOperator()) {
            couRequestPara.setOperateUuid(user.getUuid());
        }
        couRequestPara.setTeamNo(teamUserService.queryTeamNoByUserId(user.getId()));
        try {
            return couQueryApiService.QueryHolderCouPara(couRequestPara);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }


    //当上链信息没有查询到时，该函数返回null
    public ChainTransferResponse getLastChainByRelationUuid(String relationUuid) throws ParameterException {
        GetLastChainByRelationUuidPara para = new GetLastChainByRelationUuidPara();
        para.setProject(PROJECT);
        para.setRelationUuid(relationUuid);
        try {
            return couQueryApiService.queryChainLastByRelationUuid(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public List<Cou> queryCouByCashStatus(Date startDueDate, Date endDueDate, boolean isQueryOrigin,
                                          List<String> statusList, List<String> cashStatusList) throws ParameterException, ParseException {
        QueryCouCashStatusPara queryCouCashStatusPara = new QueryCouCashStatusPara();
        queryCouCashStatusPara.setProject(PROJECT);
        queryCouCashStatusPara.setTransferStatusList(statusList);
        queryCouCashStatusPara.setCashStatusList(cashStatusList);
        queryCouCashStatusPara.setStartDueDate(TimeUtils.getFormatDate(startDueDate));
        queryCouCashStatusPara.setEndDueDate(TimeUtils.getFormatDate(endDueDate));
        queryCouCashStatusPara.setQueryOrigin(isQueryOrigin);
        try {
            return couQueryApiService.queryCouByCashStatus(queryCouCashStatusPara);
        } catch (Exception e) {
            log.error("cou获取列表失败", e);
            throw new ParameterException(e.getMessage());
        }
    }

    //当没有上一手交易时（比如除了开立，其它交易都是reject），
    // PreTransferCouRes仅仅返回currCou，不返回preTransferCou
    public List<PreTransferCouRes> getPreTransferCouListByParents(List<String> couUuidList) throws ParameterException {
        QueryCouListTransferPathPara para = new QueryCouListTransferPathPara();
        para.setCouUuidList(couUuidList);
        para.setProject(PROJECT);
        List<PreTransferCouRes> resList = null;
        try {
            resList = couQueryApiService.getPreTransferCouListByParents(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
        if (CollectionUtils.isEmpty(resList)) {
            return Collections.emptyList();
        }
        return resList;
    }

    public StatisticsInfo queryStatisticsInfo() throws ParameterException {
        try {
            return couQueryApiService.queryStatisticsInfo();
        } catch (Exception e) {
            log.error("queryStatisticsInfo", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public Statistical queryPayAndCreateCou(QueryCouPayAndCreatePara para) throws ParameterException {
        try {
            return couQueryApiService.queryPayAndCreateCou(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public StatisticalBalance statisticalBalanceData(StatisticalBalancePara para) throws ParameterException {
        try {
            return couQueryApiService.statisticalBalanceData(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public TeamTransferStatisticsRes teamTransferAmountStatistics(TeamTransferStatisticsPara para) throws ParameterException {
        try {
            para.setProject(PROJECT);
            return couQueryApiService.teamTransferAmountStatistics(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public List<Transfer> transferCouHistoryTeamNoList(List<String> userIds) throws ParameterException {
        UserUuidsPara userUuidsPara = new UserUuidsPara();
        userUuidsPara.setUserUuidList(userIds);
        userUuidsPara.setProject(PROJECT);
        try {
            return couTransferApiService.queryAndUpdateTransferCouHistoryTeamNoProcess(userUuidsPara);
        } catch (Exception e) {
            log.error("transferCouHistoryTeamNoList", e);
            throw new ParameterException(e.getMessage());
        }
    }


    /**
     * 检查是否为一手的cou
     *
     * @param couUuid
     * @return
     * @throws Exception
     */
    public CouFirsthandVo firsthandCouCheck(String couUuid) throws Exception {
        CouPara couPara = new CouPara();
        couPara.setCouUuid(couUuid);
        return couQueryApiService.queryCouUseFlag(couPara);
    }


    /**
     * 查询当日到期兑付的cou数量
     *
     * @return
     * @throws IOException
     */
    public CountCouByDueDateVO todayCashCount(String pubKey, Date date) throws IOException {
        CountCouByDueDatePara para = new CountCouByDueDatePara();
        para.setDueDate(date);
        para.setPublishPubKey(pubKey);
        para.setProject(PROJECT);
        return couQueryApiService.countCouByDueDate(para);
    }

    public TransferCountGroupByStatusDTO queryTransferCountGroupByStatus(CountTransferGroupByStatus para) throws Exception {
        TransferCountGroupByStatusDTO dto = couTransferApiService.queryTransferCountGroupByStatus(para);
        return dto;
    }

    /**
     * 查询开立额度信息
     *
     * @param countCouAmountPara
     */
    public CreateCouAmountStatisticVo queryCreateCouAmountStatistics(CountCouAmountPara countCouAmountPara) throws Exception {
        countCouAmountPara.setProject(PROJECT);
        return couQueryApiService.createCouAmountStatistics(countCouAmountPara);
    }

    public QueryCouCashStatsDataResponse queryCouCashStatsData(QueryCouCashStatsDataPara para) throws ParameterException {
        try {
            return couQueryApiService.queryCouCashStatsData(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }


    /**
     * 一般企业查询支付额度
     *
     * @param para
     */
    public PayCouAmountStatisticVo payCouAmountStatistics(QueryTransferSumByAcceptOrPayPara para) throws Exception {
        para.setProject(PROJECT);
        return couTransferApiService.payCouAmountStatistics(para);
    }

    public PageInfo<HolderCouStatisticVO> queryHolderCouStatistics(HolderCouStatisticsPara para, AccountUserDTO user) throws Exception {
        QueryHolderCouStatisticsPara couRequestPara = new QueryHolderCouStatisticsPara();
        BeanUtils.copyProperties(para, couRequestPara);
        if (CompanyType.OO.name().equals(user.getCompany().getType())) {
            couRequestPara.setOperatingOrganizationUuid(user.getCompany().getUuid());
        }
        couRequestPara.setProject(PROJECT);
        if (para.getLimitOperator()) {
            couRequestPara.setOperateUuid(user.getUuid());
        }
        couRequestPara.setTeamNo(teamUserService.queryTeamNoByUserId(user.getId()));
        try {
            return couQueryApiService.holderCouCreate(couRequestPara);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public void updateTransferToVisibleFlag(String transferUuid, Integer flag) throws Exception {
        UpdateTransferToVisibleFlagPara para = new UpdateTransferToVisibleFlagPara();
        para.setProject(PROJECT);
        para.setTransferUuid(transferUuid);
        para.setFlag(flag);
        try {
            couTransferApiService.updateTransferToVisibleFlag(para);
        } catch (Exception e) {
            log.error("", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public List<Cou> queryOriginalRelCouList(QueryOriginalCouRelPara para) throws ParameterException {
        para.setProject(PROJECT);
        try {
            return couQueryApiService.queryOriginalRelCouList(para);
        } catch (Exception e) {
            log.error("queryLeafCouList", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public void couStatusChangeByCheck(CouStatusChangeByCheckPara para) throws ParameterException {
        para.setProject(PROJECT);
        try {
            couQueryApiService.couStatusChangeByCheck(para);
        } catch (Exception e) {
            log.error("queryLeafCouList", e);
            throw new ParameterException(e.getMessage());
        }
    }
}
