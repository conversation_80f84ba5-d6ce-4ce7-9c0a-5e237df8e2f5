package com.wxbc.rhine3.service.destroy;

import com.wxbc.rhine3.bean.destroy.DestroyEntity;
import com.wxbc.rhine3.exception.UpdateFailedException;
import com.wxbc.rhine3.repository.mapper.DestroyDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import static com.wxbc.rhine3.constants.ExceptionBaseConstants.BASE_DUPLICATE_KEY_ERROR;
import static com.wxbc.rhine3.constants.ExceptionBaseConstants.PARA_VALIDATE_FAILED;

/**
 * User: yanhengfu
 * Date: 2022/11/18
 * Time: 9:56
 * Description: des
 */
@Service
@Slf4j
public class DestroyUpdateDbService {

    @Autowired
    private DestroyDao destroyDao;

    public void insertDestroy(DestroyEntity destroyEntity){
        try{
            destroyDao.insert(destroyEntity);
        } catch (DuplicateKeyException duplicateKeyException) {
            log.error("insertDestroy error ! duplicateKeyException ! destroyEntity = {}", destroyEntity, duplicateKeyException);
            throw new UpdateFailedException(BASE_DUPLICATE_KEY_ERROR);
        } catch (Exception e){
            log.error("insertDestroy error ! destroyEntity = {}", destroyEntity, e);
            throw new UpdateFailedException(PARA_VALIDATE_FAILED);
        }
    }


}