package com.wxbc.rhine3.service.finance.parafill;

import com.wxbc.base.util.JsonUtil;
import com.wxbc.base.util.StringUtil;
import com.wxbc.rhine3.bean.Credit;
import com.wxbc.rhine3.bean.FinanceBusinessConfig;
import com.wxbc.rhine3.bean.FinanceInvoicePara;
import com.wxbc.rhine3.bean.Invoice;
import com.wxbc.rhine3.bean.finance.*;
import com.wxbc.rhine3.common.CreditType;
import com.wxbc.rhine3.constants.*;
import com.wxbc.rhine3.exception.BusinessException;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.service.OoQueryService;
import com.wxbc.rhine3.service.WaveIdService;
import com.wxbc.rhine3.service.company.CompanyService;
import com.wxbc.rhine3.util.TimeUtils;
import com.wxbc.venus.user.dto.AccountUserDTO;
import com.wxbc.venus.user.dto.OrgExtDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @company: 万向区块链
 * @description 内部参数填充抽象类
 * @author: lixingxing
 * @create: 2022-07-12 15:48
 **/
@Slf4j
public abstract class AbstractFinanceParaFillService<T extends CreateFinanceBaseInnerPara> implements FinanceParaFillInterface<T>  {

    @Autowired
    private CompanyService companyService;

    @Autowired
    private WaveIdService waveIdService;

    @Autowired
    private OoQueryService ooQueryService;


    protected void baseInnerParaFill(T  createFinanceBaseInnerPara, FinanceCreatePara financeCreatePara, FinanceBusinessConfig financeBusinessConfig, AccountUserDTO loginUser) throws ParameterException {
         Credit credit=getCreditInfo(financeCreatePara);
        if (credit.getEnable() != CreditEnableState.ENABLE.getCode()) {
            throw new BusinessException(ExceptionConstants.FINANCE_CREDIT_DISABLE_ERROR);
        }
         OrgExtDTO wxCompany= getOoCompany(loginUser.getCompany());
         OrgExtDTO publishCompany=getPublishCompany(financeCreatePara);
         createFinanceBaseInnerPara.setCredit(credit);
          createFinanceBaseInnerPara.setWxCompany(wxCompany);
         createFinanceBaseInnerPara.setPublishCompany(publishCompany);
         createFinanceBaseInnerPara.setFinanceBusinessConfig(financeBusinessConfig);
         createFinanceBaseInnerPara.setLoginUser(loginUser);
         createFinanceBaseInnerPara.setSplitFromCouFinanceMap(financeCreatePara.getSplitDetails().stream().collect(
                Collectors.toMap(SplitDetailCouExtPara::getFromCouUuid, Function.identity())));
         createFinanceBaseInnerPara.setBaseFinancePara4Db(initFinanceBaseInfoByFinanceCreatePara(financeCreatePara,loginUser,credit,financeBusinessConfig));
     }

    private OrgExtDTO getPublishCompany(FinanceCreatePara para) throws ParameterException {
        OrgExtDTO publishCompany = companyService.getCompanyByUuid(para.getFinanceCreateBasePara().getRelationUuid());
        if (null == publishCompany) {
            throw new ParameterException(ExceptionConstants.C_NOT_FOUND);
        }
        return publishCompany;
    }

    private Credit getCreditInfo(FinanceCreatePara para) throws ParameterException {
        // 质押的获取的是持有方的授信
        String companyUuid = null;
        if(CreditType.CREDIT_S.getCode().equals(para.getFinanceCreateRatePara().getCreditType())){
            companyUuid = para.getFinanceCreateBasePara().getApplicationOrgId();
        }else{
            companyUuid = para.getFinanceCreateBasePara().getRelationUuid();
        }
        Credit credit =companyService.extractCreditByCompany2FI(companyUuid, para.getFinanceCreateBasePara().getFiUuid(),para.getFinanceCreateRatePara().getCreditType());

        if (null == credit) {
            throw new ParameterException(ExceptionConstants.CREDIT_NOT_FOUND);
        }
        log.info("查询到授信记录为:{}", JsonUtil.object2String(credit));
        if(!StatusConst.CONFIRMED.equals(credit.getStatus())){
            throw new BusinessException(ExceptionConstants.CREDIT_NOT_APPROVED);
        }
        return credit;
    }

    private OrgExtDTO getOoCompany(OrgExtDTO loginCompany) throws ParameterException {
        OrgExtDTO ooDetailResponse=ooQueryService.queryOoDetailByUuid(StringUtil.valueOf(loginCompany.getOperatingOrgId()));
        if(ooDetailResponse==null){
            throw new ParameterException(ExceptionConstants.WX_COMPANY_NOT_FOUND);
        }
        OrgExtDTO ooCompany=new OrgExtDTO();
        ooCompany.setUuid(StringUtil.valueOf(ooDetailResponse.getId()));
        ooCompany.setOrgName(ooDetailResponse.getOrgName());
        ooCompany.setLegalPerson(ooDetailResponse.getLegalPerson());
        return ooCompany;
    }

    /**
     *  初始化融资用来入库的融资基础实体(这里只初始化公共参数)
     *  ps: 自定义参数可放置下游实现类自由修改
     * @param financeCreatePara 融资创建参数
     * @param loginUser 登录用户
     * @param credit 授信企业
     * @param financeBusinessConfig 金融服务商配置
     * @return
     */
    public Finance initFinanceBaseInfoByFinanceCreatePara(FinanceCreatePara financeCreatePara, AccountUserDTO loginUser, Credit credit, FinanceBusinessConfig financeBusinessConfig) {
        Finance finance=new Finance();
        //basePara4Front
        if (StringUtils.isEmpty(financeCreatePara.getFinanceCreateBasePara().getApplicationNumber())) {
            finance.setApplicationNumber(waveIdService.initNumberByWaveId(financeBusinessConfig.getFinanceNumberPrefix()));
        }else{
            finance.setApplicationNumber(financeCreatePara.getFinanceCreateBasePara().getApplicationNumber());
        }
        //注意：融资保理文件和应收转让通知文件需要等签章以后再填充
        finance.setAssetSumInYuan(financeCreatePara.getFinanceCreateBasePara().getAssetSumInYuan());
        finance.setDiscount(financeCreatePara.getFinanceCreateBasePara().getDiscount());
        finance.setFinanceDueDate(financeCreatePara.getFinanceCreateBasePara().getFinanceDueDate());
        finance.setFiUuid(financeCreatePara.getFinanceCreateBasePara().getFiUuid());
        finance.setRelationUuid(financeCreatePara.getFinanceCreateBasePara().getRelationUuid());
        finance.setInputFinanceAmountInYuan(financeCreatePara.getFinanceCreateBasePara().getInputFinanceAmountInYuan());
        finance.setInputFinanceTransferAmountInYuan(financeCreatePara.getFinanceCreateBasePara().getInputFinanceTransferAmountInYuan());
        finance.setAttachment(financeCreatePara.getFinanceCreateBasePara().getAttachment());
        //ratePara4Front
        finance.setFactoringRate(financeCreatePara.getFinanceCreateRatePara().getFactoringRate());
        finance.setInterestRate(financeCreatePara.getFinanceCreateRatePara().getInterestRate());
        finance.setServiceFee(financeCreatePara.getFinanceCreateRatePara().getServiceFee());
        finance.setServiceFeeRate(financeCreatePara.getFinanceCreateRatePara().getServiceFeeRate());
        //backEndFillPara
        try {
            finance.setFinanceValueDate(TimeUtils.getFormatDate(new Date()));
        } catch (ParseException e) {
            log.error("fillFinanceCreatePara:Date=" + new Date(), e);
        }
        finance.setStatus(FinanceStatus.AUDIT.name());
        finance.setFinanceType(financeCreatePara.getFinanceCreateBasePara().getFinanceType());
        finance.setAssetType(TransferAssetConst.COU.name());
        finance.setApplicantUuid(loginUser.getCompany().getUuid());
        String frUuid = UUID.randomUUID().toString();
        finance.setUuid(frUuid);
        finance.setAssetList(genFinanceAssetList(financeCreatePara.getSplitDetails(), frUuid));
        finance.setInvoiceList(getFinanceInvoiceList(financeCreatePara.getInvoiceList()));
        finance.setInterestPayWay(credit.getInterestPayWay().name());
        finance.setOperatingOrganizationUuid(loginUser.getOperatingOrganizationUuid());
        finance.setPlatformFeeModel(financeBusinessConfig.getPlatformFeeModel());

        //组装保存项目组交易所需要的参数
        finance.setTeamNo(loginUser.getTeamNo());
        return finance;
    }

    private List<FinanceAsset> genFinanceAssetList(List<SplitDetailCouExtPara> splitDetails, String frUuid) {
        List<FinanceAsset> assetList = new ArrayList<>();
        splitDetails.forEach(x -> {
            FinanceAsset financeAsset = new FinanceAsset();
            financeAsset.setAssetUuid(x.getFromCouUuid());
            financeAsset.setFrUuid(frUuid);
            financeAsset.setAssetType(TransferAssetConst.COU.name());
            financeAsset.setAssetFinanceAmountInYuan(x.getToCouAmountInCent().divide(new BigDecimal(CommonConst.TIME100)));
            assetList.add(financeAsset);
        });

        return assetList;
    }

    private List<Invoice> getFinanceInvoiceList(List<FinanceInvoicePara> invoiceParas) {
        List<Invoice> invoices = new ArrayList<>();
        invoiceParas.forEach(x -> {
            Invoice invoice = new Invoice();
            invoice.setUuid(x.getUuid());
            invoice.setApplyAmount(x.getApplyAmount());
            invoices.add(invoice);
        });
        return invoices;
    }

}
