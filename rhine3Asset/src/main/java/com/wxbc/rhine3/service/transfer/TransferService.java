package com.wxbc.rhine3.service.transfer;

import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageInfo;
import com.wxbc.cou.manager.api.constant.TransferTypeEnum;
import com.wxbc.cou.manager.api.domain.Transfer;
import com.wxbc.cou.manager.api.requestPara.QueryTransferDetailPara;
import com.wxbc.cou.manager.api.requestPara.QueryTransferListPara;
import com.wxbc.cou.manager.api.response.ChainTransferResponse;
import com.wxbc.rhine3.bean.TransferApplication;
import com.wxbc.rhine3.bean.contract.Contract;
import com.wxbc.rhine3.bean.response.ChainTradeResponse;
import com.wxbc.rhine3.bean.response.TransferListResponse;
import com.wxbc.rhine3.bean.transfer.TransferCompany;
import com.wxbc.rhine3.bean.transfer.TransferDetailRequest;
import com.wxbc.rhine3.bean.transfer.TransferExportResponse;
import com.wxbc.rhine3.bean.transfer.TransferExportSpResponse;
import com.wxbc.rhine3.bean.transfer.TransferListRequest;
import com.wxbc.rhine3.common.lob.CompanyType;
import com.wxbc.rhine3.constants.CommonConst;
import com.wxbc.rhine3.constants.TransferAssetConst;
import com.wxbc.rhine3.constants.TransferStatus;
import com.wxbc.rhine3.convert.TransferExportMapper;
import com.wxbc.rhine3.convert.TransferSpExportMapper;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.service.SarahService;
import com.wxbc.rhine3.service.company.CompanyService;
import com.wxbc.rhine3.service.contract.QueryContract4CreateOrPay;
import com.wxbc.rhine3.service.team.TeamUserService;
import com.wxbc.rhine3.util.FileUtil;
import com.wxbc.venus.user.dto.AccountUserDTO;
import com.wxbc.venus.user.dto.OrgExtDTO;
import com.wxbc.venus.user.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wxbc.rhine3.constants.CommonConst.COMPANY_TYPE_C;
import static com.wxbc.rhine3.constants.CommonConst.COMPANY_TYPE_S;
import static com.wxbc.rhine3.constants.CommonConst.PROJECT;
import static com.wxbc.rhine3.constants.ExceptionConstants.TRANSFER_NOT_FOUND;

@Service
@Slf4j
public class TransferService {
    @Autowired
    private CompanyService companyService;
    @Autowired
    private TransferApplicationService transferApplicationService;
    @Autowired
    private SarahService sarahService;
    @Autowired
    private QueryContract4CreateOrPay queryContract4CreateOrPay;
    @Autowired
    private GenTransferListResponse genTransferListResponse;
    @Autowired
    private GenRelatedCou genRelatedCou;
    @Autowired
    private TeamUserService teamUserService;


    public PageInfo<TransferListResponse> queryTransferList(TransferListRequest request) throws ParameterException {
        PageInfo<Transfer> transfersPageInfo = getTransferListFromSarah(request);
        if (null == transfersPageInfo || CollectionUtils.isEmpty(transfersPageInfo.getList())) {
            return new PageInfo<>(Collections.emptyList());
        }

        if (companyService.fillTransferCompanyNames(transfersPageInfo.getList())) {
            return new PageInfo<>(Collections.emptyList());
        }
        return genTransferListResponse.genTransferListResponse(transfersPageInfo);
    }

    public TransferListResponse queryTransferDetail(TransferDetailRequest request) throws ParameterException {

        QueryTransferDetailPara para = genQueryTransferDetailPara(request);
        Transfer transfer = sarahService.getTransferDetail(para);
        if (null == transfer) {
            return null;
        }
        if (companyService.fillTransferCompanyNames(Collections.singletonList(transfer))) {
            return null;
        }
        return genTransferDetailResponse(transfer);
    }


    public void exportTransferList(TransferListRequest request, HttpServletResponse response) throws ParameterException, IOException {
        PageInfo<Transfer> transfersPageInfo = getTransferListFromSarah(request);
        if (null == transfersPageInfo
                || CollectionUtils.isEmpty(transfersPageInfo.getList())
                || companyService.fillTransferCompanyNames(transfersPageInfo.getList())) {
            FileUtil.downloadExcel(new ArrayList<TransferExportResponse>(), response);
            return;
        }

        Map<String, Contract> contractMap = queryContract4CreateOrPay.getTransferRelationContractMap(transfersPageInfo.getList());

        if (!CollectionUtils.isEmpty(transfersPageInfo.getList())) {
            AccountUserDTO user = UserUtil.get();
            if (!CompanyType.WX.name().equals(user.getCompany().getType())) {
                List<TransferExportResponse> transferExportResponses = TransferExportMapper.instance.source2TargetList(transfersPageInfo.getList());
                fillExportTransferRelationContract(contractMap, transferExportResponses);
                FileUtil.downloadExcel(transferExportResponses, response);
            } else {
                List<TransferExportSpResponse> transferExportSpResponses = TransferSpExportMapper.instance.source2TargetList(transfersPageInfo.getList());
                fillExportSpTransferRelationContract(contractMap, transferExportSpResponses);
                FileUtil.downloadExcel(transferExportSpResponses, response);
            }

        } else {
            FileUtil.downloadExcel(new ArrayList<TransferExportResponse>(), response);
        }
    }

    public List<TransferCompany> queryTransferFromList() throws ParameterException {
        return handlerQueryTransferCompanyList(CommonConst.FROM);
    }

    public List<TransferCompany> queryTransferToList() throws ParameterException {
        return handlerQueryTransferCompanyList(CommonConst.TO);
    }

    private List<TransferCompany> handlerQueryTransferCompanyList(String queryType) throws ParameterException {
        AccountUserDTO user = UserUtil.get();
        OrgExtDTO company = user.getCompany();
        if (company == null) {
            return Collections.emptyList();
        }

        List<String> responseList;
        switch (queryType) {
            case CommonConst.FROM: {
                responseList = sarahService.getTransferFromList(company.getPubKey());
                break;
            }

            case CommonConst.TO: {
                responseList = sarahService.getTransferToList(company.getPubKey());
                break;
            }

            default: {
                return Collections.emptyList();
            }
        }

        return getTransferCompanies(responseList).stream()
                .filter(x -> !user.getCompanyUuid().equals(x.getCompanyUuid()))
                .collect(Collectors.toList());
    }

    private List<TransferCompany> getTransferCompanies(List<String> pubkeys) {
        if (CollectionUtils.isEmpty(pubkeys)) {
            return Collections.emptyList();
        }
        pubkeys = pubkeys.stream().distinct().collect(Collectors.toList());
        List<OrgExtDTO> companies = companyService.getCompanyByPubKeyList(pubkeys);
        if (CollectionUtils.isEmpty(companies)) {
            return Collections.emptyList();
        }

        AccountUserDTO user = UserUtil.get();
        if (CompanyType.C.name().equals(user.getLoginCompanyType())) {
            companies = companies.stream().filter(company -> !CompanyType.FI.name().equals(company.getType())).collect(Collectors.toList());
            pubkeys = companies.stream().map(OrgExtDTO::getPubKey).collect(Collectors.toList());
        }
        Map<String, OrgExtDTO> companyMap = companies.stream()
                .collect(Collectors.toMap(OrgExtDTO::getPubKey, Function.identity()));
        List<TransferCompany> transferCompanies = new ArrayList<>();
        for (String addr : pubkeys) {
            TransferCompany transferCompany = new TransferCompany();
            transferCompany.setCompanyName(companyMap.get(addr).getOrgName());
            transferCompany.setCouPubKey(addr);
            transferCompany.setCouPubKey(companyMap.get(addr).getPubKey());
            transferCompany.setCompanyUuid(companyMap.get(addr).getUuid());
            transferCompany.setCompanyType(companyMap.get(addr).getType());
            transferCompanies.add(transferCompany);
        }

        return transferCompanies;
    }

    public ChainTradeResponse queryChainLastByTransferUuid(String transferUuid) throws ParameterException {
        ChainTransferResponse res = sarahService.queryChainLastByTransferUuid(transferUuid);
        ChainTradeResponse chainInfo = new ChainTradeResponse();
        if (res != null) {
            BeanUtils.copyProperties(res, chainInfo);
        }
        return chainInfo;

    }

    private void fillExportTransferRelationContract(Map<String, Contract> contractMap, List<TransferExportResponse> exportTransfers) {
        for (TransferExportResponse item : exportTransfers) {
            Contract contract = contractMap.get(item.getTransferNo());
            if (null == contract) {
                continue;
            }
//            item.setContractCode(contract.getContractCode());
//            item.setName(contract.getName());
//            item.setAmount(contract.getAmount());
//            item.setSignDate(DateUtil.format(contract.getSignDate(), "yyyy-MM-dd"));
        }
    }

    private void fillExportSpTransferRelationContract(Map<String, Contract> contractMap, List<TransferExportSpResponse> exportTransfers) {
        for (TransferExportSpResponse item : exportTransfers) {
            Contract contract = contractMap.get(item.getTransferNo());
            if (null == contract) {
                continue;
            }
            item.setContractCode(contract.getContractCode());
            item.setName(contract.getName());
            item.setAmount(contract.getAmount());
            item.setSignDate(DateUtil.format(contract.getSignDate(), "yyyy-MM-dd"));
        }
    }

    private TransferListResponse genTransferDetailResponse(Transfer item) throws ParameterException {
        TransferListResponse transferListResponse = new TransferListResponse();
        transferListResponse.setTransferNo(item.getTransferNo());
        transferListResponse.setTransferUuid(item.getUuid());
        transferListResponse.setTransferType(item.getTransferType());
        transferListResponse.setSumTransferAmountInCent(item.getSumTransferAmountInCent());
        transferListResponse.setFromCouPubKey(item.getFromPubKey());
        transferListResponse.setFromName(item.getFromCompanyName());
        transferListResponse.setToName(item.getToCompanyName());
        transferListResponse.setCreateTime(item.getCreateTime());
        transferListResponse.setSign(item.getSign());
        transferListResponse.setToCouPubKey(item.getToPubKey());
        transferListResponse.setAssetUuids(item.getAssetUuids());
        List<TransferApplication> transferApplicationList = transferApplicationService.getTransferApplicationList(item.getUuid());
        if (CollectionUtils.isEmpty(transferApplicationList)) {
            throw new ParameterException(TRANSFER_NOT_FOUND);
        }
        TransferApplication transferApplication = transferApplicationList.get(0);
        String auditResult = transferApplication.getStatus();
        if (StringUtils.equals(auditResult, TransferStatus.REJECTED.name())) {
            transferListResponse.setRejectReason(transferApplication.getAuditReason());
            transferListResponse.setStatus(item.getStatus());
        } else {
            transferListResponse.setStatus(item.getStatus());
        }
        transferListResponse.setAttachment(transferApplication.getAttachment());
        transferListResponse.setTransferRemark(transferApplication.getTransferRemark());

        //查询资产列表
        if (StringUtils.isNotBlank(item.getAssetUuids()) && TransferAssetConst.CONTRACT.name().equals(item.getAssetType())) {
            transferListResponse.setContracts(queryContract4CreateOrPay.transferContractList(item.getAssetUuids()));
        }
        transferListResponse.setTransferCous(genRelatedCou.genRelatedCou(item.getCouList()));
        return transferListResponse;
    }

    private QueryTransferDetailPara genQueryTransferDetailPara(TransferDetailRequest request) {
        QueryTransferDetailPara para = new QueryTransferDetailPara();
        para.setProject(PROJECT);
        BeanUtils.copyProperties(request, para);
        return para;
    }

    private PageInfo<Transfer> getTransferListFromSarah(TransferListRequest request) throws ParameterException {
        AccountUserDTO user = UserUtil.get();
        QueryTransferListPara para = genQueryTransferListPara(request, user);
        if (processToCompanyNameSearchCondition(request, para)) {
            return null;
        }
        return request.getIfRefund() ? sarahService.getTransferListForRefund(para, user) :
                sarahService.getTransferListNew(para);
    }

    private QueryTransferListPara genQueryTransferListPara(TransferListRequest request, AccountUserDTO user) {
        QueryTransferListPara para = new QueryTransferListPara();
        BeanUtils.copyProperties(request, para);
//        if (CommonConst.TRANSFER_TYPE_RECEIVE.equals(request.getAccrualPrinciple())) {
//            para.setToPubKeys(Collections.singletonList(user.getCompany().getPubKey()));
//        } else if (CommonConst.TRANSFER_TYPE_PAY.equals(request.getAccrualPrinciple())) {
//            para.setFromPubKeys(Collections.singletonList(user.getCompany().getPubKey()));
//        } else {
//            if (!CompanyType.WX.name().equals(user.getCompany().getType())
//                    &&!CompanyType.OO.name().equals(user.getCompany().getType())) {
//                para.setFilterPubKey(user.getCompany().getPubKey());
//            }
//        }

        if (COMPANY_TYPE_C.equalsIgnoreCase(request.getOrgType())) {
            //核心企业
            para.setFromPubKeys(Collections.singletonList(user.getCompany().getPubKey()));
            List<String> transferTypeList = Arrays.asList(TransferTypeEnum.CREATE.name());
            para.setTransferTypeList(transferTypeList);
            para.setFromPubKeys(Collections.singletonList(user.getCompany().getPubKey()));
            if (CommonConst.TRANSFER_TYPE_RECEIVE.equals(request.getAccrualPrinciple())) {
                para.setToPubKeys(Collections.singletonList(user.getCompany().getPubKey()));
            }
        } else if (COMPANY_TYPE_S.equalsIgnoreCase(request.getOrgType())) {
            // 供应商
            para.setFilterPubKey(user.getCompany().getPubKey());
            para.setGeneralFlag(true);
            List<String> transferTypeList = Arrays.asList(TransferTypeEnum.CREATE.name(), TransferTypeEnum.PAY.name());
            para.setTransferTypeList(transferTypeList);
            if (CommonConst.TRANSFER_TYPE_RECEIVE.equals(request.getAccrualPrinciple())) {
                para.setToPubKeys(Collections.singletonList(user.getCompany().getPubKey()));
            } else if (CommonConst.TRANSFER_TYPE_PAY.equals(request.getAccrualPrinciple())) {
                para.setFromPubKeys(Collections.singletonList(user.getCompany().getPubKey()));
            }
        }
        if (!CompanyType.WX.name().equals(user.getCompany().getType())
                && !CompanyType.OO.name().equals(user.getCompany().getType())) {
            para.setFilterPubKey(user.getCompany().getPubKey());
        }

        if (request.getLimitOperator()) {
            para.setOperateUuid(user.getUuid());
        }
        //para.setTeamNo(teamUserService.queryTeamNoByUserId(user.getId()));
        if (CompanyType.OO.name().equals(user.getCompany().getType())) {
            para.setOperatingOrganizationUuid(user.getCompany().getUuid());
        }
        para.setProject(PROJECT);
        para.setTypeList(Arrays.asList(TransferAssetConst.CONTRACT.name()));
        return para;
    }

    private Boolean processToCompanyNameSearchCondition(TransferListRequest request, QueryTransferListPara para) {
        if (!StringUtils.isEmpty(request.getToCompanyName())) {
            OrgExtDTO toCompany = companyService.searchCompanyByFullName(request.getToCompanyName());
            if (toCompany == null) {
                return true;
            }
            if (CollectionUtils.isEmpty(para.getToPubKeys())) {
                para.setToPubKeys(Collections.singletonList(toCompany.getPubKey()));
            } else {
                List<String> originCondition = para.getToPubKeys();
                if (originCondition.contains(toCompany.getPubKey())) {
                    para.setToPubKeys(Collections.singletonList(toCompany.getPubKey()));
                } else {
                    return true;
                }
            }
        }
        return false;
    }
}
