package com.wxbc.rhine3.service.email;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wxbc.rhine3.bean.email.AddEmailConfigPara;
import com.wxbc.rhine3.bean.email.DeleteEmailConfig;
import com.wxbc.rhine3.bean.email.ModifyEmailConfigPara;
import com.wxbc.rhine3.exception.BusinessException;
import com.wxbc.rhine3.repository.entity.EmailConfigEntity;
import com.wxbc.rhine3.repository.mapper.EmailConfigDao;
import com.wxbc.rhine3.repository.table.EmailConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> zhengjiao
 * @Date : 2023-04-04 11:02
 * @Description :
 */
@Service
@Slf4j
public class EmailConfigService {
    @Autowired
    private EmailConfigDao emailConfigDao;

    public List<EmailConfigEntity> emailConfigList() {
        return emailConfigDao.selectList(Wrappers.emptyWrapper());
    }

    public void updateEmailConfig(ModifyEmailConfigPara para) {
        EmailConfigEntity entity = emailConfigDao.selectById(para.getId());
        if (null == entity) {
            throw new BusinessException("未找到相关邮件配置");
        }


        UpdateWrapper<EmailConfigEntity> updateWrapper = Wrappers.update();
        updateWrapper.set(null != para.getStatus(), EmailConfig.STATUS,para.getStatus())
                .set(StringUtils.isNotBlank(para.getEmailSubject()),EmailConfig.EMAIL_SUBJECT,para.getEmailSubject())
                .set(StringUtils.isNotBlank(para.getRoleNames()),EmailConfig.ROLE_NAMES,para.getRoleNames())
                .eq(EmailConfig.ID,para.getId());

        emailConfigDao.update(null, updateWrapper);
    }

    public void emailConfigAdd(AddEmailConfigPara para){
        EmailConfigEntity entity = new EmailConfigEntity();
        BeanUtil.copyProperties(para,entity);
        entity.setStatus(1);

        emailConfigDao.insert(entity);
    }

    public void emailConfigDelete(DeleteEmailConfig para){
        emailConfigDao.deleteById(para.getId());
    }
}
