package com.wxbc.rhine3.service.transfer;

import com.github.pagehelper.PageInfo;
import com.wxbc.cou.manager.api.domain.Transfer;
import com.wxbc.rhine3.bean.Company;
import com.wxbc.rhine3.bean.User;
import com.wxbc.rhine3.bean.response.TransferListResponse;
import com.wxbc.rhine3.constants.TransferAssetConst;
import com.wxbc.rhine3.constants.TransferStatus;
import com.wxbc.rhine3.service.AssetCacheLoginUserService;
import com.wxbc.rhine3.service.company.CompanyService;
import com.wxbc.rhine3.service.contract.QueryContract4CreateOrPay;
import com.wxbc.venus.user.dto.AccountUserDTO;
import com.wxbc.venus.user.dto.OrgExtDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

@Service
@Slf4j
public class GenTransferListResponse {
    @Autowired
    private AssetCacheLoginUserService assetCacheLoginUserService;
    @Autowired
    private CompanyService companyService;
    @Autowired
    private QueryContract4CreateOrPay queryContract4CreateOrPay;
    @Autowired
    private GenRelatedCou genRelatedCou;

    public PageInfo<TransferListResponse> genTransferListResponse(PageInfo<Transfer> transfersPageInfo) {
        PageInfo<TransferListResponse> transferResponse = new PageInfo<>();
        BeanUtils.copyProperties(transfersPageInfo, transferResponse);

        transferResponse.setList(new ArrayList<>(transfersPageInfo.getList().size()));
        for (Transfer item:transfersPageInfo.getList()){
            TransferListResponse transferListResponse = new TransferListResponse();
            BeanUtils.copyProperties(item,transferListResponse);

            transferListResponse.setTransferUuid(item.getUuid());
            transferListResponse.setFromCouPubKey(item.getFromPubKey());
            transferListResponse.setToCouPubKey(item.getToPubKey());

            //设置COU的publish方的名称
            transferListResponse.setTransferCous(genRelatedCou.genRelatedCou(item.getCouList()));
            transferListResponse.setRelCouList(genRelatedCou.genRelatedCou(item.getRelCouList()));
            if (item.getStatus().equals(TransferStatus.CONFIRMED.name())) {
                transferListResponse.setReceiveTime(item.getUpdateTime());
            }
            fillOperator(item, transferListResponse);
            fillFromCompany(transferListResponse);
            fillContracts(item, transferListResponse);
            fillToCompany(item, transferListResponse);
            transferResponse.getList().add(transferListResponse);
        }

        return transferResponse;
    }

    private void fillToCompany(Transfer item, TransferListResponse transferListResponse) {
        OrgExtDTO toCompany = companyService.extractCompanyByPubKey(item.getToPubKey());
        if (toCompany != null) {
            transferListResponse.setToSocialCreditCode(toCompany.getSocialCreditCode());
            transferListResponse.setToName(toCompany.getOrgName());
            transferListResponse.setToCompanyUuid(toCompany.getUuid());
            //transferListResponse.setFinanceActiveStatus(companyService.checkIsActiveCompanyCircle(
                    //transferListResponse.getTransferCous().get(0).getCreditUuid(), toCompany.getUuid()));
        }
    }

    private void fillContracts(Transfer item, TransferListResponse transferListResponse) {
        //查询资产列表
        if (StringUtils.isNotBlank(item.getAssetUuids())
                && TransferAssetConst.CONTRACT.name().equals(item.getAssetType())) {
            transferListResponse.setContracts(queryContract4CreateOrPay.transferContractList(item.getAssetUuids()));
        }
    }

    private void fillFromCompany(TransferListResponse transferListResponse) {
        OrgExtDTO fromCompany = companyService.extractCompanyByPubKey(transferListResponse.getFromCouPubKey());
        if (fromCompany != null) {
            transferListResponse.setFromName(fromCompany.getOrgName());
            transferListResponse.setFromCompanyUuid(fromCompany.getUuid());
            transferListResponse.setFromType(fromCompany.getType());
            transferListResponse.setFromSocialCreditCode(fromCompany.getSocialCreditCode());
        }
    }

    private void fillOperator(Transfer item, TransferListResponse transferListResponse) {
        if (!"#".equals(item.getOperateUuid()) && !StringUtils.isEmpty(item.getOperateUuid())) {
            try {
                AccountUserDTO user = assetCacheLoginUserService.getUserByUuid(item.getOperateUuid());
                if (user != null) {
                    transferListResponse.setOperator(user.getUsername());
                }
            } catch (Exception e) {
                log.error("transfer list get user：{} info fail", item.getOperateUuid(), e);
            }
        }
    }

}
