package com.wxbc.rhine3.service.cou.sign.pay;

import com.wxbc.rhine3.bean.Protocol;
import com.wxbc.rhine3.bean.User;
import com.wxbc.rhine3.bean.cou.PayCouRequest;
import com.wxbc.rhine3.bean.cou.PayCouSignPara;
import com.wxbc.rhine3.constants.FiFunctionEnum;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.venus.user.dto.AccountUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;


@Slf4j
@Service
public class JxPayCouSignService implements PayCouSignInterface {
    @Autowired
    private PayCouSignCommonService payCouSignCommonService;

    @Override
    public Map<String, Protocol> signFile4Pay(PayCouRequest payCouRequest, AccountUserDTO user) throws ParameterException, IOException {
        PayCouSignPara payCouSignPara=new PayCouSignPara();
        payCouSignPara.setCreditUuid(payCouRequest.getPayBase().getCreditUuid());
        payCouSignPara.setPublishUuid(payCouRequest.getPayBase().getPublishUuid());
        payCouSignPara.setReceiverUuid(payCouRequest.getPayBase().getReceiverUuid());
        payCouSignPara.setSplitCouList(payCouRequest.getSplitCouList());
        payCouSignPara.setLoginUser(user);
        payCouSignPara.setImmediatelySign(true);
        payCouSignPara.setFiFunctionEnum(FiFunctionEnum.JXB);
        return payCouSignCommonService.paySignProcess(payCouSignPara);
    }

    @Override
    public FiFunctionEnum initInterfaceAndSignatureCallEnum() {
        return FiFunctionEnum.JXB;
    }
}
