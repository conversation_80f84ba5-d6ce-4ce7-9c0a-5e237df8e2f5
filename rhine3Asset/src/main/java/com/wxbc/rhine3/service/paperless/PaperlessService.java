package com.wxbc.rhine3.service.paperless;

import cn.com.infosec.netsign.agent.NetSignAgent;
import cn.hutool.core.util.IdUtil;
import com.alibaba.nacos.common.utils.MD5Utils;
import com.wxbc.base.util.DateUtil;
import com.wxbc.base.util.JsonUtil;
import com.wxbc.rhine3.bean.jrbank.*;
import com.wxbc.rhine3.bean.jrbank.paperless.*;
import com.wxbc.rhine3.bean.paperless.*;
import com.wxbc.rhine3.bean.request.GeneralConfigPara;
import com.wxbc.rhine3.bean.request.GeneralConfigQueryPara;
import com.wxbc.rhine3.bill.util.StringUtils;
import com.wxbc.rhine3.common.Response;
import com.wxbc.rhine3.config.SftpConfig;
import com.wxbc.rhine3.constants.*;
import com.wxbc.rhine3.feign.UserFeignClient;
import com.wxbc.rhine3.repository.mapper.PaperlessInfoDao;
import com.wxbc.rhine3.service.FtpUtilService;
import com.wxbc.rhine3.service.MinioService;
import com.wxbc.rhine3.service.SftpUtilService;
import com.wxbc.rhine3.service.bank.JrBankPaperlessService;
import com.wxbc.rhine3.util.FeignClientUtil;
import com.wxbc.rhine3.util.InetAddressUtil;
import com.wxbc.rhine3.util.TimeUtils;
import com.wxbc.scaffold.common.definition.response.ResponseFormat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PaperlessService {

    @Autowired
    private JrBankPaperlessService jrBankPaperlessService;

    @Autowired
    private SftpConfig sftpConfig;

    @Autowired
    private MinioService minioService;

    @Autowired
    private FtpUtilService ftpUtilService;

    @Autowired
    private PaperlessInfoDao paperlessInfoDao;

    @Autowired
    private UserFeignClient userFeignClient;

    /**
     * 企业操作员证书信息查询
     * @param accountNo
     * @return
     */
    public Response<List<WXUKEYResListDetail>> queryUkeyByAccount(String accountNo) {
        try {
            WXUKEYCXReq req = new WXUKEYCXReq();
            req.setAccountNo(accountNo);
            ResponseFormat<WXUKEYCXRes> resResponseFormat = jrBankPaperlessService.jrQueryUkeyByAccount(req);
            if(resResponseFormat.isSuccess()) {
                List<WXUKEYResListDetail> details = resResponseFormat.getData().getList().getDetail();
                return Response.success(details);
            } else {
                return Response.of(resResponseFormat.getReturnCode(), resResponseFormat.getReturnDesc());
            }
        } catch (Exception e) {
            log.error("企业操作员证书信息查询异常", e);
            return Response.fail();
        }
    }

    /**
     * 信贷合同合成
     * @param fkcnDto
     */
    public Response<PaperlessFkcnhcRes> syntheticContract(PaperlessFkcnDto fkcnDto) {
        try {
            WZH016Req req = new WZH016Req();
            req.setTransq(IdUtil.getSnowflake().nextIdStr());
            req.setTranDate(DateUtil.format8(LocalDate.now()));
            req.setBranchId("01");
            req.setContractMark("KEYWXFKCNH");
//            req.setIsRemake(StringUtils.isNotBlank(fkcnDto.getRemake()) ? fkcnDto.getRemake() : "0");
            req.setIsRemake("1");

            List<IndexObj> list = new ArrayList<>();
            IndexObj indexObj1 = new IndexObj();
            indexObj1.setIndexName("contractNo");
            indexObj1.setIndexValue("合同号");
            IndexObj indexObj2 = new IndexObj();
            indexObj2.setIndexName("IDType");
            indexObj2.setIndexValue("证件类型");
            list.add(indexObj1);
            list.add(indexObj2);
            IndexRows indexRows = new IndexRows();
            indexRows.setIndexObj(list);
            req.setIndexRows(indexRows);

            List<App> appList = new ArrayList<>();
            App app1 = new App();
            app1.setAppName("modelId");
            app1.setAppValue("WXFKCNH");
            App app2 = new App();
            app2.setAppName("contractType");
            app2.setAppValue("WXFKCNH");
            appList.add(app1);
            appList.add(app2);
            Apps apps = new Apps();
            apps.setApp(appList);
            AppNode appNode = new AppNode();
            appNode.setApps(apps);
            List<AppNode> appNodeList = new ArrayList<>();
            appNodeList.add(appNode);
            AppNodes appNodes = new AppNodes();
            appNodes.setAppNode(appNodeList);

            List<Note> noteList = new ArrayList<>();
            Field[] fields = fkcnDto.getClass().getDeclaredFields();
            for(Field field : fields) {
                field.setAccessible(true);
                if(field.get(fkcnDto) != null) {
                    String propertyName = field.getName();
                    String propertyValue = field.get(fkcnDto).toString();
                    Note note = new Note();
                    note.setNoteName(propertyName);
                    note.setNoteValue(propertyValue);
                    noteList.add(note);
                }
            }

            NoteRows noteRows = new NoteRows();
            noteRows.setNote(noteList);
            Notes016 notes016 = new Notes016();
            notes016.setAppNodes(appNodes);
            notes016.setNoteRows(noteRows);
            req.setNotes(notes016);

            PaperlessRequest request = getPaperRequest("*************", req);

            log.error("信贷合同合成参数:{}", JsonUtil.object2String(request));
            ResponseFormat<WZH016ResDto> resResponseFormat = jrBankPaperlessService.syntheticContract(request);
            log.error("信贷合同合成响应:{}", JsonUtil.object2String(resResponseFormat.getData()));

            String basePath = sftpConfig.getPaperlessBaseUrl() + sftpConfig.getPaperlessUnsigned();
            log.error("合同生成的文件夹地址：{}", basePath);
            if(resResponseFormat.isSuccess()) {
                WZH016Res result = resResponseFormat.getData().getServiceBody().getResponse();
                FileObj fileObj = result.getFileRows().getFileObj();
                String filePath = basePath + fileObj.getFileName();
                log.error("合同生成的文件地址：{}", filePath);
                log.error("开始从无纸化系统进行下载");
                byte[] pledgeContractBytes = ftpUtilService.downloadFile2Bytes(filePath, FtpTargetEnum.PAPERLESS_CONTRACT);
                log.error("上传文件到minio系统");
                String bucketAndFileUrl= CommonConst.PROJECT+CommonConst.PATH_DELIMITER+ TimeUtils.getCurrentDayStr()+CommonConst.PATH_DELIMITER;
                String minioPath = minioService.uploadFile(pledgeContractBytes, bucketAndFileUrl+UUID.randomUUID().toString()+"."+StringUtils.getFileSuffix(fileObj.getFileName()));
                log.error("合同生成的minio地址：{}", minioPath);

                PaperlessFkcnhcRes fkcnhcRes = new PaperlessFkcnhcRes();
                fkcnhcRes.setFilePath(minioPath);
                fkcnhcRes.setRecordId(result.getRecordID());
                fkcnhcRes.setFileName(fileObj.getFileName());
                return Response.success(fkcnhcRes);
            } else {
                return Response.of(resResponseFormat.getReturnCode(), resResponseFormat.getReturnDesc());
            }


        } catch (Exception e) {
            log.error("信贷合同合成异常", e);
            return Response.fail();
        }
    }

    /**
     * 信贷合同个人盖章
     * @param fkcnGzDto
     */
    public Response<PaperlessFkcnGzRes> sealContract(PaperlessFkcnGzDto fkcnGzDto) {
        try {
            WZH017Req req = new WZH017Req();
            req.setRecordID(fkcnGzDto.getRecordID());
            req.setTransq(IdUtil.getSnowflake().nextIdStr());
            req.setTranDate(DateUtil.format8(LocalDate.now()));
            req.setBranchId("01");
            req.setIsUpload("0");

            List<App> appList = new ArrayList<>();
            App app1 = new App();
            app1.setAppName("oriFilePath");
            app1.setAppValue(fkcnGzDto.getOriFilePath());
            App app2 = new App();
            app2.setAppName("IDType");
            app2.setAppValue(fkcnGzDto.getIdType());
            App app3 = new App();
            app3.setAppName("IDCard");
            app3.setAppValue(fkcnGzDto.getPublishSocialCode());
            App app4 = new App();
            app4.setAppName("businessName");
            app4.setAppValue(fkcnGzDto.getPublishCompanyName());
            App app5 = new App();
            app5.setAppName("ukeyNo");
            app5.setAppValue(fkcnGzDto.getUkeyNo());
            App app6 = new App();
            app6.setAppName("signWay");
            app6.setAppValue("4");
            App app7 = new App();
            app7.setAppName("signKey");
            app7.setAppValue(fkcnGzDto.getSignKey());
            App app8 = new App();
            app8.setAppName("certType");
            app8.setAppValue("0");
            App app9 = new App();
            app9.setAppName("sm2");
            app9.setAppValue("1");

            appList.add(app1);
            appList.add(app2);
            appList.add(app3);
            appList.add(app4);
            appList.add(app5);
            appList.add(app6);
            appList.add(app7);
            appList.add(app8);
            appList.add(app9);

            Apps apps = new Apps();
            apps.setApp(appList);
            AppNode appNode = new AppNode();
            appNode.setApps(apps);
            List<AppNode> appNodeList = new ArrayList<>();
            appNodeList.add(appNode);
            AppNodes appNodes = new AppNodes();
            appNodes.setAppNode(appNodeList);

            Notes017 notes017 = new Notes017();
            notes017.setAppNodes(appNodes);
            req.setNotes(notes017);

            PaperlessRequest request = getPaperRequest("*************", req);
            log.error("信贷合同个人盖章参数:{}", JsonUtil.object2String(req));
            ResponseFormat<WZH017ResDto> resResponseFormat = jrBankPaperlessService.sealContract(request);
            log.error("信贷合同个人盖章响应:{}", JsonUtil.object2String(resResponseFormat.getData()));

            if(resResponseFormat.isSuccess()) {
                WZH017Res result = resResponseFormat.getData().getServiceBody().getResponse();
                FileObj fileObj = result.getFileRows().getFileObj();

                PaperlessFkcnGzRes fkcnGzRes = new PaperlessFkcnGzRes();
                fkcnGzRes.setFileName(fileObj.getFileName());
                fkcnGzRes.setFileDigest(fileObj.getFileDigest());
                return Response.success(fkcnGzRes);
            } else {
                return Response.of(resResponseFormat.getReturnCode(), resResponseFormat.getReturnDesc());
            }

        } catch (Exception e) {
            log.error("信贷合同个人盖章异常", e);
            return Response.fail();
        }
    }

    /**
     * 信贷合同个人加签
     * @param jqDto
     */
    public Response<PaperlessFkcnJqRes> personalSignContract(PaperlessFkcnJqDto jqDto) {
        try {
            WZH026Req req = new WZH026Req();
            req.setRecordID(jqDto.getRecordID());
            req.setIsUpload("0");

            List<App> appList = new ArrayList<>();
            App app1 = new App();
            app1.setAppName("oriFilePath");
            app1.setAppValue(jqDto.getOriFilePath());
            App app2 = new App();
            app2.setAppName("signature");
            String signature = jqDto.getSignature().replaceAll("(\\r\\n|\\r|\\n|\\n\\r)", "").substring(77);
            app2.setAppValue(signature);
//            app2.setAppValue(bytesToHex(signature.getBytes()));
//            app2.setAppValue(jqDto.getSignature());
            App app3 = new App();
            app3.setAppName("publicKey");
            app3.setAppValue(jqDto.getPublicKey());
            App app4 = new App();
            app4.setAppName("sm2");
            app4.setAppValue("1");
            App app5 = new App();
            app5.setAppName("digest");
            app5.setAppValue(jqDto.getDigest());

            appList.add(app1);
            appList.add(app2);
            appList.add(app3);
            appList.add(app4);
            appList.add(app5);

            Apps apps = new Apps();
            apps.setApp(appList);
            AppNode appNode = new AppNode();
            appNode.setApps(apps);
            List<AppNode> appNodeList = new ArrayList<>();
            appNodeList.add(appNode);
            AppNodes appNodes = new AppNodes();
            appNodes.setAppNode(appNodeList);


            Notes026 notes026 = new Notes026();
            notes026.setAppNodes(appNodes);
            req.setNotes(notes026);

            PaperlessRequest request = getPaperRequest("*************", req);
            log.error("信贷合同个人加签参数:{}", JsonUtil.object2String(req));
            ResponseFormat<WZH026ResDto> resResponseFormat = jrBankPaperlessService.personalSignContract(request);
            log.error("信贷合同个人加签响应:{}", JsonUtil.object2String(resResponseFormat.getData()));

            if(resResponseFormat.isSuccess()) {
                WZH026Res result = resResponseFormat.getData().getServiceBody().getResponse();
                FileObj fileObj = result.getFileRows().getFileObj();

                PaperlessFkcnJqRes fkcnJqRes = new PaperlessFkcnJqRes();
                fkcnJqRes.setFileName(fileObj.getFileName());
                fkcnJqRes.setFileDigest(fileObj.getFileDigest());
                return Response.success(fkcnJqRes);
            } else {
                return Response.of(resResponseFormat.getReturnCode(), resResponseFormat.getReturnDesc());
            }

        } catch (Exception e) {
            log.error("信贷合同个人加签异常", e);
            return Response.fail();
        }
    }

    public PaperlessRequest getPaperRequest(String serviceId, FileNode fileNode ){
        PaperlessRequest paperlessRequest = new PaperlessRequest();
        ServiceHeader serviceHeader = new ServiceHeader();
        try {
            String ip = InetAddressUtil.getLocalHostLANAddress().getHostAddress();
            NetworkInterface networkInterface = NetworkInterface.getByInetAddress(InetAddressUtil.getLocalHostLANAddress());
            byte[] mac = networkInterface.getHardwareAddress();
            StringBuilder sb = new StringBuilder();
            for(int i = 0; i < mac.length; i++) {
                sb.append(String.format("%02X%s", mac[i], (i < mac.length - 1) ? "-" : ""));
            }
            String macStr = sb.toString();

            serviceHeader.setSystemId("001");
            serviceHeader.setServiceId(serviceId);
            serviceHeader.setClientIp(ip);
            serviceHeader.setDeviceNo(macStr);

        } catch (SocketException e) {
            throw new RuntimeException(e);
        }
        ServiceBody body = new ServiceBody();
        Request request = new Request();
        RequestBody requestBody = new RequestBody();
        requestBody.setFileNode(fileNode);
        request.setRequestBody(requestBody);
        body.setRequest(request);

        paperlessRequest.setServiceHeader(serviceHeader);
        paperlessRequest.setServiceBody(body);

        return paperlessRequest;
    }

    public PaperlessInfo createPaperlessInfo(PaperlessInfo paperlessInfo) {
        String unsignedPath = sftpConfig.getPaperlessBaseUrl() + sftpConfig.getPaperlessUnsigned();
        String signedPath = sftpConfig.getPaperlessBaseUrl() + sftpConfig.getPaperlessSigned();
        paperlessInfo.setUnsignedPath(unsignedPath);
        paperlessInfo.setSignedPath(signedPath);
        paperlessInfoDao.insertPaperlessInfo(paperlessInfo);
        return paperlessInfo;
    }

    public PaperlessInfo findPaperlessInfoByNoOrId(String noOrId) {
        return paperlessInfoDao.selectByNoOrId(noOrId);
    }

    public ReturnCode checkPaperlessUkey(String noOrId, String signedText, String publicKey) {
        if (!checkCreatePaperlessSwitchIsON()) {
            return ReturnCode.CHECK_UKEY_SUCCESS;
        }

        log.error("传入的sign:{}", signedText);
        PaperlessInfo paperlessInfo = paperlessInfoDao.selectByNoOrId(noOrId);
        if(paperlessInfo == null) {
            return ReturnCode.CHECK_UKEY_SUCCESS;
        }

        String waitCompare = org.apache.commons.lang3.StringUtils.substringBetween(signedText, "<UkeyNo>", "</UkeyNo>");
        log.error("需要对比的内容:{}", waitCompare);
        if(org.apache.commons.lang3.StringUtils.equals(paperlessInfo.getCouNo() + "-" + paperlessInfo.getUkeyNo(), waitCompare)) {
            return ReturnCode.CHECK_UKEY_FAIL_SAME_CREATE;
        }
        WXUKEYCXReq req = new WXUKEYCXReq();
        req.setAccountNo(paperlessInfo.getReviewKey());
        ResponseFormat<WXUKEYCXRes> resResponseFormat = jrBankPaperlessService.jrQueryUkeyByAccount(req);
        if(resResponseFormat.isSuccess()) {
            List<WXUKEYResListDetail> details = resResponseFormat.getData().getList().getDetail();
            Boolean compareResult = details.stream().anyMatch(it ->
                org.apache.commons.lang3.StringUtils.equals(paperlessInfo.getCouNo() + "-" + it.getMediaNbr(), waitCompare)
            );
            if(!compareResult) {
                return ReturnCode.CHECK_UKEY_FAIL_NO_MATCH;
            }
        } else {
            return ReturnCode.CHECK_UKEY_FAIL_NO_MATCH;
        }

        try {
            String env = System.getProperty("spring.profiles.active");
            log.error("env:" + env);
            ResourceBundle resourceBundle = ResourceBundle.getBundle("verifySignConfig-prod");
            if("it".equals(env)) {
                resourceBundle = ResourceBundle.getBundle("verifySignConfig-it");
            } else if("dev".equals(env)) {
                resourceBundle = ResourceBundle.getBundle("verifySignConfig-dev");
            }
            log.error("ServerIP:{}", resourceBundle.getString("ServerIP"));
            log.error("开始初始化配置");
            NetSignAgent.initialize(resourceBundle);
            String charsetName = "UTF-8";
            log.error("前端密文内容:{}", signedText);
            int offset = signedText.indexOf(':');
            signedText = signedText.substring(offset + 1);
            byte signatureBytes[] = signedText.getBytes(charsetName);
            int dataLen = Integer.parseInt((new String(signatureBytes, 0, 8)).trim());
            byte dataPartBytes[] = new byte[dataLen];
            System.arraycopy(signatureBytes, 8, dataPartBytes, 0, dataLen);

            int signLen = Integer.parseInt((new String(signatureBytes, 8 + dataLen, 8)).trim());
            String signature = new String(signatureBytes, 8 + dataLen + 8, signLen, charsetName);
            byte signPartBytes[] = hex2Byte(signature);
            log.error("公钥内容:{}", publicKey);
            log.error("公钥通过base64转X509Certificate");
            X509Certificate certificate = (X509Certificate) NetSignAgent.generateCertificate(cn.com.infosec.util.Base64.decode(publicKey));
            log.error("开始验签");
            NetSignAgent.rawVerify(dataPartBytes, signPartBytes, null, certificate);
            log.error("验签成功");

        } catch (Exception e) {
            log.error("验签失败");
            return ReturnCode.CHECK_UKEY_FAIL_SIGN_VERIFICATION;
        }
        return ReturnCode.CHECK_UKEY_SUCCESS;
    }

    private boolean checkCreatePaperlessSwitchIsON() {
        GeneralConfigQueryPara generalConfigQueryPara = new GeneralConfigQueryPara();
        generalConfigQueryPara.setConfigKeys(Arrays.asList(GeneralConfigConstants.CREATE_PAPERLESS_SWITCH));
        Response<List<GeneralConfigPara>> generalConfigParaRes = userFeignClient.queryGeneralConfig(generalConfigQueryPara);
        FeignClientUtil.checkResponse(generalConfigParaRes);
        if (!CollectionUtils.isEmpty(generalConfigParaRes.getData())
                && generalConfigParaRes.getData().get(0) != null &&
                SwitchValueConst.ON.name().equals(generalConfigParaRes.getData().get(0).getConfigValue())) {
            return true;
        }
        log.info("--------------query create paperless switch is off-------------------");
        return false;
    }

    public byte[] hex2Byte(String inbuf) {
        int len = inbuf.length() / 2;
        byte[] outbuf = new byte[len];

        for(int i = 0; i < len; ++i) {
            int byte1 = char2Int(inbuf.charAt(i * 2));
            int byte2 = char2Int(inbuf.charAt(i * 2 + 1));
            outbuf[i] = (byte) (byte1 << 4 | byte2);
        }
        return outbuf;
    }

    public int char2Int(char ch) {
        if(ch >= '0' && ch <= '9') {
            return ch - 48;
        } else if(ch >= 'a' && ch <= 'f') {
            return ch - 97 + 10;
        } else if(ch >= 'A' && ch <= 'F') {
            return ch - 65 + 10;
        }  else {
            throw new RuntimeException("invalid_char");
        }
    }

}
