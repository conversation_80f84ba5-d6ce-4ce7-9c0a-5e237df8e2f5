package com.wxbc.rhine3.service.workflow;

import java.util.Map;

/**
 * rhine3Asset
 *
 * <AUTHOR> ch<PERSON> cheng
 * Date         : 2023/2/2 13:21
 * Jdk          : 1.8
 * Description  :
 */
public interface WorkflowTaskEventListener {


    /**
     * 在创建task之前触发的一个事件
     * @param taskName    即将创建的taskName
     * @param flowKey     流程定义的key
     * @param bizKey      本次执行的业务key
     * @param executionId 本次执行的id
     * @param variables   流程中的variables
     */
    void beforeTask(String taskName, String flowKey, String bizKey, String executionId, Map<String, Object> variables);
}
