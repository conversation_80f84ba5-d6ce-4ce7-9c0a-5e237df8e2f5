package com.wxbc.rhine3.service.refund.sign;

import com.wxbc.cou.manager.api.bean.SplitDetail;
import com.wxbc.rhine3.bean.Protocol;
import com.wxbc.rhine3.bean.User;
import com.wxbc.rhine3.bean.cou.PayCouSignPara;
import com.wxbc.rhine3.bean.refund.RefundApplyPara;
import com.wxbc.rhine3.bean.refund.RefundCouApplyPara;
import com.wxbc.rhine3.constants.FiFunctionEnum;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.service.cou.sign.pay.PayCouSignCommonService;
import com.wxbc.venus.user.dto.AccountUserDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class RlRefundSignService implements RefundSignInterface {

    @Autowired
    private PayCouSignCommonService payCouSignCommonService;

    @Override
    public Map<String, Protocol> signOneFile4transfer(RefundApplyPara refundApplyPara, AccountUserDTO user, String receiveCompanyUuid) throws ParameterException, IOException {
        PayCouSignPara payCouSignPara=new PayCouSignPara();
        List<SplitDetail> splitCouList=new ArrayList<>();
        for(RefundCouApplyPara refundCouApplyParaItem:refundApplyPara.getRefundCouApplyParaList()){
            SplitDetail splitDetailItem=new SplitDetail();
            splitDetailItem.setToCouNo(refundCouApplyParaItem.getReceiveCouNo());
            splitDetailItem.setToCouAmountInCent(refundCouApplyParaItem.getRefundCouAmountInCent());
            splitDetailItem.setFromCouUuid(refundCouApplyParaItem.getRefundCouUuid());
            splitDetailItem.setDueDate(refundCouApplyParaItem.getDueDate());
            splitDetailItem.setChangeCouNo(refundCouApplyParaItem.getChangeCouNo());
            splitCouList.add(splitDetailItem);
        }
        payCouSignPara.setSplitCouList(splitCouList);
        payCouSignPara.setCreditUuid(refundApplyPara.getCreditUuid());
        payCouSignPara.setPublishUuid(refundApplyPara.getPublishUuid());
        payCouSignPara.setReceiverUuid(receiveCompanyUuid);
        payCouSignPara.setLoginUser(user);
        payCouSignPara.setImmediatelySign(true);
        payCouSignPara.setFiFunctionEnum(FiFunctionEnum.FJRL);
        return payCouSignCommonService.paySignProcess(payCouSignPara);
    }

    @Override
    public FiFunctionEnum initInterfaceAndSignatureCallEnum() {
        return FiFunctionEnum.FJRL;
    }
}
