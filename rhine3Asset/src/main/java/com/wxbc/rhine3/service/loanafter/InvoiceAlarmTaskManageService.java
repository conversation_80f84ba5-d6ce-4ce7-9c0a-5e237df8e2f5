package com.wxbc.rhine3.service.loanafter;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.pagehelper.PageInfo;
import com.wxbc.rhine3.bean.User;
import com.wxbc.rhine3.bean.loanafter.CreateInvoiceAlarmTaskReq;
import com.wxbc.rhine3.bean.loanafter.InvoiceAlarmTaskReq;
import com.wxbc.rhine3.bean.loanafter.QueryInvoiceAlarmTaskListRes;
import com.wxbc.rhine3.common.PagePara;
import com.wxbc.rhine3.constants.finance.InvoiceTaskStepEnum;
import com.wxbc.rhine3.constants.loanafter.InvoiceAlarmTaskStatusEnum;
import com.wxbc.rhine3.repository.entity.InvoiceAlarmTaskEntity;
import com.wxbc.rhine3.repository.mapper.InvoiceAlarmTaskDao;
import com.wxbc.rhine3.repository.table.InvoiceAlarmTask;
import com.wxbc.rhine3.service.WaveIdService;
import com.wxbc.venus.user.dto.AccountUserDTO;
import com.wxbc.venus.user.util.UserUtil;
import com.wxbc.rhine3.utils.PageUtil;
import com.wxbc.scaffold.common.definition.enums.CommonStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR> zhengjiao
 * @Date : 2024-03-06 9:38
 * @Description : 发票监控任务管理服务
 */
@Service
@Slf4j
public class InvoiceAlarmTaskManageService {
    @Autowired
    private InvoiceAlarmTaskDao invoiceAlarmTaskDao;
    @Autowired
    private WaveIdService waveIdService;

    public static final String INVOICE_ALARM_TASK_PREFIX = "IAT";

    public PageInfo<QueryInvoiceAlarmTaskListRes> getInvoiceAlarmTaskList(PagePara req){
        AccountUserDTO ooLoginUser = UserUtil.get();
        QueryWrapper<InvoiceAlarmTaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(InvoiceAlarmTask.OPERATING_ORGANIZATION_UUID, ooLoginUser.getCompanyUuid());
        queryWrapper.eq(InvoiceAlarmTask.STATUS, CommonStatus.NORMAL.value());
        queryWrapper.orderByDesc(InvoiceAlarmTask.START_TIME);
        final IPage<InvoiceAlarmTaskEntity> queryPage = PageUtil.requestToIPage(req);
        final IPage<InvoiceAlarmTaskEntity> retPage = invoiceAlarmTaskDao.selectPage(queryPage, queryWrapper);
        return PageUtil.page2Response(retPage, QueryInvoiceAlarmTaskListRes::new);
    }
    public void createInvoiceAlarmTask(CreateInvoiceAlarmTaskReq req){
        AccountUserDTO ooLoginUser = UserUtil.get();
        InvoiceAlarmTaskEntity entity = new InvoiceAlarmTaskEntity();
        entity.setStartTime(req.getStartTime());
        entity.setOperatingOrganizationUuid(ooLoginUser.getCompanyUuid());
        entity.setNextStep(req.getNextStep());
        if(req.getId() != null){
            entity.setId(req.getId());
            invoiceAlarmTaskDao.updateById(entity);
        } else {
            entity.setTaskNo(waveIdService.initNumberByWaveId(INVOICE_ALARM_TASK_PREFIX));
            entity.setTaskStatus(InvoiceAlarmTaskStatusEnum.INIT.getCode());
            entity.setStatus(CommonStatus.NORMAL.value());
            invoiceAlarmTaskDao.insert(entity);
        }

    }

    public void deleteInvoiceAlarmTask(InvoiceAlarmTaskReq req){
        invoiceAlarmTaskDao.update(null, new UpdateWrapper<InvoiceAlarmTaskEntity>()
                .set(InvoiceAlarmTask.STATUS, CommonStatus.DELETED.value())
                .set(InvoiceAlarmTask.TASK_STATUS, InvoiceAlarmTaskStatusEnum.STOP.getCode())
                .set(InvoiceAlarmTask.END_TIME, LocalDateTime.now())
                .eq(InvoiceAlarmTask.ID, req.getId()));
    }

    public void stopInvoiceAlarmTask(InvoiceAlarmTaskReq req){
        invoiceAlarmTaskDao.update(null, new UpdateWrapper<InvoiceAlarmTaskEntity>()
                .set(InvoiceAlarmTask.TASK_STATUS, InvoiceAlarmTaskStatusEnum.STOP.getCode())
                .set(InvoiceAlarmTask.END_TIME, LocalDateTime.now())
                .eq(InvoiceAlarmTask.ID, req.getId()));
    }

    public void deriveInvoiceAlarmTask(Long taskId) {
        InvoiceAlarmTaskEntity preEntity = invoiceAlarmTaskDao.selectById(taskId);
        LocalDateTime startTime = preEntity.getStartTime();
        if (StringUtils.equals(preEntity.getNextStep(), InvoiceTaskStepEnum.DAY_STEP.getVal())) {
            startTime = startTime.plusDays(1);
        } else if (StringUtils.equals(preEntity.getNextStep(), InvoiceTaskStepEnum.WEEK_STEP.getVal())) {
            startTime = startTime.plusWeeks(1);
        } else if (StringUtils.equals(preEntity.getNextStep(), InvoiceTaskStepEnum.MONTH_STEP.getVal())) {
            startTime = startTime.plusMonths(1);
        } else {
            return;
        }
        InvoiceAlarmTaskEntity entity = new InvoiceAlarmTaskEntity();
        entity.setTaskNo(waveIdService.initNumberByWaveId(INVOICE_ALARM_TASK_PREFIX));
        entity.setTaskStatus(InvoiceAlarmTaskStatusEnum.INIT.getCode());
        entity.setStartTime(startTime);
        entity.setOperatingOrganizationUuid(preEntity.getOperatingOrganizationUuid());
        entity.setStatus(CommonStatus.NORMAL.value());
        entity.setNextStep(preEntity.getNextStep());
        invoiceAlarmTaskDao.insert(entity);
    }
}
