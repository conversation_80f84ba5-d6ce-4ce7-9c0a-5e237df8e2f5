package com.wxbc.rhine3.service.settlement;

import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageInfo;
import com.wxbc.cou.manager.api.constant.CouCashStatus;
import com.wxbc.cou.manager.api.domain.Cou;
import com.wxbc.rhine3.bean.Company;
import com.wxbc.rhine3.bean.jxbank.JXSettlementPara;
import com.wxbc.rhine3.bean.response.SettlementHistoryStrandedExportResponse;
import com.wxbc.rhine3.bean.response.SettlementStrandedExportResponse;
import com.wxbc.rhine3.bean.settlement.*;
import com.wxbc.rhine3.constants.ExceptionConstants;
import com.wxbc.rhine3.constants.settlement.SettlementConstants;
import com.wxbc.rhine3.constants.settlement.SettlementStatusEnum;
import com.wxbc.rhine3.constants.settlement.SettlementStrandedStatusEnum;
import com.wxbc.rhine3.convert.SettlementHistoryStrandedExportMapper;
import com.wxbc.rhine3.convert.SettlementStrandedExportMapper;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.repository.mapper.SettlementQueryDao;
import com.wxbc.rhine3.repository.mapper.SettlementStrandedQueryDao;
import com.wxbc.rhine3.service.SarahService;
import com.wxbc.rhine3.service.company.CompanyService;
import com.wxbc.rhine3.service.destroy.DestroyCacheService;
import com.wxbc.rhine3.util.FileUtil;
import com.wxbc.rhine3.util.NumberFormatUtil;
import com.wxbc.venus.user.util.UserUtil;
import com.wxbc.venus.user.dto.OrgExtDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @company: 万向区块链
 * @description 清分异常列表服务
 * @author: lixingxing
 * @create: 2022-09-08 15:12
 **/
@Slf4j
@Service
public class SettlementStrandedService {

    @Autowired
    private SettlementQueryDao settlementQueryDao;

    @Autowired
    private SettlementStrandedQueryDao settlementStrandedQueryDao;

    @Autowired
    private SettlementCommonService settlementCommonService;

    @Autowired
    private Settlement4JxBankService settlement4JxBankService;

    @Autowired
    private DestroyCacheService destroyCacheService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private SarahService sarahService;

    @Autowired
    private SettlementUpdateDbService settlementUpdateDbService;

    public PageInfo<SettlementStranded> querySettlementStrandedList(SettlementStrandedQueryPara settlementStrandedQueryPara) throws ParameterException {
        if (processSearchPublishCompanyName(settlementStrandedQueryPara)) {
            return new PageInfo<>();
        }

        List<SettlementStranded> settlementStrandedResponseList = getStrandedList(settlementStrandedQueryPara);
        return new PageInfo<>(settlementStrandedResponseList);
    }

    public PageInfo<SettlementStranded> querySettlementStrandedHistoryList(SettlementStrandedQueryPara settlementStrandedQueryPara) throws ParameterException {
        if (processSearchPublishCompanyName(settlementStrandedQueryPara)) {
            return new PageInfo<>();
        }

        List<SettlementStranded> historyStrandedList = getHistoryStrandedList(settlementStrandedQueryPara);
        return new PageInfo<>(historyStrandedList);
    }

    private List<SettlementStranded> getStrandedList(SettlementStrandedQueryPara queryPara) throws ParameterException {
        queryPara.setOperatingOrganizationUuid(UserUtil.get().getCompanyUuid());

        List<SettlementStranded> strandedList = settlementStrandedQueryDao.selectListByConditions(queryPara);
        if (CollectionUtils.isEmpty(strandedList)) {
            return strandedList;
        }

        fillCompanyNameAndCashStatus(true, strandedList);

        return strandedList;
    }

    private List<SettlementStranded> getHistoryStrandedList(SettlementStrandedQueryPara queryPara) throws ParameterException {
        queryPara.setOperatingOrganizationUuid(UserUtil.get().getCompanyUuid());

        List<SettlementStranded> historyList = settlementStrandedQueryDao.selectHistoryByConditions(queryPara);
        if (CollectionUtils.isEmpty(historyList)) {
            return historyList;
        }

        fillCompanyNameAndCashStatus(false, historyList);

        return historyList;
    }

    private void fillCompanyNameAndCashStatus(boolean isQueryCouCashStatus, List<SettlementStranded> settlementStrandedResponseList) throws ParameterException {
        Map<String, String> couMap = new HashMap<>();
        if (isQueryCouCashStatus) {
            List<String> couUuids = settlementStrandedResponseList.stream().map(SettlementStranded::getCouUuid).distinct().collect(Collectors.toList());
            List<Cou> couList = sarahService.couListByUuids(couUuids);
            if (CollectionUtils.isEmpty(couList)) {
                throw new ParameterException(ExceptionConstants.SETTLEMENT_STRANDED_DELETE_ERROR);
            }

            couMap = couList.stream().collect(Collectors.toMap(Cou::getUuid, Cou::getCashStatus));

        }

        for (SettlementStranded settlementStranded : settlementStrandedResponseList) {
            settlementStranded.setCouPublishName(companyService.extractCompanyNameByPubKey(settlementStranded.getCouPublishPubKey()));
            settlementStranded.setCouHolderName(companyService.extractCompanyNameByPubKey(settlementStranded.getCouHolderPubKey()));
            settlementStranded.setCouCashStatus(couMap.get(settlementStranded.getCouUuid()));
        }
    }

    public void exportSettlementStrandedList(SettlementStrandedQueryPara settlementStrandedQueryPara, HttpServletResponse response) throws IOException, ParameterException {
        if (processSearchPublishCompanyName(settlementStrandedQueryPara)) {
            FileUtil.downloadExcel(new ArrayList<SettlementStrandedExportResponse>(), response);
        }

        List<SettlementStranded> strandedList = getStrandedList(settlementStrandedQueryPara);

        if (!CollectionUtils.isEmpty(strandedList)) {
            List<SettlementStrandedExportResponse> strandedExportResponses = SettlementStrandedExportMapper.instance.source2TargetList(strandedList);
            FileUtil.downloadExcel(strandedExportResponses, response);
        } else {
            FileUtil.downloadExcel(new ArrayList<SettlementStrandedExportResponse>(), response);
        }

    }

    public void exportSettlementStrandedHistoryList(SettlementStrandedQueryPara settlementStrandedQueryPara, HttpServletResponse response) throws IOException, ParameterException {
        if (processSearchPublishCompanyName(settlementStrandedQueryPara)) {
            FileUtil.downloadExcel(new ArrayList<SettlementStrandedExportResponse>(), response);
        }

        List<SettlementStranded> historyStrandedList = getHistoryStrandedList(settlementStrandedQueryPara);

        if (!CollectionUtils.isEmpty(historyStrandedList)) {
            List<SettlementHistoryStrandedExportResponse> historyList = SettlementHistoryStrandedExportMapper.instance.source2historyList(historyStrandedList);
            FileUtil.downloadExcel(historyList, response);
        } else {
            FileUtil.downloadExcel(new ArrayList<SettlementHistoryStrandedExportResponse>(), response);
        }

    }

    private boolean processSearchPublishCompanyName(SettlementStrandedQueryPara settlementStrandedQueryPara) {
        if (StringUtils.isNotEmpty(settlementStrandedQueryPara.getPublishName())) {
            OrgExtDTO publishCompany = companyService.searchCompanyByFullName(settlementStrandedQueryPara.getPublishName());
            if (publishCompany == null) {
                return true;
            }
            settlementStrandedQueryPara.setCouPublishPubKey(publishCompany.getPubKey());
        }
        return false;
    }

    public PageInfo<Settlement> querySettlementByCoUuid(SettlementCouUuidQueryPara settlementCouUuidQueryPara) {
        List<Settlement> settlementList = settlementQueryDao.selectByCouUuid(settlementCouUuidQueryPara);
        return new PageInfo<>(settlementList);
    }

    public void settlementSendToJxBank(SettlementSendJxBankPara settlementSendJxBankPara) throws ParameterException, ParseException {
        SettlementStranded settlementStranded = settlementStrandedQueryDao.selectListByUuidAndOoUUid(settlementSendJxBankPara.getUuid(), UserUtil.get().getCompanyUuid());
        Cou cou = validSettlementStrandedParaAndGetCou(settlementStranded);
        Settlement waitSettlement = null;
        if (SettlementConstants.SETTLEMENT_STRANDED_SOURCE_ONLINE == settlementStranded.getSource()) {
            Settlement strandedSettlement = settlementQueryDao.selectBySettleNo(settlementStranded.getStrandedSettleNo());
            waitSettlement = settlementCommonService.copySettlementData(strandedSettlement);
        } else {
            waitSettlement = settlementCommonService.newSettlement(cou, settlementStranded.getFinanceFlag());
        }
        JXSettlementPara jxSettlementPara = settlementCommonService.initJxSettlementPara(waitSettlement);
        if (!jxSettlementPara.getSendFlag()) {
            log.error("当前清分数据不满足发送江西银行条件:settleNo:{}", waitSettlement.getSettleNo());
            throw new ParameterException(ExceptionConstants.SETTLEMENT_STRANDED_SEND_ERROR);
        }
        //发送给江西银行
        settlement4JxBankService.sendSettlementDataToJXBank(jxSettlementPara);
        //不管推送成功失败,都下放到查询定时任务里面二次确认
        waitSettlement.setStatus(SettlementStatusEnum.SETTLEMENT_STATUS_PROCESSING.getVal());
        //******** hotfix fix清分日期不能是当前日期
        waitSettlement.setSettleDate(settlementCommonService.genSettleDateByCouAndFinanceFlag(settlementStranded.getFinanceFlag(), cou));
        settlementStranded.setStatus(SettlementStrandedStatusEnum.SETTLEMENT_STATUS_PROCESSING.getVal());
        settlementStranded.setStrandedSettleNo(waitSettlement.getSettleNo());
        settlementUpdateDbService.updateStrandedAndInsertSettlement(settlementStranded, waitSettlement);
        destroyCacheService.cacheCouInDestroyWorkflow(settlementStranded.getCouUuid());
    }


    private Cou validSettlementStrandedParaAndGetCou(SettlementStranded settlementStranded) throws ParameterException {
        if (settlementStranded == null) {
            throw new ParameterException(ExceptionConstants.SETTLEMENT_STRANDED_SEND_ERROR);
        }
        if (destroyCacheService.isCouInDestroyWorkflow(settlementStranded.getCouUuid())) {
            throw new ParameterException(ExceptionConstants.SETTLEMENT_ALREADY_PROCESSING);
        }
        List<Cou> couList = sarahService.couListByUuids(Arrays.asList(settlementStranded.getCouUuid()));
        if (CollectionUtils.isEmpty(couList)) {
            throw new ParameterException(ExceptionConstants.SETTLEMENT_STRANDED_SEND_ERROR);
        }
        if (CouCashStatus.CASH_OK.name().equals(couList.get(0).getCashStatus())) {
            throw new ParameterException(ExceptionConstants.SETTLEMENT_HAS_BEEN_PROCESS_SUCCESS);
        }
        if (CouCashStatus.NO_CASH.name().equals(couList.get(0).getCashStatus())) {
            throw new ParameterException(ExceptionConstants.SETTLEMENT_COU_NOT_LOCK);
        }
        return couList.get(0);
    }

    public void settlementStrandedBackHistory(String settleNo) {
        SettlementStranded settlementStranded = settlementStrandedQueryDao.selectBySettleNo(settleNo);
        if (settlementStranded == null) {
            return;
        }

        settlementUpdateDbService.moveStrandedToHistory(settlementStranded);
        log.info("settlementStrandedBackHistory success settleNo:{}", settleNo);
    }

    @Transactional(rollbackFor = Exception.class)
    public void delSettlementStrandedByUuid(SettlementDelJxBankPara settlementDelJxBankPara) throws ParameterException {
        SettlementStranded settlementStranded = settlementStrandedQueryDao.selectListByUuidAndOoUUid(settlementDelJxBankPara.getUuid(), UserUtil.get().getCompanyUuid());
        if (settlementStranded == null) {
            throw new ParameterException(ExceptionConstants.SETTLEMENT_STRANDED_DELETE_ERROR);
        }
        List<Cou> couList = sarahService.couListByUuids(Collections.singletonList(settlementStranded.getCouUuid()));
        if (CollectionUtils.isEmpty(couList)) {
            throw new ParameterException(ExceptionConstants.SETTLEMENT_STRANDED_DELETE_ERROR);
        }
        if (!CouCashStatus.CASH_OK.name().equals(couList.get(0).getCashStatus())) {
            throw new ParameterException(ExceptionConstants.SETTLEMENT_STRANDED_NO_CASH_OK_DELETE_ERROR);
        }

        settlementUpdateDbService.moveStrandedToHistory(settlementStranded);
    }


    public void clearCouListSettlementStrandedProcess(List<String> couUuids) {
        for (String couUuid : couUuids) {
            destroyCacheService.clearCouInDestroyWorkflow(couUuid);
        }
    }

    public List<SettlementStrandedRemindRes> settlementStrandedRemind() throws ParameterException {
        List<SettlementStrandedRemindRes> settlementStrandedRemindResList=new ArrayList<>();
        OrgExtDTO currCompany=UserUtil.get().getCompany();
        if(currCompany==null){
            log.error("curr user:{} get company info is empty", JSONUtil.toJsonStr(UserUtil.get()));
            throw new ParameterException(ExceptionConstants.SETTLEMENT_REMIND_LIST_ERROR_PLEASE_RETRY_LOGIN);
        }
        List<Settlement> settlementStrandedList = settlementQueryDao.selectSettlementStrandedRemindList(currCompany.getPubKey());
        if (!CollectionUtils.isEmpty(settlementStrandedList)) {
            List<Settlement> distinctSettlementStrandedList =settlementStrandedList.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(p -> p.getCouNo()))), ArrayList::new));
            List<Cou> couList = sarahService.couListByUuids(distinctSettlementStrandedList.stream().map(Settlement::getCouUuid).collect(Collectors.toList()));
            Map<String, Date> couUuidMap = couList.stream().collect(Collectors.toMap(Cou::getUuid, Cou::getDueDate));
            Map<String, String> couCashStatusMap = couList.stream().collect(Collectors.toMap(Cou::getUuid, Cou::getCashStatus));
            //过滤之后再按创建时间排序
            distinctSettlementStrandedList=distinctSettlementStrandedList.stream().sorted(Comparator.comparing(Settlement::getCreateTime).reversed()).collect(Collectors.toList());
            for (Settlement settlement : distinctSettlementStrandedList) {
                try {
                    if(CouCashStatus.CASH_OK.name().equals(couCashStatusMap.get(settlement.getCouUuid()))){
                        continue;
                    }
                    SettlementStrandedRemindRes settlementStrandedRemindRes = new SettlementStrandedRemindRes();
                    settlementStrandedRemindRes.setDueDate(couUuidMap.get(settlement.getCouUuid()));
                    settlementStrandedRemindRes.setCouAmount(NumberFormatUtil.thousandsFormatAnd2f(settlement.getCouAmount()));
                    settlementStrandedRemindRes.setCouNo(settlement.getCouNo());
                    settlementStrandedRemindRes.setCreditCompanyName(companyService.extractCompanyNameByPubKey(settlement.getCouCreditPubKey()));
                    settlementStrandedRemindRes.setHolderCompanyName(companyService.extractCompanyNameByPubKey(settlement.getCouHolderPubKey()));
                    settlementStrandedRemindResList.add(settlementStrandedRemindRes);
                } catch (Exception e) {
                    log.error("process settlement strand remind data error settleNo:{}", settlement.getSettleNo(), e);
                }
            }
        }
        log.info("query settlement stranded remind data.....total:{}", settlementStrandedList.size());
        return settlementStrandedRemindResList;
    }
}
