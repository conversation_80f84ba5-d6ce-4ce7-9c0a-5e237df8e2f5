package com.wxbc.rhine3.service.bank;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.wxbc.base.async.WxAsyncService;
import com.wxbc.base.transaction.WxTransService;
import com.wxbc.base.util.JsonUtil;
import com.wxbc.base.util.StringUtil;
import com.wxbc.rhine3.bean.LoanAuditPara;
import com.wxbc.rhine3.bean.ZdwFeedBack;
import com.wxbc.rhine3.bean.finance.Finance;
import com.wxbc.rhine3.bean.jrbank.JrcbFinanceCou;
import com.wxbc.rhine3.bean.jrbank.JrcbFinanceInvoice;
import com.wxbc.rhine3.bean.jrbank.media.JrMediaAsyncBatchReq;
import com.wxbc.rhine3.bean.jrbank.media.JrMediaAsyncFile;
import com.wxbc.rhine3.bean.jrbank.media.JrMediaFile;
import com.wxbc.rhine3.bean.jrcb.JrbcFinancePara;
import com.wxbc.rhine3.bean.jrcb.LoanNoticeLoanInfo;
import com.wxbc.rhine3.bean.jrcb.LoanNoticePara;
import com.wxbc.rhine3.bean.jrcb.LoanNoticeSignProtocolInfo;
import com.wxbc.rhine3.bean.zdw.RegAttachmentDownloadReqPara;
import com.wxbc.rhine3.config.bank.JiangYinConfig;
import com.wxbc.rhine3.constants.*;
import com.wxbc.rhine3.constants.finance.LoanResultEnum;
import com.wxbc.rhine3.constants.finance.ReceivableTransferNoticeStatusEnum;
import com.wxbc.rhine3.constants.zdw.DelEnum;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.feign.UserFeignClient;
import com.wxbc.rhine3.repository.entity.FinanceProtocolEntity;
import com.wxbc.rhine3.repository.mapper.FinanceQueryDao;
import com.wxbc.rhine3.repository.mapper.ReceivableTransferNoticeDao;
import com.wxbc.rhine3.repository.mapper.ZdCheckTaskExtDao;
import com.wxbc.rhine3.service.MinioService;
import com.wxbc.rhine3.service.OperateLogService;
import com.wxbc.rhine3.service.SarahService;
import com.wxbc.rhine3.service.company.CompanyService;
import com.wxbc.rhine3.service.finance.FinanceUpdateDbService;
import com.wxbc.rhine3.service.transfer.TransferApplicationService;
import com.wxbc.rhine3.service.zdw.reg.ZdwPledgeService;
import com.wxbc.rhine3.service.zdw.terminate.ZdwTerminateRegService;
import com.wxbc.rhine3.util.FileUtil;
import com.wxbc.rhine3.util.TimeUtils;
import com.wxbc.rhine3.utils.FieldUtil;
import com.wxbc.rhine3.utils.ZipUtils;
import com.wxbc.venus.zd.common.enums.TaskStatus;
import com.wxbc.venus.zd.repository.entity.ZdCheckTaskEntity;
import com.wxbc.venus.zd.repository.table.ZdCheckTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.math.RoundingMode;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @company: 万向区块链
 * @description 江阴银行放款通知接口
 * @author: lixingxing
 * @create: 2024-03-21 08:53
 **/
@Service
@Slf4j
public class JRCBLoanService  {

    @Autowired
    private SarahService sarahService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private FinanceQueryDao financeQueryDao;

    @Autowired
    private TransferApplicationService transferApplicationService;

    @Autowired
    private FinanceUpdateDbService financeUpdateDbService;
    @Autowired
    private ZdwTerminateRegService zdwTerminateRegService;

    @Autowired
    private JRCBProtocolService jrcbProtocolService;
    @Autowired
    private ZdwPledgeService zdwPledgeService;
    @Autowired
    private UserFeignClient userFeignClient;

    @Resource
    private JrBankMediaInvokeService jrBankMediaInvokeService;

    @Resource
    private MinioService minioService;

    @Resource
    private ZdCheckTaskExtDao zdCheckTaskExtDao;
    @Resource
    private WxAsyncService wxAsyncService;

    @Autowired
    private OperateLogService operateLogService;
    @Autowired
    private JiangYinConfig jiangYinConfig;

    @Resource
    private WxTransService wxTransService;


    @Autowired
    private ReceivableTransferNoticeDao receivableTransferNoticeDao;

    public void loanNotice(LoanNoticePara jrbcLoanPara) throws Exception {
        String jsonbody=JsonUtil.object2String(jrbcLoanPara);
        log.info("jrcb loan notice sign para:{}", jsonbody);
        md5AndCheckSign(jrbcLoanPara.getSocialCreditCode()+jrbcLoanPara.getApplyNo()+StringUtil.valueOf(jrbcLoanPara.getLoanStatus()), jrbcLoanPara.getSignStr());
        operateLogService.insertOperateLog(OperateLogConst.JRCB_NOTICE_API_URL, jsonbody, OperateLogConst.OPERATE_API_NAME_BY_JRCB_FINANCE_NOTICE,
                OperateLogConst.OPERATE_RECEIVER_4_JRCB, jrbcLoanPara.getApplyNo());
        Finance financeRequest = getFinanceByNumber(jrbcLoanPara.getApplyNo());
        LoanAuditPara loanAuditPara = new LoanAuditPara();
        loanAuditPara.setApplicationNumber(jrbcLoanPara.getApplyNo());
        loanAuditPara.setLoanResult(LoanResultEnum.getEnumByCode(jrbcLoanPara.getLoanStatus()));
        //因为是一个接口多次被调用,每次传过来的值都不一样,所以需要对loaninfo这个字段做并集
        if(financeRequest.getLoanInfo()==null){
            financeRequest.setLoanInfo(JsonUtil.object2String(jrbcLoanPara));
        }else{
            LoanNoticePara jrbcLoanTemp=FieldUtil.mergeBeans(jrbcLoanPara, JsonUtil.jsonStr2Object(financeRequest.getLoanInfo(),LoanNoticePara.class));
            jrbcLoanTemp.setLoanStatus(jrbcLoanPara.getLoanStatus());
            jrbcLoanTemp.setSignStr(jrbcLoanPara.getSignStr());
            jrbcLoanTemp.setApplyNo(jrbcLoanPara.getApplyNo());
            jrbcLoanTemp.setSocialCreditCode(jrbcLoanPara.getSocialCreditCode());
            financeRequest.setLoanInfo(JsonUtil.object2String(jrbcLoanTemp));
        }
        LoanResultEnum loanResultEnum=LoanResultEnum.getEnumByCode(jrbcLoanPara.getLoanStatus());
        switch (loanResultEnum){
            case AGREE_LOAN:
                LoanNoticeLoanInfo loanInfo=jrbcLoanPara.getLoanInfo();
                Assert.notNull(loanInfo, ExceptionConstants.JRCB_PLEDGE_LOAN_NOTICE_STATUS_LOAN_INFO_IS_NULL);
                financeRequest.setDiscount(loanInfo.getPledgeRate()==null?financeRequest.getDiscount():loanInfo.getPledgeRate());
                financeRequest.setFactoringRate(loanInfo.getRate()==null?financeRequest.getFactoringRate():loanInfo.getRate());
                financeRequest.setInputFinanceAmountInYuan(loanInfo.getLoanAmount()==null?
                        financeRequest.getInputFinanceAmountInYuan():loanInfo.getLoanAmount().divide(CommonConst.HUNDRED, 2, RoundingMode.DOWN));
                loanAuditPara.setFinanceValueDate(loanInfo.getValueDate());
                loanAuditPara.setFinanceDueDate(loanInfo.getDueDate());
                financeRequest.setStatus(FinanceStatus.CONFIRM.name());
                financeLoanDBUpdate(financeRequest, loanAuditPara);
                break;
            case REJECT_LOAN:
                financeRequest.setStatus(FinanceStatus.REJECT.name());
                loanAuditPara.setComment(jrbcLoanPara.getRejectInfo()==null?"":jrbcLoanPara.getRejectInfo().getRejectReason());
                //江阴融资放款时秩序拒绝放款需要改变cou相关状态,放款成功cou还是质押汇总
                sarahService.auditCou(financeRequest.getTransferUuid(), companyService.extractCompanyPubKeyByUuid(financeRequest.getFiUuid()),
                        LoanResultEnum.AGREE_LOAN.equals(loanAuditPara.getLoanResult()),false);
                // 拒绝放款时自动注销中登网登记
                zdwTerminateRegService.zdwTerminateAuto(financeRequest);
                //释放供应商额度
                userFeignClient.updateQuota(financeRequest.getApplicantUuid(), financeRequest.getFiUuid(),
                        financeRequest.getInputFinanceAmountInYuan().multiply(CommonConst.HUNDRED), UpdateQuotaOptType.ROLL_FROZEN,2);
                releaseFinanceAssetAndInvoice(financeRequest, false);
                break;
            case REPAYMENT_LOAN:
                financeRequest.setStatus(FinanceStatus.REPAID.name());
                //已经还款=需要释放融信(相当于拒绝接受融信逻辑)
                sarahService.auditCou(financeRequest.getTransferUuid(), companyService.extractCompanyPubKeyByUuid(financeRequest.getFiUuid()),
                        false,false);
                // 还款时自动注销中登网登记
                zdwTerminateRegService.zdwTerminateAuto(financeRequest);
                //释放供应商额度
                userFeignClient.updateQuota(financeRequest.getApplicantUuid(), financeRequest.getFiUuid(),
                        financeRequest.getInputFinanceAmountInYuan().multiply(CommonConst.HUNDRED), UpdateQuotaOptType.ROLL_FROZEN,2);
                //无需释放发票只需要修改状态和释放融信
                financeUpdateDbService.updateStatusByUuid(financeRequest);
                break;
            case SIGNED_LOAN:
                financeRequest.setStatus(FinanceStatus.WAIT_CONFIRM.name());
                LoanNoticeSignProtocolInfo signedInfo=jrbcLoanPara.getSignFileInfo();
                Assert.notNull(signedInfo, ExceptionConstants.JRCB_PLEDGE_LOAN_NOTICE_STATUS_SIGN_INFO_IS_NULL);
                Assert.notNull(jrbcLoanPara.getCompanyInfo(), ExceptionConstants.JRCB_PLEDGE_LOAN_NOTICE_STATUS_COMPANY_INFO_IS_NULL);
                financeRequest.setInputFinanceAmountInYuan(signedInfo.getPledgeAmount()==null
                        ?financeRequest.getInputFinanceTransferAmountInYuan():signedInfo.getPledgeAmount().divide(CommonConst.HUNDRED, 2, RoundingMode.DOWN));
                // 更新通知为可见状态
                receivableTransferNoticeDao.updateStatusByFinanceNumber(financeRequest.getApplicationNumber(), ReceivableTransferNoticeStatusEnum.VISIBLE.getVal());
                jrcbProtocolService.saveJRCBProtocolAndUpdateFinance(signedInfo,financeRequest);
                //先保存推送过来的协议数据，合同到期日需要给中登登记，数据库不保存
                financeRequest.setFinanceValueDate(signedInfo.getBorrowBeginDate()==null
                        ?financeRequest.getFinanceValueDate():TimeUtils.getFormatDate(signedInfo.getBorrowBeginDate(),TimeUtils.DATA_FORMAT_YYYY_MM_DD));
                financeRequest.setFinanceDueDate(signedInfo.getBorrowEndDate()==null
                        ?financeRequest.getFinanceDueDate():TimeUtils.getFormatDate(signedInfo.getBorrowEndDate(),TimeUtils.DATA_FORMAT_YYYY_MM_DD));
                //发起中登登记
                ZdwFeedBack zdwFeedBack = zdwPledgeService.checkExistOrRegister(financeRequest, true);
                //调用银行影像文件接口
                try {
                    sendImageList2JRCB(zdwFeedBack, financeRequest,signedInfo.getBorrowContractNo());
                }catch (Exception e){
                    log.error("finance no:{} send images error",financeRequest.getApplicationNumber(),e);
                }
                break;
            default:
                throw new ParameterException(ExceptionConstants.JRCB_PLEDGE_LOAN_NOTICE_STATUS_ERROR);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void rejectFinance(Finance financeRequest) throws Exception {
        //江阴融资放款时秩序拒绝放款需要改变cou相关状态,放款成功cou还是质押汇总
        sarahService.auditCou(financeRequest.getTransferUuid(), companyService.extractCompanyPubKeyByUuid(financeRequest.getFiUuid()),
                false,false);
        // 拒绝放款时自动注销中登网登记
        zdwTerminateRegService.zdwTerminateAuto(financeRequest);
        //释放供应商额度
        userFeignClient.updateQuota(financeRequest.getApplicantUuid(), financeRequest.getFiUuid(),
                financeRequest.getInputFinanceAmountInYuan().multiply(CommonConst.HUNDRED), UpdateQuotaOptType.ROLL_FROZEN,2);
        releaseFinanceAssetAndInvoice(financeRequest, false);
    }


    public void financeLoanDBUpdate(Finance financeRequest, LoanAuditPara loanAuditPara) {
        wxTransService.invokeWithinTransaction(() -> {
            financeUpdateDbService.confirmFinanceSetDueDate(loanAuditPara, financeRequest);
            transferApplicationService.updateTransferApplicationInfo(true, "", financeRequest.getTransferUuid());
        });
    }

    private void md5AndCheckSign(String data, String sign) throws ParameterException {
        String signStr = SecureUtil.md5(data);
        if (!signStr.equals(sign)) {
            log.error("md5 sign verify fail");
            throw new ParameterException(ExceptionConstants.SIGN_FAIL);
        }
    }

    public Finance getFinanceByNumber(String financeApplicationNumber) throws ParameterException {
        Finance financeRequest = financeQueryDao.getFinanceRequestByApplicationNumber(financeApplicationNumber);
        if (null == financeRequest) {
            log.error("financeLoanAudit param is error! financeRequest NOT FOUND! " +
                    "financeApplicationNumber={}", financeApplicationNumber);
            throw new ParameterException(ExceptionConstants.DATA_NOT_FOUND);
        }
        return financeRequest;
    }


    // 和workflow需要在同一个事务中
    public void releaseCouAndInvoiceInfo(Finance finance,boolean financeCancel) throws ParameterException {

        releaseFinanceAssetAndInvoice(finance,financeCancel);

        String fiPubKey = companyService.extractCompanyPubKeyByUuid(finance.getFiUuid());
        sarahService.auditCou(finance.getTransferUuid(), fiPubKey,
                false,false);
    }

    public void releaseFinanceAssetAndInvoice(Finance finance,boolean financeCancel) {
        wxTransService.invokeWithinTransaction(() -> {
            if (financeCancel) {
                finance.setStatus(FinanceStatus.CANCELED.name());
                financeUpdateDbService.rejectJRCBFinanceReleaseAssetAndInvoice(finance);
            } else {
                finance.setStatus(FinanceStatus.REJECT.name());
                financeUpdateDbService.rejectJRCBFinanceReleaseAssetAndInvoice(finance);
            }
            transferApplicationService.updateTransferApplicationInfo(false, finance.getAuditReason(), finance.getTransferUuid());
        });
    }

    public void sendJRCBFinanceImages(String applicationNumber) throws Exception {
        Finance financeRequest = getFinanceByNumber(applicationNumber);
        ZdwFeedBack zdwFeedBack=zdwPledgeService.checkExistOrRegister(financeRequest,false);
        sendImageList2JRCB(zdwFeedBack,financeRequest,null);
    }

    public void sendImageList2JRCB(ZdwFeedBack zdwFeedBack,Finance finance,String borrowContractNo) throws Exception {
        //调用接口推送数据至银行影像文件接口
        JrbcFinancePara jrbcFinancePara = JsonUtil.jsonStr2Object(finance.getFinanceExtInfo(),JrbcFinancePara.class );
        //借款合同
        if(StringUtil.isEmpty(borrowContractNo)){
            FinanceProtocolEntity financeProtocol = jrcbProtocolService.queryByApplicationNoAndType(finance.getApplicationNumber(), ProtocolTypeEnum.PROTOCOL_PLEDGE_FINANCE.getVal());
            if(Objects.isNull(financeProtocol)){
                log.warn("质押融资 : {} 关联的借款合同为空,无法发送到影像文件",finance.getApplicationNumber());
                return;
            }
            borrowContractNo=financeProtocol.getProtocolNo();
        }

        JrMediaAsyncBatchReq req = new JrMediaAsyncBatchReq();
        List<JrMediaFile> mediaFileList = new ArrayList<>();
        //中登登记文件
        JrMediaFile zdRegFile = genZdRegFile(finance,zdwFeedBack);
        if(Objects.nonNull(zdRegFile)){
            log.info("质押融资中登登记文件名称: {}",zdRegFile.getFileName());
            mediaFileList.add(zdRegFile);
        }
        //最新的查重文件
        JrMediaFile zdCheckFile = genZDCheckFile(finance);
        if(Objects.nonNull(zdCheckFile)){
            log.info("质押融资中登查重文件名称: {}",zdCheckFile.getFileName());
            mediaFileList.add(zdCheckFile);
        }
        //发票文件
        List<JrMediaFile> invoiceFiles = genInvoiceFile(jrbcFinancePara);
        log.info("质押融资发票文件名称: {}",invoiceFiles.stream().map(JrMediaFile::getFileName).collect(Collectors.toList()));
        mediaFileList.addAll(invoiceFiles);
        //COU文件
        List<JrMediaFile> couFiles = genCouFile(jrbcFinancePara);
        mediaFileList.addAll(couFiles);
        log.info("质押融资COU文件名称: {}",couFiles.stream().map(JrMediaFile::getFileName).collect(Collectors.toList()));


        List<String> pictureExtensions = Lists.newArrayList("jpg", "png","jpeg");

        List<JrMediaAsyncFile> asyncFileList = mediaFileList.stream()
                .filter(f -> Objects.nonNull(f)&&StringUtils.isNotBlank(f.getFile()))
                .map(f -> {
                    JrMediaAsyncFile jrMediaAsyncFile = new JrMediaAsyncFile();
                    jrMediaAsyncFile.setFile(f.getFile());
                    jrMediaAsyncFile.setFileName(f.getFileName());
                    // 图片和文件使用不同的treeId
                    if(pictureExtensions.contains(FilenameUtils.getExtension(f.getFileName()).toLowerCase())){
                        jrMediaAsyncFile.setTreeId(jiangYinConfig.getPicTreeId());
                    }else{
                        jrMediaAsyncFile.setTreeId(jiangYinConfig.getMediaTreeId());
                    }
                    return jrMediaAsyncFile;
                })
                .collect(Collectors.toList());

        req.setList(asyncFileList);
        // 借款合同编号
        String borrowNo = borrowContractNo.replaceAll("[^A-Za-z0-9]","");
        req.setBuzzId(borrowNo);
        // 融资申请方社会信用代码
        req.setCusId(jrbcFinancePara.getFinanceOrganizationCode());
        req.setCusName(companyService.extractCompanyNameByScc(jrbcFinancePara.getFinanceOrganizationCode()));
        // 异步发送
        wxAsyncService.asyncExecute(() -> {
            log.info("发送影像平台的列表数据为: {}",JsonUtil.object2String(asyncFileList.stream().map(f -> {
                JrMediaAsyncFile jrMediaAsyncFile = new JrMediaAsyncFile();
                jrMediaAsyncFile.setFileName(f.getFileName());
                jrMediaAsyncFile.setTreeId(f.getTreeId());
                return jrMediaAsyncFile;
            }).collect(Collectors.toList())));

            jrBankMediaInvokeService.jrMediaAsyncBatchSend(req);
        });
    }

    /**
     * 中登查重文件
     * @param finance
     * @return
     */
    private JrMediaFile genZDCheckFile(Finance finance) {
        //最新的查重文件
        try{
            QueryWrapper<ZdCheckTaskEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(ZdCheckTask.BIZ_UUID,finance.getUuid());
            queryWrapper.eq(ZdCheckTask.IS_DEL, DelEnum.NORMAL.getCode());
            queryWrapper.eq(ZdCheckTask.STATUS, TaskStatus.DONE.getCode());
            queryWrapper.isNotNull(ZdCheckTask.CHECK_REPORT_FILE);
            queryWrapper.orderByDesc(ZdCheckTask.CREATE_TIME);
            queryWrapper.last(" limit 1");
            ZdCheckTaskEntity zdCheckTask = zdCheckTaskExtDao.selectOne(queryWrapper);
            if(Objects.nonNull(zdCheckTask)){
                String minioUrl = zdCheckTask.getCheckReportFile();
                String fileName = minioUrl.substring(minioUrl.lastIndexOf("/")+1);
                File file = minioService.downloadTempFile(zdCheckTask.getCheckReportFile());
                return convertToJrMediaFile(file,fileName);
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("genZDCheckFile error",e);
        }
        return  null;
   }

    /**
     * 中登登记文件
     * @param finance
     * @param zdwFeedBack
     * @return
     * @throws Exception
     */
   private JrMediaFile genZdRegFile(Finance finance,ZdwFeedBack zdwFeedBack) throws Exception {
       try{
           RegAttachmentDownloadReqPara regAttachmentDownloadReqPara=new RegAttachmentDownloadReqPara();
           regAttachmentDownloadReqPara.setRegisterNo(zdwFeedBack.getZdwRegNo());
           //中登网登记证明文件
           byte[] zdwRegFileZip = zdwPledgeService.registrationAttachmentDownload(regAttachmentDownloadReqPara);
           byte[] zdwRegFilePdf = ZipUtils.readZipFileByNameAndSuffix(zdwRegFileZip,zdwFeedBack.getZdwRegNo(),CommonConst.FILE_PDF_SUFFIX);
           File regFile = transferFile(zdwRegFilePdf,zdwFeedBack.getZdwRegNo());
           return convertToJrMediaFile(regFile,zdwFeedBack.getZdwRegNo() + CommonConst.FILE_PDF_SUFFIX);
       }catch (Exception e){
           e.printStackTrace();
           log.error("获取中登登记文件失败",e);
       }
       return null;
   }

    /**
     * 中登登记文件
     * @param zdwRegFile
     * @param fileName
     * @return
     */
    private File transferFile(byte[] zdwRegFile, String fileName) {
        File file =  null;
        try{
            file = File.createTempFile(fileName,CommonConst.FILE_PDF_SUFFIX);

            try(FileOutputStream fos = new FileOutputStream(file)){
                fos.write(zdwRegFile);
            }
        }catch (Exception e){
            log.error("生成中登登记文件失败",e);
        }
        return file;
    }



    /**
     *  转化为要发送的对象
     * @param file
     * @return
     */
    private JrMediaFile convertToJrMediaFile(File file,String fileName) {
        if(Objects.isNull(file)){
            return null;
        }
        JrMediaFile jrMediaFile = new JrMediaFile();
        try{
            URLConnection urlConnection = file.toURI().toURL().openConnection();
            String contentType = urlConnection.getContentType();
            jrMediaFile.setFileName(fileName);
            //文件的base64
            byte[] data = FileUtil.readBytes(file);
            if(Objects.nonNull(data) && data.length > 0){
                String base64 = Base64Encoder.encode(data);
                jrMediaFile.setFile("data:" + contentType + ";base64," + base64);
            }else{
                log.error("读取文件: {}的byte为空", fileName);
            }
        } catch (Exception e) {
            log.error("文件: {}转化为Base64失败",fileName,e);
        }
        return jrMediaFile;
    }


    /**
     * 生成发票的base64文件
     * @param jrbcFinancePara
     * @return
     */
    private List<JrMediaFile>  genInvoiceFile(JrbcFinancePara jrbcFinancePara){
        List<JrcbFinanceInvoice> invoiceList = new ArrayList<>();
        List<JrcbFinanceInvoice> financeInvoiceList = jrbcFinancePara.getFinanceInvoiceList();
        List<JrcbFinanceInvoice> klInvoiceList = jrbcFinancePara.getKlInvoiceList();
        invoiceList.addAll(financeInvoiceList);
        invoiceList.addAll(klInvoiceList);
        List<JrMediaFile> jrMediaFileList = new ArrayList<>();
        invoiceList.stream().distinct().forEach(jrcbFinanceInvoice -> {
            String minioUrl = jrcbFinanceInvoice.getInvoiceFile();
            String fileName = minioUrl.substring(minioUrl.lastIndexOf("/")+1);
            File file = minioService.downloadTempFile(jrcbFinanceInvoice.getInvoiceFile());
            JrMediaFile jrMediaFile = convertToJrMediaFile(file,fileName);
            jrMediaFileList.add(jrMediaFile);
        });
        return jrMediaFileList;
    }


    /**
     * 处理COU中的文件
     * @param jrbcFinancePara
     * @return
     */
    private List<JrMediaFile> genCouFile(JrbcFinancePara jrbcFinancePara){
        List<JrMediaFile>  jrMediaFileList = new ArrayList<>();
        List<JrcbFinanceCou> financeCouList = jrbcFinancePara.getFinanceCouList();
        List<String> fileUrlList = new ArrayList<>();
        //付款承诺函  originCouPromiseFile
        //transferFile 融信流转单
        //融信流转路径 couTransferTraceImg
        if(!CollectionUtils.isEmpty(financeCouList)){
            for(JrcbFinanceCou jrcbFinanceCou : financeCouList){
                if(StringUtils.isNotBlank(jrcbFinanceCou.getOriginCouPromiseFile())){
                    String[] originCouPromiseFiles = jrcbFinanceCou.getOriginCouPromiseFile().split("@");
                    fileUrlList.addAll(Arrays.asList(originCouPromiseFiles));
                }
                if(StringUtils.isNotBlank(jrcbFinanceCou.getTransferFile())){
                    String[] transferFiles = jrcbFinanceCou.getTransferFile().split("@");
                    fileUrlList.addAll(Arrays.asList(transferFiles));
                }
                if(StringUtils.isNotBlank(jrcbFinanceCou.getContractFile())){
                    String[] couContranctFiles = jrcbFinanceCou.getContractFile().split("@");
                    fileUrlList.addAll(Arrays.asList(couContranctFiles));
                }
                if(StringUtils.isNotBlank(jrcbFinanceCou.getOriginCouContractFile())){
                    String[] originCouContranctFiles = jrcbFinanceCou.getContractFile().split("@");
                    fileUrlList.addAll(Arrays.asList(originCouContranctFiles));
                }
            }
        }
        fileUrlList
                .stream()
                .filter(StringUtils::isNotBlank)
                .distinct()
                .forEach(minioUrl -> {
                    String fileName = minioUrl.substring(minioUrl.lastIndexOf("/")+1);
                    File file = minioService.downloadTempFile(minioUrl);
                    JrMediaFile jrMediaFile = convertToJrMediaFile(file,fileName);
                    jrMediaFileList.add(jrMediaFile);
                });
        return jrMediaFileList;
    }




}
