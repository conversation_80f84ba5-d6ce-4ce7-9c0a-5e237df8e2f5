package com.wxbc.rhine3.service.destroy;

import com.wxbc.rhine3.constants.RedisConst;
import com.wxbc.rhine3.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DestroyCacheService {

    @Autowired
    private RedisService redisService;

    public void cacheCouInDestroyWorkflow(String couUuid){
        if (StringUtils.isEmpty(couUuid)) {
            log.error("cacheCouInDestroyWorkflow error! couUuid is empty!");
            return;
        }

        String redisKey = RedisConst.DESTROY_PRE + couUuid;

        try {
            redisService.setValue(redisKey, couUuid);
        } catch (Exception e) {
            log.error("cacheCouInDestroyWorkflow", e);
        }
    }

    public void clearCouInDestroyWorkflow(String couUuid){
        if (StringUtils.isEmpty(couUuid)) {
            log.error("clearCouInDestroyWorkflow error! couUuid is empty!");
            return;
        }

        String redisKey = RedisConst.DESTROY_PRE + couUuid;
        redisService.delete(redisKey);
    }

    public Boolean isCouInDestroyWorkflow(String couUuid){
        String redisKey = RedisConst.DESTROY_PRE + couUuid;
        return StringUtils.isNotBlank(redisService.getValue(redisKey));
    }
}
