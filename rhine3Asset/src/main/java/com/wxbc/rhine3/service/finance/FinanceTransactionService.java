package com.wxbc.rhine3.service.finance;

import com.wxbc.rhine3.bean.FinancePlatformFeeProtocolInnerPara;
import com.wxbc.rhine3.bean.ReceivableTransferNotice;
import com.wxbc.rhine3.bean.TransferApplication;
import com.wxbc.rhine3.bean.finance.Finance;
import com.wxbc.rhine3.constants.InvoiceTradeType;
import com.wxbc.rhine3.constants.UpdateQuotaOptType;
import com.wxbc.rhine3.feign.UserFeignClient;
import com.wxbc.rhine3.repository.entity.FinanceProtocolEntity;
import com.wxbc.rhine3.repository.mapper.FinanceProtocolDao;
import com.wxbc.rhine3.repository.mapper.ProtocolPlatformFeeSignDao;
import com.wxbc.rhine3.repository.mapper.ProtocolPlatformFeeSignExtDao;
import com.wxbc.rhine3.repository.mapper.ReceivableTransferNoticeDao;
import com.wxbc.rhine3.service.bank.JRCBProtocolService;
import com.wxbc.rhine3.service.invoice.TradeInvoiceService;
import com.wxbc.rhine3.service.transfer.TransferApplicationUpdateDbService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

//融资需要事务包裹的代码
@Service
public class FinanceTransactionService {

    @Autowired
    private TransferApplicationUpdateDbService transferApplicationUpdateDbService;

    @Autowired
    private FinanceUpdateDbService financeUpdateDbService;

    @Autowired
    private TradeInvoiceService tradeInvoiceService;

    @Autowired
    private ReceivableTransferNoticeDao receivableTransferNoticeDao;

    @Autowired
    private ProtocolPlatformFeeSignDao protocolPlatformFeeSignDao;

    @Autowired
    private ProtocolPlatformFeeSignExtDao protocolPlatformFeeSignExtDao;

    @Autowired
    private JRCBProtocolService jrcbProtocolService;

    @Transactional(rollbackFor = Exception.class)
    public void batchInsertFinanceCreate(Finance financePara4Db, List<TransferApplication> transferApplications,
                                         List<ReceivableTransferNotice> receivableTransferNotices,
                                         FinancePlatformFeeProtocolInnerPara financePlatformFeeProtocolInnerPara) {
        transferApplicationUpdateDbService.batchInsertTransferApplication(transferApplications);
        financeUpdateDbService.insertFinanceRequest(financePara4Db);
        financeUpdateDbService.batchInsertFinanceAsset(financePara4Db.getUuid(), financePara4Db.getAssetList(), financePara4Db.getAssetType());
        tradeInvoiceService.batchInsertTradeInvoice(financePara4Db.getUuid(), financePara4Db.getInvoiceList(), InvoiceTradeType.FINANCE.getCode());
        receivableTransferNoticeDao.batchInsertReceivableTransferNotice(receivableTransferNotices);
        if(financePlatformFeeProtocolInnerPara!=null
                &&financePlatformFeeProtocolInnerPara.getProtocolPlatformFeeSignEntity()!=null
                &&financePlatformFeeProtocolInnerPara.getFirstSignEntity()!=null
                &&financePlatformFeeProtocolInnerPara.getSecondSignEntity()!=null){
            protocolPlatformFeeSignDao.insert(financePlatformFeeProtocolInnerPara.getProtocolPlatformFeeSignEntity());
            protocolPlatformFeeSignExtDao.insert(financePlatformFeeProtocolInnerPara.getFirstSignEntity());
            protocolPlatformFeeSignExtDao.insert(financePlatformFeeProtocolInnerPara.getSecondSignEntity());
        }
    }


    /**
     * 保存transfer_application，finance_application,finance_asset,
     * finance_invoice,receivable_transfer_notice,finance_protocol以及fee相关的表
     * @param financePara4Db
     * @param transferApplications
     * @param receivableTransferNotices
     * @param financePlatformFeeProtocolInnerPara
     * @param pledgeFinanceFileList
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertPledgeFinanceCreate(Finance financePara4Db, List<TransferApplication> transferApplications,
                                         List<ReceivableTransferNotice> receivableTransferNotices,
                                         FinancePlatformFeeProtocolInnerPara financePlatformFeeProtocolInnerPara,List<FinanceProtocolEntity> pledgeFinanceFileList) {
        transferApplicationUpdateDbService.batchInsertTransferApplication(transferApplications);
        financeUpdateDbService.insertFinanceRequest(financePara4Db);
        financeUpdateDbService.batchInsertFinanceAsset(financePara4Db.getUuid(), financePara4Db.getAssetList(), financePara4Db.getAssetType());
        tradeInvoiceService.batchInsertTradeInvoice(financePara4Db.getUuid(), financePara4Db.getInvoiceList(), InvoiceTradeType.FINANCE.getCode());
        receivableTransferNoticeDao.batchInsertReceivableTransferNotice(receivableTransferNotices);
//        jrcbProtocolService.saveBatch(pledgeFinanceFileList);
        if(financePlatformFeeProtocolInnerPara!=null
                &&financePlatformFeeProtocolInnerPara.getProtocolPlatformFeeSignEntity()!=null
                &&financePlatformFeeProtocolInnerPara.getFirstSignEntity()!=null
                &&financePlatformFeeProtocolInnerPara.getSecondSignEntity()!=null){
            protocolPlatformFeeSignDao.insert(financePlatformFeeProtocolInnerPara.getProtocolPlatformFeeSignEntity());
            protocolPlatformFeeSignExtDao.insert(financePlatformFeeProtocolInnerPara.getFirstSignEntity());
            protocolPlatformFeeSignExtDao.insert(financePlatformFeeProtocolInnerPara.getSecondSignEntity());
        }
    }



}
