package com.wxbc.rhine3.service.cfca;

import com.wxbc.rhine3.bean.protocol.ProtocolSignPosition;
import com.wxbc.rhine3.constants.FiFunctionEnum;
import com.wxbc.rhine3.constants.ProtocolTypeEnum;
import com.wxbc.rhine3.constants.bank.RlBankConfigPara;
import com.wxbc.rhine3.constants.sign.SignPositionOrderEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CommitmentRlPosition implements SignPositionInterface{
    @Autowired
    private RlBankConfigPara rlPara;
    @Override
    public String init() {
        return ProtocolTypeEnum.PROTOCOL_COMM.getVal() + FiFunctionEnum.FJRL.getVal();
    }

    @Override
    public ProtocolSignPosition getPosition(SignPositionOrderEnum signOrder) {
        if (SignPositionOrderEnum.ONE.equals(signOrder)) {
            return ProtocolSignPosition.builder()
                    .signatureType(rlPara.getRlCommitTypeOne())
                    .signatureCoordinateX(rlPara.getRlCommitCoordinateX())
                    .signatureCoordinateY(rlPara.getRlCommitCoordinateY())
                    .signatureKeyWord(rlPara.getRlCommitKeywordOne())
                    .signaturePageIndex(rlPara.getRlCommitPageIndex())
                    .needBase64Flag(false).addDateMark(false)
                    .build();
        }

        if (SignPositionOrderEnum.TWO.equals(signOrder)) {
            return ProtocolSignPosition.builder()
                    .signatureType(rlPara.getRlCommitTypeTwo())
                    .signatureCoordinateX(rlPara.getRlCommitOffSetX())
                    .signatureCoordinateY(rlPara.getRlCommitOffSetY())
                    .signatureKeyWord(rlPara.getRlCommitKeywordTwo())
                    .signaturePageIndex(rlPara.getRlCommitPageIndex())
                    .needBase64Flag(false).addDateMark(false)
                    .build();
        }

        return null;
    }
}