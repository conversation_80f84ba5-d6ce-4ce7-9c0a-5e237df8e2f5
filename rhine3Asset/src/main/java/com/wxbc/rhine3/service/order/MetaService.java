package com.wxbc.rhine3.service.order;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.wxbc.base.util.BeanUtil;
import com.wxbc.rhine3.common.constant.EntityType;
import com.wxbc.rhine3.common.constant.FieldType;
import com.wxbc.rhine3.model.meta.req.MetaAddReq;
import com.wxbc.rhine3.model.meta.req.MetaItemAddReq;
import com.wxbc.rhine3.model.meta.res.MetaRes;
import com.wxbc.rhine3.model.meta.res.MetaWithItemRes;
import com.wxbc.rhine3.model.order.dto.MetaDTO;
import com.wxbc.rhine3.model.order.dto.MetaItemDTO;
import com.wxbc.rhine3.model.order.vo.res.FieldTypeRes;
import com.wxbc.rhine3.repository.entity.MetaEntity;
import com.wxbc.rhine3.repository.mapper.MetaDao;
import com.wxbc.rhine3.repository.table.Meta;
import com.wxbc.scaffold.common.definition.enums.CommonStatus;
import com.wxbc.scaffold.common.definition.exception.BizException;
import com.wxbc.scaffold.common.definition.response.ReturnCode;
import com.wxbc.wave.WaveId;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * financing-service
 *
 * <AUTHOR> chen cheng
 * Date         : 2022/11/22 17:57
 * Jdk          : 1.8
 * Description  :
 */
@Service
@Slf4j
public class MetaService {

    @Resource
    private MetaDao dao;

    @Resource
    private MetaItemService itemService;

    @Resource
    private WaveId waveId;

    private static final List<FieldTypeRes> fieldTypeResList = Lists.newArrayList();

    static {
        for(FieldType f: FieldType.values()) {
            FieldTypeRes fr = new FieldTypeRes();
            fr.setDesc(f.getDesc());
            fr.setFieldType(f.value());
            fieldTypeResList.add(fr);
        }
    }

    public List<MetaRes> query() {
        final List<MetaEntity> entityList = dao.selectList(new QueryWrapper<>());
        final List<MetaRes> metaResList = BeanUtil.copyList(entityList, MetaRes::new);
        metaResList.forEach(m -> {
            final EntityType e = EntityType.of(m.getEntityType());
            m.setEntityTypeName(e.getDesc());
        });
        return metaResList;
    }

    public boolean addMeta(MetaAddReq req, Long accountId) {
        MetaEntity entity = new MetaEntity();
        BeanUtil.copyProperties(req, entity);
        entity.setId(waveId.nextId());
        entity.setCreatorId(accountId);
        dao.insert(entity);
        itemService.addMetaItem(req.getFieldList(), entity.getId());
        return true;
    }


    public boolean addMetaItem(MetaItemAddReq req) {
        final MetaEntity entity = dao.selectById(req.getMetaId());
        if (entity == null) {
            log.error("not found meta with id:{}", req.getMetaId());
            throw new BizException(ReturnCode.UNI_PARAMETER_FAILED);
        }
        itemService.addMetaItem(req.getFieldList(), req.getMetaId());
        return true;
    }

    public List<FieldTypeRes> queryFieldTypes() {
        return fieldTypeResList;
    }


    public MetaWithItemRes queryMetaItem(Long id) {
        QueryWrapper<MetaEntity> query = new QueryWrapper<>();

        query.eq(Meta.ID, id)
                .eq(Meta.STATUS, CommonStatus.NORMAL.value());

        final MetaEntity entity = dao.selectOne(query);
        if (entity == null) {
            log.error("no meta data found for:{}", id);
            throw new BizException(ReturnCode.UNI_PARAMETER_FAILED);
        }
        MetaWithItemRes res = new MetaWithItemRes();
        BeanUtil.copyProperties(entity, res);
        res.setFieldList(itemService.queryMetaItem(entity.getId()));
        return res;
    }


    /**
     * 查询订单元数据
     * @param companyId 所属公司id
     * @param tenantId  租户
     * @return List
     */
    public MetaDTO queryOrderMeta(String companyId, String tenantId) {
        return findMatchOne(companyId, tenantId, EntityType.ORDER);
    }

    /**
     * 查询融资的元数据
     * @param companyId 所属公司
     * @param tenantId  所属租户
     * @return MetaDTO
     */
    public MetaDTO queryFinanceMeta(String companyId, String tenantId) {
        return findMatchOne(companyId, tenantId, EntityType.FINANCE);
    }



    private MetaDTO findMatchOne(String companyId, String tenantId, EntityType entityType) {
        //先指定查询
        List<MetaDTO> metaDTOList = queryMeta(companyId, tenantId, entityType);
        boolean filterCompany = true;
        if (CollectionUtils.isEmpty(metaDTOList)) {
            //指定查询未查询到，则使用全局配置查询,
            filterCompany = false;
            metaDTOList = queryMeta(null, OrderServiceConstants.COMMON_TENANT, entityType);
        }
        Stream<MetaDTO> stream = metaDTOList.stream();
        if (companyId != null && filterCompany) {
            stream = stream.filter(dto -> companyId.equals(dto.getCompanyId()));
        }

        final Optional<MetaDTO> ret = stream.findFirst();
        if (ret.isPresent()) {
            final MetaDTO metaDTO = ret.get();
            final List<MetaItemDTO> metaItems = itemService.queryMetaItemDTO(metaDTO.getId());
            metaDTO.setItemList(metaItems);
            return metaDTO;
        }
        return null;
    }

    private List<MetaDTO> queryMeta(String companyId, String tenantId, EntityType entityType) {
        log.debug("query meta by:{}, {}, {}", companyId, tenantId, entityType);
        QueryWrapper<MetaEntity> query = new QueryWrapper<>();
        query.eq(Meta.ENTITY_TYPE, entityType.value())
                .eq(Meta.STATUS, CommonStatus.NORMAL.value())
                .and(q -> q.eq(companyId != null, Meta.COMPANY_ID, companyId)
                        .eq(Meta.TENANT_ID, tenantId == null ? 0 : tenantId));


        final List<MetaEntity> entityList = dao.selectList(query);
        if (!CollectionUtils.isEmpty(entityList)) {
            final List<Long> idList = entityList.stream().map(MetaEntity::getId).collect(Collectors.toList());
            final Map<Long, List<MetaItemDTO>> detailMap = itemService.queryMetaItem(idList);
            return entityList.stream().map(e -> {
                MetaDTO dto = new MetaDTO();
                BeanUtil.copyProperties(e, dto);
                dto.setItemList(detailMap.get(dto.getId()));
                return dto;
            }).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

}
