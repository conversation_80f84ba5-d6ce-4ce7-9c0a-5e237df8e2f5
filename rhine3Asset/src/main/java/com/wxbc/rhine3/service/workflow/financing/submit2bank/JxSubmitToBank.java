package com.wxbc.rhine3.service.workflow.financing.submit2bank;

import cn.hutool.crypto.SecureUtil;
import com.wxbc.base.util.JsonUtil;
import com.wxbc.base.util.StringUtil;
import com.wxbc.rhine3.bean.OperateLog;
import com.wxbc.rhine3.bean.ZdwFeedBack;
import com.wxbc.rhine3.bean.finance.Finance;
import com.wxbc.rhine3.bean.jxbank.JXBankResponse;
import com.wxbc.rhine3.bean.jxbank.JXFinancePara;
import com.wxbc.rhine3.constants.ExceptionConstants;
import com.wxbc.rhine3.constants.FiFunctionEnum;
import com.wxbc.rhine3.constants.OperateLogConst;
import com.wxbc.rhine3.constants.ReturnCode;
import com.wxbc.rhine3.constants.bank.JXBankConst;
import com.wxbc.rhine3.constants.bank.JxBankConfigPara;
import com.wxbc.rhine3.constants.bank.JxOrganizationCode;
import com.wxbc.rhine3.constants.bank.SendBankStatus;
import com.wxbc.rhine3.exception.BankBusinessException;
import com.wxbc.rhine3.httpclient.HttpClient;
import com.wxbc.rhine3.service.OperateLogService;
import com.wxbc.rhine3.service.company.CompanyService;
import com.wxbc.rhine3.service.finance.FinanceUpdateDbService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;

//江西银行的推送银行具体实现类
@Service
@Slf4j
public class JxSubmitToBank implements SubmitToBankInterface {
    @Autowired
    private OperateLogService operateLogService;
    @Autowired
    private JxBankConfigPara jxBankConfigPara;
    @Autowired
    private CompanyService companyService;
    @Autowired
    private FinanceUpdateDbService financeUpdateDbService;

    @Override
    public FiFunctionEnum initFiFunctionEnum() {
        return FiFunctionEnum.JXB;
    }

    @Override
    public void sendDataToBankAndUpdateDb(Finance finance, ZdwFeedBack zdw) throws BankBusinessException {
        JXFinancePara para = updateJxFinancePara(zdw.getZdwRegNo(), finance);

        OperateLog operateLog = operateLogService.getOperateLog(
                jxBankConfigPara.getJxFinanceUrl() + JXBankConst.FINANCE_URL,
                JsonUtil.object2String(para), OperateLogConst.OPERATE_API_NAME_BY_FINANCE,
                OperateLogConst.OPERATE_RECEIVER, para.getApplicationNumber());

        try {
            String result = HttpClient.sendPostByHttps(
                    jxBankConfigPara.getJxFinanceUrl() + JXBankConst.FINANCE_URL, para);
            log.info("jxBank finance request result:{}", result);
            JXBankResponse response = JsonUtil.jsonStr2Object(result, JXBankResponse.class);

            operateLogService.insertOptResultLog(operateLog, result, response.getReturnCode());

            if (StringUtil.valueOf(ReturnCode.REQUEST_SUCCESS.getValue(),"").equals(response.getReturnCode())) {
                financeUpdateDbService.updateFinanceBankFlag(SendBankStatus.SUCCESS.name(), finance.getUuid());
            } else {
                String errMsg = ExceptionConstants.FINANCE_ERROR + response.getReturnDesc()
                        .replace(ExceptionConstants.JAVA_LANG_EXCEPTION, "")
                        .replace(ExceptionConstants.JAVA_LANG_EXCEPTION_MH, "");
                throw new BankBusinessException(ExceptionConstants.JXBANK_ERROR + errMsg);
            }

        } catch (IOException | NoSuchAlgorithmException | KeyManagementException e) {
            log.error("jxBank return Exception! FinanceUuid = " + finance.getUuid(), e);
            operateLogService.insertOptResultLog(operateLog, e.toString(),
                    StringUtil.valueOf(ReturnCode.REQUEST_FAILED.getValue(),""));
            String errMsg = ExceptionConstants.FINANCE_ERROR + e.toString()
                    .replace(ExceptionConstants.JAVA_LANG_EXCEPTION, "")
                    .replace(ExceptionConstants.JAVA_LANG_EXCEPTION_MH, "");
            throw new BankBusinessException(ExceptionConstants.JXBANK_ERROR + errMsg);
        }
    }

    @NotNull
    private JXFinancePara updateJxFinancePara(String zdwRegisterNo, Finance finance) {
        JXFinancePara jxFinancePara = JsonUtil.jsonStr2Object(finance.getFinanceExtInfo(), JXFinancePara.class);

        String financeOrganizationCode = JxOrganizationCode.subOrganizationCode(
                companyService.getCompanyByUuid(finance.getApplicantUuid()).getSocialCreditCode());
        jxFinancePara.setFinanceOrganizationCode(financeOrganizationCode);
        jxFinancePara.setCoreOrganizationCode(JxOrganizationCode.subOrganizationCode(
                companyService.getCompanyByUuid(finance.getRelationUuid()).getSocialCreditCode()));

        String signStr = SecureUtil.md5((finance.getApplicationNumber()
                + financeOrganizationCode + jxFinancePara.getFinanceAmount()));
        jxFinancePara.setSignStr(signStr);

        jxFinancePara.setRegistrationNo(zdwRegisterNo);
        return jxFinancePara;
    }

}
