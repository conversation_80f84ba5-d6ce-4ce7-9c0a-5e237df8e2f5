package com.wxbc.rhine3.service.statistical;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.wxbc.cou.manager.api.bean.QueryCouPayAndCreatePara;
import com.wxbc.cou.manager.api.bean.Statistical;
import com.wxbc.cou.manager.api.bean.StatisticalBalance;
import com.wxbc.cou.manager.api.bean.StatisticalBalancePara;
import com.wxbc.rhine3.bean.Company;
import com.wxbc.rhine3.bean.FinanceServiceFeeStatistical;
import com.wxbc.rhine3.bean.StatisticalCorePara;
import com.wxbc.rhine3.bean.response.*;
import com.wxbc.rhine3.common.Response;
import com.wxbc.rhine3.constants.FinanceStatus;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.feign.UserFeignClient;
import com.wxbc.rhine3.repository.mapper.ContractDao;
import com.wxbc.rhine3.repository.mapper.InvoiceDao;
import com.wxbc.rhine3.repository.mapper.StatisticalAssetDao;
import com.wxbc.rhine3.service.SarahService;
import com.wxbc.rhine3.service.company.CompanyService;
import com.wxbc.rhine3.util.FeignClientUtil;
import com.wxbc.venus.user.dto.OrgExtDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.wxbc.rhine3.constants.CommonConst.PROJECT;
import static com.wxbc.rhine3.convert.UtilMapper.convertCent2Yuan;

/**
 * <AUTHOR> 金融服务商
 * 核心指标
 */
@Service
@Slf4j
public class StatisticalCoreService {


    @Autowired
    private StatisticalAssetDao statisticalAssetDao;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private ContractDao contractDao;

    @Autowired
    private InvoiceDao invoiceDao;

    @Autowired
    private SarahService sarahService;

    @Resource
    private CompanyService companyService;


    /**
     * 核心指标页面 根据金融服务商和日期查询统计指标
     */
    public StatisticalCoreResponse queryStatisticalCoreDetail(StatisticalCorePara para) throws ParameterException {
        if (!StringUtils.isBlank(para.getCompanyUuid())) {
            Response<List<String>> listResponse =
                    userFeignClient.queryFiCompanyChildren(para.getCompanyUuid());
            FeignClientUtil.checkResponse(listResponse);
            List<String> companyUuidList = listResponse.getData();
            //不管是不是母公司 都要加上自己
            companyUuidList.add(para.getCompanyUuid());
            para.setCompanyChildrenUuidList(companyUuidList);
        }

        StatisticalCoreResponse statisticalCoreResponse = new StatisticalCoreResponse();
        //查询收入总金额
        FinanceServiceFeeStatistical financeServiceFeeStatistical = statisticalAssetDao.queryFinanceServiceFeeByPara(para);
        if (financeServiceFeeStatistical != null) {
            statisticalCoreResponse.setServiceFeeAmount(financeServiceFeeStatistical.getServiceFeeAmount());
        }

        //查询融资相关统计数据
        queryFinanceStatisticalData(para, statisticalCoreResponse);

        //查询融信相关统计数据
        queryCouStatisticalData(para, statisticalCoreResponse);

        //查询已上传合同数量
        Integer contractCount = contractDao.countUploadContract(para);
        statisticalCoreResponse.setUploadContractCount(contractCount);

        //查询已上传发票数量
        Integer invoiceCount = invoiceDao.countUploadInvoice(para);
        statisticalCoreResponse.setUploadInvoiceCount(invoiceCount);

        //查询企业相关统计数据
        Response<StatisticalCompanyResponse> companyResponse = userFeignClient.queryStatisticalCompany(para);
        FeignClientUtil.checkResponse(companyResponse);
        StatisticalCompanyResponse companyResponseData = companyResponse.getData();
        BeanUtils.copyProperties(companyResponseData, statisticalCoreResponse);

        //核心企业授信数据
        Response<CoreCreditStatisticalResp> coreCreditStatisticalResponse = userFeignClient.statisticalCreditData(para);
        FeignClientUtil.checkResponse(coreCreditStatisticalResponse);
        CoreCreditStatisticalResp coreCreditStatisticalResp = coreCreditStatisticalResponse.getData();
        if (null != coreCreditStatisticalResp) {
            BeanUtils.copyProperties(coreCreditStatisticalResp, statisticalCoreResponse);
            statisticalCoreResponse.setCoreCompanyCreditAvgAmount(coreCreditStatisticalResp.getCoreCompanyCreditAvgAmount().setScale(2, RoundingMode.HALF_UP));
        }
        //融资余额
        queryCouBalanceStatisticalData(para, statisticalCoreResponse);

        //已上传合同金额（元）
        BigDecimal contractAmount = contractDao.sumUploadContractAmount(para);
        if (null != contractAmount) {
            statisticalCoreResponse.setUploadedContractAmount(contractAmount);
        }
        //已上传发票金额（元）
        BigDecimal invoiceAmount = invoiceDao.sumUploadInvoiceAmount(para);
        if (null != invoiceAmount) {
            statisticalCoreResponse.setUploadedInvoiceAmount(invoiceAmount);
        }
        return statisticalCoreResponse;
    }

    /**
     * 统计融资相关数据
     */
    private void queryFinanceStatisticalData(StatisticalCorePara para, StatisticalCoreResponse statisticalCoreResponse) {
        //融资申请总金额 和 融资申请企业数
        FinanceApplicationStatisticalResponse financeApplicationStatisticalResponse = statisticalAssetDao.queryFinanceApplicationDetail(para);
        BeanUtil.copyProperties(financeApplicationStatisticalResponse, statisticalCoreResponse, CopyOptions.create().ignoreNullValue());

        //融资放款总金额 和 融资放款企业数
        FinanceLoanStatisticalResponse financeLoanStatisticalResponse = statisticalAssetDao.queryFinanceLoanDetail(para);
        BeanUtil.copyProperties(financeLoanStatisticalResponse, statisticalCoreResponse, CopyOptions.create().ignoreNullValue());
        //融资申请总笔数 和 融资放款总笔数
        ArrayList<String> statusList = new ArrayList<>();
        statusList.add(FinanceStatus.CONFIRM.name());
        //放款总笔数
        Integer financeLoanCount = statisticalAssetDao.queryFinanceLoanCount(para, statusList);
        statisticalCoreResponse.setFinanceLoanCount(financeLoanCount);
        statusList.add(FinanceStatus.REJECT.name());
        statusList.add(FinanceStatus.AUDIT.name());
        statusList.add(FinanceStatus.CONFIRM.name());
        //融资申请总笔数
        Integer financeApplicationCount = statisticalAssetDao.queryFinanceCount(para, statusList);
        statisticalCoreResponse.setFinanceApplicationCount(financeApplicationCount);
    }
    /**
     * 统计融信相关数据
     */
    private void queryCouStatisticalData(StatisticalCorePara para, StatisticalCoreResponse statisticalCoreResponse) throws ParameterException {
        //融信开立次数 和 融信开立金额
        //融信支付次数 和 融信支付金额
        QueryCouPayAndCreatePara queryCouPayAndCreatePara = new QueryCouPayAndCreatePara();
        queryCouPayAndCreatePara.setProject(PROJECT);
        queryCouPayAndCreatePara.setStartDate(para.getStartDate());
        queryCouPayAndCreatePara.setEndDate(para.getEndDate());
        if (!CollectionUtils.isEmpty(para.getCompanyChildrenUuidList())) {
            //金融服务商
            List<OrgExtDTO> companyList = companyService.getCompanyByUuid(para.getCompanyChildrenUuidList());
            queryCouPayAndCreatePara.setCompanyPubKeyList(companyList.stream().map(OrgExtDTO::getPubKey).collect(Collectors.toList()));
        }
        Statistical statistical = sarahService.queryPayAndCreateCou(queryCouPayAndCreatePara);
        statisticalCoreResponse.setCouCreateCount(statistical.getCouCreateCount());
        if (statistical.getCouCreateAmount() != null) {
            statisticalCoreResponse.setCouCreateAmount(convertCent2Yuan(statistical.getCouCreateAmount()));
        }

        statisticalCoreResponse.setCouPayCount(statistical.getCouPayCount());
        if (statistical.getCouPayAmount() != null) {
            statisticalCoreResponse.setCouPayAmount(convertCent2Yuan(statistical.getCouPayAmount()));
        }
    }

    /**
     * 统计融信余额相关数据
     */
    private void queryCouBalanceStatisticalData(StatisticalCorePara para, StatisticalCoreResponse statisticalCoreResponse) throws ParameterException {
        StatisticalBalancePara statisticalBalancePara = new StatisticalBalancePara();
        statisticalBalancePara.setProject(PROJECT);
        if (!CollectionUtils.isEmpty(para.getCompanyChildrenUuidList())) {
            //COU持有人 金融服务商
            List<OrgExtDTO> companyList = companyService.getCompanyByUuid(para.getCompanyChildrenUuidList());
            statisticalBalancePara.setCompanyPubKeyList(companyList.stream().map(OrgExtDTO::getPubKey).collect(Collectors.toList()));
        }
        StatisticalBalance statisticalBalance = sarahService.statisticalBalanceData(statisticalBalancePara);
        if (statisticalBalance != null) {
            BeanUtils.copyProperties(statisticalBalance, statisticalCoreResponse);
            statisticalCoreResponse.setFinancingBalancePerCompany(statisticalBalance.getFinancingBalancePerCompany().setScale(2, RoundingMode.HALF_UP));
        }
    }


}
