package com.wxbc.rhine3.service.workflow;

import com.wxbc.rhine3.common.constant.AuditResult;
import com.wxbc.rhine3.constants.workflow.WorkflowProcessDefinitionKeyEnum;
import com.wxbc.scaffold.common.definition.exception.BizException;
import com.wxbc.workflow.model.TaskRelationVo;
import com.wxbc.workflow.model.flowable.ActTaskEntity;
import org.flowable.task.service.impl.persistence.entity.TaskEntityImpl;

import java.util.List;
import java.util.Map;

// 每个流程的抽象类
// 新增工作流流程时只要实现这个接口就可以了
public interface AuditInterface {
    /**
     * 函数功能：表明当前是什么流程
     * @return 工作流流程定义枚举，与bpmn中processDefinitionKey一一对应
     */
    WorkflowProcessDefinitionKeyEnum initProcessDefKey();


    /**
     * task 异常结束时, 目前是BizReturnCode.FIND_TASK_ERROR和BizReturnCode.CLAIM_TASK_ERROR
     * @param processDefinitionKey 流程的key
     * @param businessKey          业务key
     * @param e                    exception
     */
    default RuntimeException onCompleteTaskError(String processDefinitionKey, String businessKey, BizException e) {
        throw e;
    }

    /**
     * 函数功能：根据工作流提供的taskList(包含businessKey)，填充待审核列表waitingAuditList
     * 前端通过/process/task/loadCurrentUserTask接口查看当前登录用户的待审核列表
     *
     * @param waitingAuditList - 待审核列表waitingAuditList
     * @param taskList         - 工作流提供的taskList(包含businessKey)
     */
    void fillWaitingAuditList(List<ActTaskEntity> waitingAuditList, List<TaskRelationVo> taskList);

    /**
     * 函数功能： 节点审核数据处理(同意该流程 or 拒绝该流程)
     * 请注意工作流doTask会自动加事务包裹，请注意业务代码和工作流事务是否需要在同一个事务中
     * 前端通过/process/task/doTask 接口进行审核操作
     *
     * @param nodeName  - 当前任务节点的名字，该名字和bpmn流程图中所画一一对应
     * @param variables - workflow启动时WorkFlowStartService.startProcess传入的变量
     * @param businessKey - 是业务数据的主Key，是在workflow启动时WorkFlowStartService.startProcess传入的业务主key
     *                    businessKey：开立和支付是transferUuid，融资是financeUuid,退款是refundUuid
     */
    void auditProc(String nodeName, Map<?,?> variables, String businessKey);

    /**
     * 流程中任务节点创建的事件， 此时还未执行
     * @param bizKey    业务key，和businessKey是同一个意思
     * @param nodeName  节点名称/任务名称
     * @param variables 流程中流转的变量
     */
    default void onTaskCreated(String bizKey, String nodeName, Map<String, Object> variables) {

    }

    /**
     * 流程完成的回调事件
     * @param bizKey      业务key
     * @param variables   业务流程中的变量
     * @param comment     最后的审批备注
     * @param auditResult 审核结果
     */
    default void onProcessComplete(String bizKey, Map<String, Object> variables, String comment, AuditResult auditResult){

    }


    default void onTaskCreatedAfterSetAssignCandidate(TaskEntityImpl delegateTask, String processDefinitionKey, String businessKey) {

    }
}
