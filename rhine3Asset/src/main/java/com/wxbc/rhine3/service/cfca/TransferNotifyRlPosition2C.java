package com.wxbc.rhine3.service.cfca;

import com.wxbc.rhine3.bean.protocol.ProtocolSignPosition;
import com.wxbc.rhine3.constants.FiFunctionEnum;
import com.wxbc.rhine3.constants.ProtocolTypeEnum;
import com.wxbc.rhine3.constants.bank.RlBankConfigPara;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TransferNotifyRlPosition2C implements SignPositionInterface{
    @Autowired
    private RlBankConfigPara rlPara;

    @Override
    public String init() {
        return ProtocolTypeEnum.PROTOCOL_TRANSFER_NOTIFY_2C.getVal() + FiFunctionEnum.FJRL.getVal();
    }

    @Override
    public ProtocolSignPosition getPosition() {
        return ProtocolSignPosition.builder()
                .signatureType(rlPara.getRlTransferNotifySignType())
                .signatureCoordinateX(rlPara.getRlTransferNotifyCoordinateX())
                .signatureCoordinateY(rlPara.getRlTransferNotifyCoordinateY())
                .signatureKeyWord(rlPara.getRlTransferNotifyKeyWord())
                .signaturePageIndex(rlPara.getRlTransferNotifyPageIndex())
                .needBase64Flag(false).addDateMark(false)
                .build();
    }
}
