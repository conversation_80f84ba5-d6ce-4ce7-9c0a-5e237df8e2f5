package com.wxbc.rhine3.service.workflow.destroying.sign;

import com.wxbc.rhine3.constants.FiFunctionEnum;
import com.wxbc.rhine3.constants.ProtocolTypeEnum;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.service.cfca.CfcaSignatureService;
import com.wxbc.venus.user.util.UserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SrAuditDestroySignService implements AuditDestroySignInterface{
    @Autowired
    private CfcaSignatureService cfcaSignatureService;

    @Override
    public FiFunctionEnum initSignParaEnum() {
        return FiFunctionEnum.SRB;
    }

    @Override
    public void signCashPdf(String cashPdfUrl)  throws ParameterException {

        cfcaSignatureService.signatureFile(UserUtil.get().getCompany(), cashPdfUrl,
                ProtocolTypeEnum.PROTOCOL_DESTROY_NOTIFY,FiFunctionEnum.SRB);

    }
}
