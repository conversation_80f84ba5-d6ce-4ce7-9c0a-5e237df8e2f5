package com.wxbc.rhine3.service;

import com.wxbc.base.util.JsonUtil;
import com.wxbc.base.util.StringUtil;
import com.wxbc.rhine3.bean.ThirdInvokeLogEntity;
import com.wxbc.rhine3.constants.CommonConst;
import com.wxbc.rhine3.constants.ReturnCode;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * @company: 万向区块链
 * @description 内部公共服务调用日志存储函数式接口
 * @author: lixingxing
 * @create: 2023-05-26 16:14
 **/
@Service
public class ThirdLogFunctionService<T> {

    @Autowired
    private ThirdInvokeLogService thirdInvokeLogService;

    public T saveLog(FunctionCall<T> functionCall,String... args){
        ThirdInvokeLogEntity thirdInvokeLogEntity=new ThirdInvokeLogEntity();
        T res =null;
        try {
            thirdInvokeLogEntity.setApiUrl(args[0]);
            thirdInvokeLogEntity.setSendTime(LocalDateTime.now());
            thirdInvokeLogEntity.setTargetName(args[1]);
            thirdInvokeLogEntity.setTraceId(TraceContext.traceId());
            thirdInvokeLogEntity.setRelationNo(args[2]);
            thirdInvokeLogEntity.setSendInfo(args[3]);
            res = functionCall.execute();
        }catch (Exception e){
            thirdInvokeLogEntity.setReturnCode(StringUtil.valueOf(ReturnCode.REQUEST_FAILED.getValue(),""));
            thirdInvokeLogEntity.setReturnResult(e.getMessage());
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
            throw e;
        }
        thirdInvokeLogEntity.setReceiveTime(LocalDateTime.now());
        thirdInvokeLogEntity.setReturnCode(StringUtil.valueOf(ReturnCode.REQUEST_SUCCESS.getValue(),""));
        if (res != null && JsonUtil.object2String(res).length() <= CommonConst.LOG_PRINT_LMIT_10480_BYTE) {
            thirdInvokeLogEntity.setReturnResult(JsonUtil.object2String(res));
        }
        thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
        return res;
    }

    @FunctionalInterface
    public interface FunctionCall<T> {
        T execute();
    }

}
