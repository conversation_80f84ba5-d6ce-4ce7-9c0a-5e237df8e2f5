package com.wxbc.rhine3.service.proof;

import com.wxbc.rhine3.bean.cou.CouResponse;
import com.wxbc.rhine3.constants.CouInfoProofConsts;
import com.wxbc.rhine3.util.NumberFormatUtil;
import com.wxbc.rhine3.util.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> zhengjiao
 * @Date : 2023-05-22 10:05
 * @Description :
 */
@Service
@Slf4j
public class ProofOriginDataService {
    public Map<String, Object> genCouProofOriginDataMap(CouResponse couResponse,boolean isFinance) throws ParseException {
        Map<String, Object> couProofItemMap = new HashMap<>();
        couProofItemMap.put(CouInfoProofConsts.COU_NO_FIELD_CHINESE_NAME, couResponse.getCouNo());
        couProofItemMap.put(CouInfoProofConsts.COU_AMOUNT_FIELD_CHINESE_NAME, NumberFormatUtil.
                thousandsFormatAnd2f(couResponse.getCouAmountInYuan()));
        if(isFinance){
            couProofItemMap.put(CouInfoProofConsts.COU_PUBLISH_DATE_FIELD_CHINESE_NAME, TimeUtils.getFormatTime(new Date(),TimeUtils.DATA_FORMAT_YYYY_MM_DD));
        }else{
            couProofItemMap.put(CouInfoProofConsts.COU_PUBLISH_DATE_FIELD_CHINESE_NAME, couResponse.getCouCreateTime());
        }
        couProofItemMap.put(CouInfoProofConsts.COU_DUE_DATE_FIELD_CHINESE_NAME, couResponse.getDueDate());
        couProofItemMap.put(CouInfoProofConsts.COU_PUBLISH_FIELD_CHINESE_NAME, couResponse.getPublishName());
        couProofItemMap.put(CouInfoProofConsts.COU_CREDIT_FIELD_CHINESE_NAME, couResponse.getCreditName());
        couProofItemMap.put(CouInfoProofConsts.COU_PAYMENT_FIELD_CHINESE_NAME, couResponse.getPayName());
        couProofItemMap.put(CouInfoProofConsts.COU_HOLDER_FIELD_CHINESE_NAME, couResponse.getHolderName());
        return couProofItemMap;
    }
}
