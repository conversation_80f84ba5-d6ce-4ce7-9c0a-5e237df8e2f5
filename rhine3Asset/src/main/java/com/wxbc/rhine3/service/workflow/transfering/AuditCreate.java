package com.wxbc.rhine3.service.workflow.transfering;

import com.wxbc.cou.manager.api.domain.Transfer;
import com.wxbc.rhine3.bean.AuditPara;
import com.wxbc.rhine3.bean.TransferApplication;
import com.wxbc.rhine3.bean.User;
import com.wxbc.rhine3.constants.CommonConst;
import com.wxbc.rhine3.constants.UpdateQuotaOptType;
import com.wxbc.rhine3.constants.workflow.WorkflowProcessDefinitionKeyEnum;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.feign.UserFeignClient;
import com.wxbc.rhine3.repository.mapper.FinanceInvoiceDao;
import com.wxbc.rhine3.repository.mapper.FinanceInvoiceRetainDao;
import com.wxbc.rhine3.repository.mapper.InvoiceDao;
import com.wxbc.rhine3.service.SarahService;
import com.wxbc.rhine3.service.company.CompanyService;
import com.wxbc.rhine3.service.contract.UpdateContractAmount;
import com.wxbc.rhine3.service.transfer.TransferApplicationService;
import com.wxbc.workflow.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

import static com.wxbc.rhine3.constants.workflow.WorkflowProcessDefinitionKeyEnum.PROCESS_DEFINITION_KEY_COU_CREATE;

//开立流程具体实现类
@Service
@Slf4j
public class AuditCreate extends AbstractAuditCreateOrPay {
    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private SarahService sarahService;
    @Autowired
    private TransferApplicationService transferApplicationService;

    @Autowired
    private UpdateContractAmount updateContractAmount;
    @Autowired
    private CompanyService companyService;
    @Autowired
    private FinanceInvoiceDao financeInvoiceDao;
    @Autowired
    private FinanceInvoiceRetainDao financeInvoiceRetainDao;
    @Autowired
    private InvoiceDao invoiceDao;

    @Override
    public void handleSarahAndModifyRelationAmount(AuditPara request, String publishCompanyUuid,
                                                                      String creditCompanyUuid, Transfer transfer) throws ParameterException {

        List<TransferApplication> transferApplicationList =
                transferApplicationService.getTransferApplicationList(transfer.getUuid());
        String contractUuid = transferApplicationList.get(0).getContractUuid();

        transferApplicationService.updateTransferApplicationInfo(
                request.getIsAgree(), request.getAgreeReason(), transfer.getUuid());

        if (request.getIsAgree()) {
            userFeignClient.updateQuota(publishCompanyUuid, creditCompanyUuid,
                    transfer.getSumTransferAmountInCent(), UpdateQuotaOptType.FROZEN_2_USED,1);
            financeInvoiceDao.confirmInvoiceByFrUuid(transfer.getUuid());
            invoiceDao.updateInvoiceUsedAmountByFruuid(transfer.getUuid());
        } else {
            updateContractAmount.subtractContractUsedAmount(transfer.getSumTransferAmountInCent()
                    .divide(CommonConst.HUNDRED), contractUuid);

            userFeignClient.updateQuota(publishCompanyUuid, creditCompanyUuid,
                    transfer.getSumTransferAmountInCent(), UpdateQuotaOptType.ROLL_FROZEN,1);
            //拒绝释放关联的发票之前，先保存在关联表中
//            financeInvoiceRetainDao.insertRetainBySelect(transfer.getUuid());
            //释放关联的发票
            financeInvoiceDao.releaseInvoiceByFrUuid(transfer.getUuid());
            invoiceDao.updateInvoiceUsedAmountByFruuid(transfer.getUuid());
        }

        try {
            sarahService.auditCou(transfer.getUuid(), request.getToPubKey(),request.getIsAgree(),true);

        } catch (Exception e) {
            log.error("sarahService.auditCou create error! businessKey= " + transfer.getUuid(), e);
            rollUserAmountRelation(request.getIsAgree(), creditCompanyUuid, transfer.getSumTransferAmountInCent());
            throw e;
        }
    }

    private void rollUserAmountRelation(boolean isAgree, String creditCompanyUuid, BigDecimal sumTransferAmountInCent) throws ParameterException {
        User currUser = companyService.getAndCheckUserValidation(UserUtil.getUserId());

        if (Boolean.TRUE.equals(isAgree)) {
            userFeignClient.updateQuota(currUser.getCompanyUuid(), creditCompanyUuid,
                    sumTransferAmountInCent, UpdateQuotaOptType.ROLL_FROZEN_2_USED,1);
        } else {
            userFeignClient.updateQuota(currUser.getCompanyUuid(), creditCompanyUuid,
                    sumTransferAmountInCent, UpdateQuotaOptType.ADD_FROZEN,1);
        }
    }

    @Override
    public WorkflowProcessDefinitionKeyEnum initProcessDefKey() {
        return PROCESS_DEFINITION_KEY_COU_CREATE;
    }
}
