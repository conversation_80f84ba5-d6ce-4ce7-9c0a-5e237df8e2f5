package com.wxbc.rhine3.service.destroy;

import com.github.pagehelper.PageInfo;
import com.wxbc.cou.manager.api.constant.CouCashStatus;
import com.wxbc.cou.manager.api.constant.CouStatus;
import com.wxbc.cou.manager.api.domain.Cou;
import com.wxbc.rhine3.bean.destroy.DestroyCou4Detail;
import com.wxbc.rhine3.bean.destroy.DestroyQueryCouLeaves;
import com.wxbc.rhine3.constants.ExceptionConstants;
import com.wxbc.rhine3.constants.FiFunctionEnum;
import com.wxbc.rhine3.convert.CouLeavesConvert;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.service.SarahService;
import com.wxbc.rhine3.service.company.CompanyService;
import com.wxbc.venus.user.util.UserUtil;
import com.wxbc.rhine3.utils.PageUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class DestroyCouService {
    @Autowired
    private SarahService sarahService;
    @Autowired
    private CompanyService companyService;
    @Autowired
    private DestroyCacheService destroyCacheService;

    public PageInfo<DestroyCou4Detail> queryPageCouLeaves(DestroyQueryCouLeaves para) throws ParameterException {
        PageInfo<Cou> couPageInfo = sarahService.queryPageCouLeaves(para, UserUtil.get().getCompany().getPubKey());
        if (null == couPageInfo || CollectionUtils.isEmpty(couPageInfo.getList())){
            return new PageInfo<>();
        }

        companyService.fillCouCompanyNamesAndUuids(couPageInfo.getList());

        PageInfo<DestroyCou4Detail> destroyCouPageInfo = new PageInfo<>();
        PageUtil.copyPaginationInfo(couPageInfo, destroyCouPageInfo);
        destroyCouPageInfo.setList(CouLeavesConvert.instance.source2details(couPageInfo.getList()));
        return destroyCouPageInfo;
    }

    public void checkDestroyCouLeaf(String couUuid) throws ParameterException {
        Cou destroyCou = getSingleDestroyCou(couUuid);
        checkDestroyCou(destroyCou);
    }

    public void checkDestroyCou(Cou destroyCou) throws ParameterException {
        if (!UserUtil.get().getCompany().getPubKey().equals(destroyCou.getPublishPubKey())){
            throw new ParameterException(ExceptionConstants.DESTROY_COU_PUBLISH_ERROR);
        }

        if(destroyCacheService.isCouInDestroyWorkflow(destroyCou.getUuid())){
            throw new ParameterException(ExceptionConstants.DESTROY_CREATE_DUPLICATED);
        }

        if (CouCashStatus.CASH_OK.name().equals(destroyCou.getCashStatus())){
            throw new ParameterException(ExceptionConstants.DESTROY_AUDIT_NOT_FOUND);
        }

        if (CouCashStatus.NO_CASH.name().equals(destroyCou.getCashStatus())){
            throw new ParameterException(ExceptionConstants.DESTROY_COU_LOCK_ERROR);
        }

        if (!CouStatus.TRANSFERRING.name().equals(destroyCou.getStatus())
                && !CouStatus.AVAILABLE.name().equals(destroyCou.getStatus())) {
            throw new ParameterException(ExceptionConstants.DESTROY_COU_STATUS_ERROR);
        }

        if ((FiFunctionEnum.JXB.getVal() == companyService.getFinanceBusinessConfig(destroyCou.getCreditUuid()).getFinanceFunctionFlag())
                && destroyCou.getHolderUuid().equals(destroyCou.getCreditUuid()) ){
            throw new ParameterException(ExceptionConstants.DESTROY_COU_JXB_ERROR);
        }
    }

    public Cou getSingleDestroyCou(String couUuid) throws ParameterException {
        List<Cou> destroyCouList = getDestroyCouList(Collections.singletonList(couUuid));
        return destroyCouList.get(0);
    }

    private List<Cou> getDestroyCouList(List<String> couUuids) throws ParameterException {
        List<Cou> destroyCouList = sarahService.couListByUuidsAndTransfer(couUuids);
        if (CollectionUtils.isEmpty(destroyCouList) || null == destroyCouList.get(0)) {
            throw new ParameterException(ExceptionConstants.DESTROY_COU_NOT_FOUND);
        }

        companyService.fillCouCompanyNamesAndUuids(destroyCouList);

        return destroyCouList;
    }

    public Map<String, Cou> getDestroyCouMap(List<String> couUuids) throws ParameterException {
        List<Cou> destroyCouList = getDestroyCouList(couUuids);
        return destroyCouList.stream().collect(Collectors.toMap(Cou::getUuid, Function.identity()));
    }
}
