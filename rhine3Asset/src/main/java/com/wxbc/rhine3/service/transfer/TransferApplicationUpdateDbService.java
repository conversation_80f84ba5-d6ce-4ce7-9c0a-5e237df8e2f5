package com.wxbc.rhine3.service.transfer;

import com.wxbc.base.util.JsonUtil;
import com.wxbc.cou.manager.api.domain.Cou;
import com.wxbc.cou.manager.api.response.TransferResponse;
import com.wxbc.rhine3.bean.CreateCouRequest;
import com.wxbc.rhine3.bean.FileInfo;
import com.wxbc.rhine3.bean.Protocol;
import com.wxbc.rhine3.bean.TransferApplication;
import com.wxbc.rhine3.constants.ProtocolTypeEnum;
import com.wxbc.rhine3.constants.TransferStatus;
import com.wxbc.rhine3.exception.UpdateFailedException;
import com.wxbc.rhine3.repository.mapper.TransferApplicationModifyDao;
import com.wxbc.rhine3.service.invoice.TradeInvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static com.wxbc.rhine3.constants.ExceptionBaseConstants.BASE_DUPLICATE_KEY_ERROR;
import static com.wxbc.rhine3.constants.ExceptionBaseConstants.PARA_VALIDATE_FAILED;

/**
 * <AUTHOR> zhengjiao
 * @Date : 2022-11-02 16:39
 * @Description : TransferApplication dao写入类service封装
 */
@Service
@Slf4j
public class TransferApplicationUpdateDbService {

    @Autowired
    private TransferApplicationModifyDao transferApplicationModifyDao;

    @Autowired
    private TradeInvoiceService tradeInvoiceService;


    public void batchInsertTransferApplication(List<TransferApplication> transferApplications){
        int rs = transferApplicationModifyDao.batchInsertTransferApplication(transferApplications);
        if(rs < transferApplications.size()){
            log.error("batchInsertTransferApplication error ! transferApplications = {},rs={}", transferApplications, rs);
            throw new UpdateFailedException(BASE_DUPLICATE_KEY_ERROR);
        }
    }

    public void updateTransferAuditInfoById(TransferApplication transferApplication){
        int rs = transferApplicationModifyDao.updateTransferAuditInfoById(transferApplication, TransferStatus.WATING_AUDIT.name());
        if(rs < 1){
            log.error("updateTransferAuditInfoById error ! transferApplication = {},rs={}", transferApplication, rs);
            throw new UpdateFailedException(BASE_DUPLICATE_KEY_ERROR);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void insertTransferApplication4create(CreateCouRequest request, TransferResponse transfer,
                                                 String paymentProtocolUrl) {
        Cou cou = transfer.getCouList().get(0);
        TransferApplication transferApplication = new TransferApplication();
        transferApplication.setTransferUuid(transfer.getUuid());
        transferApplication.setUuid(UUID.randomUUID().toString());
        transferApplication.setCouUuid(cou.getUuid());
        transferApplication.setParentCouUuid(cou.getParentUuid());
        transferApplication.setContractUuid(request.getContractUuid());
        transferApplication.setStatus(TransferStatus.WATING_AUDIT.name());
        transferApplication.setAttachment(request.getAttachment());

        FileInfo fileInfo = new FileInfo();
        Protocol protocol = new Protocol();
        protocol.setUrl(paymentProtocolUrl);
        protocol.setType(ProtocolTypeEnum.PROTOCOL_COMM.getVal());

        fileInfo.setProtocolList(Collections.singletonList(protocol));
        transferApplication.setFileInfo(JsonUtil.object2String(fileInfo));
        transferApplication.setTransferRemark(request.getCreateBase().getCreateRemark());

        insertTransferApplication(transferApplication);
        tradeInvoiceService.batchInsertTradeInvoice(transfer.getUuid(),request.getInvoiceList(),1);

    }

    public void insertTransferApplication(TransferApplication transferApplication){
        try{
            transferApplicationModifyDao.insertTransferApplication(transferApplication);
        }catch (DuplicateKeyException duplicateKeyException) {
            log.error("insertTransferApplication error ! duplicateKeyException ! transferApplication = {}", transferApplication, duplicateKeyException);
            throw new UpdateFailedException(BASE_DUPLICATE_KEY_ERROR);
        } catch (Exception e){
            log.error("insertTransferApplication error ! transferApplication = {}", transferApplication, e);
            throw new UpdateFailedException(PARA_VALIDATE_FAILED);
        }
    }



    public void updateTransferApplicationByTransferUuid(String transferUuid, boolean isAgree, String reason) {
        int rs = transferApplicationModifyDao.updateTransferAuditInfoByTransferUuid(transferUuid,
                reason, isAgree ? TransferStatus.CONFIRMED.name() : TransferStatus.REJECTED.name(),
                TransferStatus.WATING_AUDIT.name()
        );
        if(rs < 1){
            log.error("updateTransferApplicationByTransferUuid error !  transferUuid = {} rs={}", transferUuid, rs);
            throw new UpdateFailedException(BASE_DUPLICATE_KEY_ERROR);
        }
    }

    public void updateTransferContractByTransferUuid(String transferUuid, String contractUuid) {
        int rs = transferApplicationModifyDao.updateTransferContractById(transferUuid, contractUuid);
        if(rs < 1 ){
            log.error("updateTransferContractByTransferUuid error !  transferUuid = {} contractUuid={} rs={}", transferUuid,contractUuid, rs);
            throw new UpdateFailedException(BASE_DUPLICATE_KEY_ERROR);
        }
    }
}
