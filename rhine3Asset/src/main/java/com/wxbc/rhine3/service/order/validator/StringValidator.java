package com.wxbc.rhine3.service.order.validator;

import com.google.common.collect.Lists;
import com.wxbc.rhine3.common.constant.FieldType;
import com.wxbc.rhine3.common.constant.OrderFinancingReturnCode;
import com.wxbc.rhine3.model.order.dto.MetaItemDTO;
import com.wxbc.scaffold.common.definition.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * financing-service
 *
 * <AUTHOR> chen cheng
 * Date         : 2022/11/24 15:04
 * Jdk          : 1.8
 * Description  :
 */
@Service
@Slf4j
public class StringValidator extends BaseValidator {

    /**
     * 类型是否匹配
     *
     * @param meta  元数据
     * @param value 当前值
     * @return true - 类型匹配, false - 类型不匹配
     */
    @Override
    boolean isTypeMatch(MetaItemDTO meta, Object value) {
        return true;
    }

    @Override
    void validateValue(MetaItemDTO meta, Object value) {
        if ((value != null && meta.getFieldLength() > 0) && (value.toString().length() > meta.getFieldLength())) {
            log.error("字段长度不正确, metaId:{}, 字段:{},{}", meta.getMetaId(), meta.getFieldName(), meta.getDisplayName());
            throw new BizException(OrderFinancingReturnCode.BIZ_META_FIELD_INCORRECT_FORMAT);
        }
    }

    /**
     * 验证器支持的字段类型
     *
     * @return List
     */
    @Override
    public List<FieldType> supportFieldType() {
        return Lists.newArrayList(FieldType.STRING);
    }
}
