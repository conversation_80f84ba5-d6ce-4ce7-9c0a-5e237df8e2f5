package com.wxbc.rhine3.service.workflow.refunding;

import cn.hutool.core.bean.BeanUtil;
import com.wxbc.rhine3.bean.refund.Refund;
import com.wxbc.rhine3.bean.refund.RefundPayAudit;
import com.wxbc.rhine3.common.constant.AuditResult;
import com.wxbc.rhine3.constants.ErrorConstants;
import com.wxbc.rhine3.constants.workflow.WorkflowProcessDefinitionKeyEnum;
import com.wxbc.rhine3.exception.BusinessException;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.repository.mapper.RefundQueryDao;
import com.wxbc.rhine3.service.SarahService;
import com.wxbc.rhine3.service.company.CompanyService;
import com.wxbc.rhine3.service.transfer.TransferApplicationUpdateDbService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.wxbc.rhine3.constants.workflow.WorkflowConst.*;
import static com.wxbc.rhine3.constants.workflow.WorkflowProcessDefinitionKeyEnum.PROCESS_DEFINITION_KEY_REFUND_PAY;

//支付退款的具体实现类
@Service
@Slf4j
public class AuditRefundPay extends AbstractAuditRefund {
    @Autowired
    private CompanyService companyService;
    @Autowired
    private SarahService sarahService;
    @Autowired
    private TransferApplicationUpdateDbService transferApplicationUpdateDbService;
    @Autowired
    private RefundQueryDao refundQueryDao;

    @Override
    public void auditProc(String nodeName, Map<?,?> variables, String businessKey) {
        if (TASK_NAME_S_OPERATOR.equals(nodeName)
                && PARAMETER_AGREE.equals(variables.get(PARAMETER_OUTCOME).toString())) {
            relationContract(variables, businessKey);
        }
    }

    @Override
    public void onProcessComplete(String bizKey, Map<String, Object> variables, String comment, AuditResult auditResult) {
        try {
            processRefundPay(variables, bizKey);
        } catch (Exception e) {
            log.error("auditRefundPay error:businessKey=" + bizKey, e);
            throw new BusinessException(ErrorConstants.ERROR_REFUND);
        }
    }

    @Override
    public WorkflowProcessDefinitionKeyEnum initProcessDefKey() {
        return PROCESS_DEFINITION_KEY_REFUND_PAY;
    }

    private void relationContract(Map<?,?> variables, String businessKey) {
        try {

            RefundPayAudit refundPayAudit = new RefundPayAudit();
            BeanUtil.fillBeanWithMap(variables, refundPayAudit, true);
            extractedValidate(refundPayAudit);
            String contractUuid = refundPayAudit.getContractUuid();
            Refund refund = queryRefundByUuid(businessKey);
            transferApplicationUpdateDbService.updateTransferContractByTransferUuid(refund.getRefundTransferUuid(), contractUuid);
            sarahService.transferUpdateAssetUuids(contractUuid, refund.getRefundTransferUuid());

        } catch (Exception e) {
            log.error("processRefundPay.transferUpdateAssetUuids,businessKey=" + businessKey, e);
            throw new BusinessException(e.getMessage());
        }
    }

    private void processRefundPay(Map<?,?> variables, String businessKey) throws ParameterException {
        Refund refund = queryRefundByUuid(businessKey);

        boolean isAgree = PARAMETER_AGREE.equalsIgnoreCase(variables.get(PARAMETER_OUTCOME).toString());
        Object reason = variables.get(PARAMETER_COMMENT);
        String reasonStr = reason == null ? null : reason.toString();

        if (isAgree) {
            updateRefundConfirmedByUuid(refund.getRefundUuid());
        } else {
            rejectRefund(businessKey, reason);
        }

        transferApplicationUpdateDbService.updateTransferApplicationByTransferUuid(
                refund.getRefundTransferUuid(), isAgree, reasonStr);

        String toPubKey = companyService.extractCompanyPubKeyByUuid(refund.getReceiveCompanyUuid());

        log.info("taskCompletedCallback,toPubKey={},isAgree={},businessKey={},reasonStr={}",
                toPubKey,isAgree, businessKey,reasonStr);
        sarahService.auditCou(refund.getRefundTransferUuid(), toPubKey,isAgree,false);
    }

    private Refund queryRefundByUuid(String refundUuid) {
        return refundQueryDao.selectRefundByUuid(refundUuid);
    }
}
