package com.wxbc.rhine3.service.protocol;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.wxbc.base.util.JsonUtil;
import com.wxbc.rhine3.bean.BusinessProtocol;
import com.wxbc.rhine3.bean.ProtocolQueryPara;
import com.wxbc.rhine3.bean.finance.SignPlatFromFeeProtocolInnerPara;
import com.wxbc.rhine3.bean.request.LatestPlatformProtocolPara;
import com.wxbc.rhine3.bean.response.ProtocolTextRes;
import com.wxbc.rhine3.common.lob.Project;
import com.wxbc.rhine3.constants.ExceptionConstants;
import com.wxbc.rhine3.exception.BusinessException;
import com.wxbc.rhine3.feign.UserFeignClient;
import com.wxbc.rhine3.remote.Word2PdfExApi;
import com.wxbc.rhine3.repository.mapper.ProtocolDao;
import com.wxbc.rhine3.service.company.CompanyService;
import com.wxbc.rhine3.util.FeignClientUtil;
import com.wxbc.rhine3.util.FileUtil;
import com.wxbc.venus.user.dto.OrgExtDTO;
import com.wxbc.word2pdf.word2pdf.api.Word2PdfApi;
import feign.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class ProtocolWord2pdfService {
    @Resource
    private Word2PdfApi word2PdfApi;
    @Resource
    private Word2PdfExApi word2PdfExApi;
    @Resource
    private ProtocolDao protocolDao;
    @Resource
    private CompanyService companyService;
    @Resource
    private UserFeignClient userFeignClient;

    /**
     * 根据金融机构和协议类型获取协议文件流
     *
     * @param protocolPara 需要填入协议模板的参数
     * @param fiUuid       金融机构uuid
     * @param protocolType 需要获取的协议类型
     * @return 填入协议参数至模板文件之后的文件流
     */
    public byte[] getWord2pdfProtocolData(String protocolPara, String fiUuid, String protocolType) {
        ProtocolQueryPara protocolQueryPara = new ProtocolQueryPara();
        protocolQueryPara.setFinanceUuid(fiUuid);
        protocolQueryPara.setProtocolType(Collections.singletonList(protocolType));
        List<BusinessProtocol> protocolList = getProtocolByFiUuid(protocolQueryPara);
        if (CollectionUtils.isEmpty(protocolList) || protocolList.get(0).getFileContent() == null) {
            throw new BusinessException(ExceptionConstants.PROTOCOL_NOT_FOUND);
        }
        MultipartFile multipartFile = FileUtil.byteToMultipartFile(protocolList.get(0).getFileContent());
        return word2PdfExApi.convertSuper(multipartFile, protocolPara);
    }

    /**
     * 根据金融机构和协议类型获取协议文件流
     *
     * @param protocolPara 需要填入协议模板的参数
     * @param fiUuid       金融机构uuid
     * @param protocolType 需要获取的协议类型
     * @return 填入协议参数至模板文件之后的文件流
     */
    public InputStream getWord2pdfProtocol(String protocolPara, String fiUuid, String protocolType) {
        long beginTime = System.currentTimeMillis();
        ProtocolQueryPara protocolQueryPara = new ProtocolQueryPara();
        protocolQueryPara.setFinanceUuid(fiUuid);
        protocolQueryPara.setProtocolType(Collections.singletonList(protocolType));
        List<BusinessProtocol> protocolList = getProtocolByFiUuid(protocolQueryPara);
        if (CollectionUtils.isEmpty(protocolList) || protocolList.get(0).getFileContent() == null) {
            throw new BusinessException(ExceptionConstants.PROTOCOL_NOT_FOUND);
        }
        MultipartFile multipartFile = FileUtil.byteToMultipartFile(protocolList.get(0).getFileContent());
        return word2PdfProcess(protocolPara, beginTime, multipartFile);
    }

    private InputStream word2PdfProcess(String protocolPara, long beginTime, MultipartFile multipartFile) {
        Response feignResponse = word2PdfApi.convert(multipartFile, protocolPara);
        long endTime = System.currentTimeMillis();
        log.info(" word to pdf server response status:{},protocolPara:{},time-consuming:{}ms", feignResponse.status(), protocolPara, endTime - beginTime);

        if (feignResponse.status() != 200) {
            log.error("word to pdf server error:{},", JsonUtil.object2String(feignResponse.headers()));
            throw new BusinessException(ExceptionConstants.WORD_TO_PDF_SERVER_ERROR);
        }

        try {
            return feignResponse.body().asInputStream();
        } catch (IOException e) {
            log.error("getProtocolContent: pdf server error:{}", JsonUtil.object2String(feignResponse.headers()), e);
            throw new BusinessException(ExceptionConstants.WORD_TO_PDF_SERVER_ERROR);
        }
    }

    private List<BusinessProtocol> getProtocolByFiUuid(ProtocolQueryPara para) {
        List<BusinessProtocol> businessProtocols = protocolDao.getLastestProtocol(para);

        if (CollectionUtils.isNotEmpty(businessProtocols)
                && 0 != businessProtocols.size()) {
            log.info("getProtocolByFiUuid:businessProtocols.get0.protocolType={}",
                    businessProtocols.get(0).getProtocolType());
            return businessProtocols;
        }

        OrgExtDTO fiCompany = companyService.extractCompanyByUuid(para.getFinanceUuid());
        log.info("getProtocolByFiUuid:fiCompany={}", fiCompany);

        if (Objects.nonNull(fiCompany.getParentId())) {
            log.info("getProtocolByFiUuid:ParentCompanyId={}", fiCompany.getParentId());
            para.setFinanceUuid(String.valueOf(fiCompany.getParentId()));
            log.info("getProtocolByFiUuid:ParentFi para={}", para);
            businessProtocols = protocolDao.getLastestProtocol(para);
        }

        return businessProtocols;
    }

    /**
     * 获取平台对应的协议转成PDF并返回
     *
     * @param protocolPara         需要填入协议模板的参数
     * @param ooCompanyUuid        运营机构uuid
     * @param platformProtocolType 平台协议的类型(协议类型:(userRule-平台服务及用户操作规则,companyKey-密钥确认函,
     *                             "platformServer-基于区块链技术的供应链金融服务平台总章程,platformFee-平台服务费协议)
     * @return 填入协议参数至模板文件之后的文件流
     */
    public SignPlatFromFeeProtocolInnerPara getPlatformProtocol2Pdf(String protocolPara, String ooCompanyUuid, String platformProtocolType) {
        long beginTime = System.currentTimeMillis();
        LatestPlatformProtocolPara latestPlatformProtocolPara = new LatestPlatformProtocolPara();
        latestPlatformProtocolPara.setOoCompanyUuid(ooCompanyUuid);
        latestPlatformProtocolPara.setProtocolType(platformProtocolType);
        latestPlatformProtocolPara.setProjectCode(Project.FACTOR.code());
        com.wxbc.rhine3.common.Response<ProtocolTextRes> latestProtocolRes = userFeignClient.queryLatestProtocol(latestPlatformProtocolPara);
        FeignClientUtil.checkResponse(latestProtocolRes);
        SignPlatFromFeeProtocolInnerPara signPlatFromFeeProtocolInnerPara = new SignPlatFromFeeProtocolInnerPara();
        if (latestProtocolRes.getData() == null || latestProtocolRes.getData().getFileContent() == null) {
            //如果没有获取到协议则直接跳过,不报错阻塞流程;默认跳过
            signPlatFromFeeProtocolInnerPara.setContinueFlag(true);
            return signPlatFromFeeProtocolInnerPara;
        }
        MultipartFile multipartFile = FileUtil.byteToMultipartFile(latestProtocolRes.getData().getFileContent());
        signPlatFromFeeProtocolInnerPara.setProtocolContent(word2PdfProcess(protocolPara, beginTime, multipartFile));
        signPlatFromFeeProtocolInnerPara.setProtocolTemplateName(latestProtocolRes.getData().getProtocolType());
        return signPlatFromFeeProtocolInnerPara;
    }
}
