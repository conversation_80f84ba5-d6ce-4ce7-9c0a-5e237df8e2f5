package com.wxbc.rhine3.service.loanafter;

import com.wxbc.common.file.converter.Converter;

/**
 * service-vena
 *
 * <AUTHOR> chen cheng
 * Date         : 2023/9/11 19:18
 * Jdk          : 1.8
 * Description  :
 */
public class InvoiceCheckFlagConverter implements Converter<Integer> {

    /**
     * 转换成excel单元格的值
     *
     * @param value 原始值
     * @return String 返回描述信息
     */
    @Override
    public Object convertToExcelData(Integer value) {

        if (value == 1) {
            return "验真成功";
        } else {
            return "验真失败";
        }
    }
}
