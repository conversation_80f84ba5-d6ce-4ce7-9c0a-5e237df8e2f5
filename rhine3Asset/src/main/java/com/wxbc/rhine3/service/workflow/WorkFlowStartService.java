package com.wxbc.rhine3.service.workflow;

import com.wxbc.base.util.StringUtil;
import com.wxbc.rhine3.constants.workflow.WorkflowConst;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.scaffold.common.definition.exception.BizException;
import com.wxbc.scaffold.common.definition.response.ReturnCode;
import com.wxbc.venus.user.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


@Slf4j
@Service
public class WorkFlowStartService {

    @Resource
    private WorkFlowTaskService workFlowTaskService;

    public void startOrderProcess(String processDefinitionKey,
                                  String fromCompanyId, String toCompanyId,
                                  String businessKey) {
        try {
            Map<String, Object> variables = new HashMap<>();
            variables.put(WorkflowConst.PARAMETER_FROM_COMPANY_UUID, fromCompanyId);
            variables.put(WorkflowConst.PARAMETER_TO_COMPANY_UUID, toCompanyId);
            variables.put(WorkflowConst.PARAMETER_TEAM_NO, "");
            variables.put(WorkflowConst.ORDER_FLOW_KEY, WorkflowConst.ORDER_FLOW_VALUE);

            //设置第一个任务
            variables.put(WorkflowConst.PARAMETER_OUTCOME, "");
            startProcess(processDefinitionKey, businessKey, variables);
        } catch (Exception e) {
            log.error("startProcess {}, businessKey:{} error", processDefinitionKey, businessKey, e);
            throw new BizException(ReturnCode.UNI_PARAMETER_FAILED);
        }
    }

    public void startProcess(String processDefinitionKey, String fromCompanyUuid,
                             String toCompanyUuid, String businessKey, String teamNo) throws ParameterException {
        Map<String, Object> variables = new HashMap<>();
        teamNo = StringUtil.isNotBlank(teamNo) ? teamNo : "";
        variables.put(WorkflowConst.PARAMETER_FROM_COMPANY_UUID, fromCompanyUuid);
        variables.put(WorkflowConst.PARAMETER_TO_COMPANY_UUID, toCompanyUuid);
        variables.put(WorkflowConst.PARAMETER_TEAM_NO, teamNo);
        variables.put(WorkflowConst.PARAMETER_OUTCOME, "");
        //如果是融资流程，需加入op的company_uuid 默认值内置=8
        variables.put(WorkflowConst.PARAMETER_OP_COMPANY_UUID, UserUtil.get().getOperatingOrganizationUuid());

        startProcess(processDefinitionKey, businessKey, variables);
    }

    private void startProcess(String processDefinitionKey, String businessKey, Map<String, Object> variables) throws ParameterException {
        try {
            variables.put(WorkflowConst.PARAMETER_SKIP_EXPRESSION, false);
            variables.put(WorkflowConst.PARAMETER_SKIP_EXPRESSION2, false);
            variables.put(WorkflowConst.BUSINESS_KEY, businessKey);
            variables.put(WorkflowConst.FLOW_KEY, processDefinitionKey);
            log.info("startProcess {}:variables={},businessKey={}", processDefinitionKey, variables, businessKey);
            workFlowTaskService.startProcess(processDefinitionKey, businessKey, variables);
        } catch (Exception e) {
            log.error("startProcess {}, businessKey:{} error", processDefinitionKey, businessKey, e);
            throw new ParameterException(e.getMessage());
        }
    }
}
