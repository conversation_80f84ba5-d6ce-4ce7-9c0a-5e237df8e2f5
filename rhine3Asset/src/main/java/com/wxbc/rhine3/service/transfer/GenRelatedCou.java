package com.wxbc.rhine3.service.transfer;

import com.wxbc.cou.manager.api.domain.Cou;
import com.wxbc.cou.manager.api.domain.Transfer;
import com.wxbc.rhine3.bean.Company;
import com.wxbc.rhine3.bean.TransferApplication;
import com.wxbc.rhine3.bean.response.TransferListCou;
import com.wxbc.rhine3.service.company.CompanyService;
import com.wxbc.venus.user.dto.OrgExtDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GenRelatedCou {
    @Autowired
    private CompanyService companyService;
    @Autowired
    private TransferApplicationService transferApplicationService;

    public List<TransferListCou> genRelatedCou(List<Cou> couList) {
        if (CollectionUtils.isEmpty(couList)) {
            return Collections.emptyList();
        }

        return couList.stream()
                .map(x -> {
                    TransferListCou transferListCou = new TransferListCou(x);
                    OrgExtDTO publishCompany = companyService.extractCompanyByPubKey(transferListCou.getPublishCouPubKey());
                    if (publishCompany != null) {
                        transferListCou.setPublishName(publishCompany.getOrgName());
                        transferListCou.setPublishSocialCreditCode(publishCompany.getSocialCreditCode());
                    }

                    OrgExtDTO creditCompany = companyService.extractCompanyByPubKey(transferListCou.getCreditPubKey());
                    if (creditCompany != null) {
                        transferListCou.setCreditCompanyName(creditCompany.getOrgName());
                        transferListCou.setCreditUuid(creditCompany.getUuid());
                        OrgExtDTO creditParentCompany = companyService.getCompanyParent(String.valueOf(creditCompany.getId()));
                        if (null != creditParentCompany) {
                            transferListCou.setCreditParentPubKey(creditParentCompany.getPubKey());
                        }
                    }

                    transferListCou.setHolderName(companyService.extractCompanyNameByPubKey(transferListCou.getHolderCouPubKey()));
                    TransferApplication transferApplication = transferApplicationService.getTransferApplicationByCouUuid(transferListCou.getUuid());
                    transferListCou.setFileInfo(transferApplication == null ? "" : transferApplication.getFileInfo());
                    return transferListCou;
                }).collect(Collectors.toList());
    }
}
