package com.wxbc.rhine3.utils;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.util.StringUtil;
import com.wxbc.rhine3.bean.Protocol;
import com.wxbc.rhine3.bill.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class ProtocolUtil {
    private ProtocolUtil() {
        //do nothing
    }

    public static String getFileUrlByType(List<Protocol> protocols, String type) {
        if (ArrayUtil.isEmpty(protocols)){
            return null;
        }
        for (Protocol protocol : protocols) {
            if (null==protocol)
            {
                return null;
            }
            if (StringUtil.isEmpty(protocol.getType()))
            {
                return null;
            }

            if (type.equals(protocol.getType())) {
                return protocol.getUrl();
            }
        }
        return null;
    }

    public static List<String> getUrlListByType(List<Protocol> protocols, String type) {
        List<String> urlList = new ArrayList<>();
        for (Protocol protocol : protocols) {
            if (protocol.getType().equals(type)) {
                urlList.add(protocol.getUrl());
            }
        }
        return urlList;
    }
}
