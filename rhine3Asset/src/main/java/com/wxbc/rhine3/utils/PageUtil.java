package com.wxbc.rhine3.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import com.wxbc.base.util.BeanUtil;
import com.wxbc.rhine3.common.PagePara;

public class PageUtil {

    private PageUtil() {
        //do nothing
    }

    /**
     * http请求转mybatis分页请求
     * @param request 查询请求
     * @param <T> entity type
     * @return Ipage 分页参数
     */
    public static <T> IPage<T> requestToIPage(PagePara request){
        IPage<T> page = new Page<>();
        if(request == null){
            page.setCurrent(1);
            page.setSize(50);
        }else{
            page.setSize(request.getPageSize());
            page.setCurrent(request.getPageNum());
        }
        return page;
    }

    /**
     * mybatis分页信息复制到http response,不复制list
     * @param page     mybatis查询分页结果
     * @param response http response
     */
    public static void copyPaginationInfo(IPage<?> page, PageInfo<?> response){
        response.setPageNum(Long.valueOf(page.getCurrent()).intValue());
        response.setPageSize(Long.valueOf(page.getSize()).intValue());
        response.setPages(Long.valueOf(page.getPages()).intValue());
        response.setSize(Long.valueOf(page.getSize()).intValue());
        response.setTotal(Long.valueOf(page.getTotal()).intValue());
    }

    /**
     * 分页信息复制到http response,不复制list
     * @param source     分页结果
     * @param response http response
     */
    public static void copyPaginationInfo(PageInfo<?> source, PageInfo<?> response){
        response.setPageNum(source.getPageNum());
        response.setPageSize(source.getPageSize());
        response.setPages(source.getPages());
        response.setSize(source.getSize());
        response.setTotal(source.getTotal());
    }


    /*
     * 只拷贝分页信息
     */
    public static <R, T> PageInfo<R> copyPagination(IPage<T> page) {
        PageInfo<R> data = new PageInfo<>();
        data.setPageNum((int)page.getCurrent());
        data.setPageSize((int)page.getPages());
        data.setSize((int)page.getSize());
        data.setTotal((int)page.getTotal());
        return data;
    }

    /*
     * 拷贝分页和实体信息
     */
    public static <R, T> PageInfo<R> page2Response(IPage<T> page, BeanUtil.ObjectProvider<R> supplier) {

        PageInfo<R> res = copyPagination(page);
        res.setList(BeanUtil.copyList(page.getRecords(), supplier));
        return res;
    }
}
