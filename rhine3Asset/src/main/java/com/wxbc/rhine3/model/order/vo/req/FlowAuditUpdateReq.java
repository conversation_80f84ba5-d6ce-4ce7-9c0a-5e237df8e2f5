package com.wxbc.rhine3.model.order.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * rhine3Asset
 *
 * <AUTHOR> <PERSON><PERSON> <PERSON><PERSON>
 * Date         : 2023/1/10 16:43
 * Jdk          : 1.8
 * Description  :
 */
@Data
public class FlowAuditUpdateReq {

    @ApiModelProperty("审核任务id")
    @NotNull(message = "审核任务id必填")
    private Long id;

    @ApiModelProperty("角色名称")
    @Size(max = 100, message = "角色名超长")
    private String role;

    @ApiModelProperty("审核参数, 审核时额外的参数")
    @Size(max = 100, message = "审核参数超长")
    private String auditParam;

    @ApiModelProperty("项目组参数,如果是项目组角色，需要额外配置的参数")
    @Size(max = 100, message = "项目组参数超长")
    private String teamParam;
}
