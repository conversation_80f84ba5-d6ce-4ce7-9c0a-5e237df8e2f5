package com.wxbc.rhine3.model.order.vo.req;

import com.wxbc.rhine3.model.order.vo.FieldMappingItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * financing-service
 *
 * <AUTHOR> chen cheng
 * Date         : 2022/12/5 10:00
 * Jdk          : 1.8
 * Description  :
 */
@Data
public class FieldMappingAddReq {

    @ApiModelProperty(value = "实体类型,1-融资,2-订单", allowableValues = "[1,2]")
    @NotNull(message = "实体类型必填")
    private Integer entityType;

    @ApiModelProperty(value = "映射方向,1-in,2-web,3-事件推送", allowableValues = "[1,2,3]")
    @NotNull(message = "映射方向必填")
    private Integer mappingWay;

    @ApiModelProperty("所属公司")
    private String companyId;

    @ApiModelProperty("所属租户")
    private String tenantId;

    @Valid
    @ApiModelProperty("字段映射列表")
    private List<FieldMappingItem> fieldList;
}
