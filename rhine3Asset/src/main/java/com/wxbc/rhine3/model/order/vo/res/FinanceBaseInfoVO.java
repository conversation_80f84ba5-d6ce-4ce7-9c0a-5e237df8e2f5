package com.wxbc.rhine3.model.order.vo.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * User: yanh<PERSON><PERSON>
 * Date: 2022/11/7
 * Time: 18:12
 * Description: 融资详情 baseInfo
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class FinanceBaseInfoVO extends FinanceListVO{
    @ApiModelProperty(value = "年化利率")
    private BigDecimal rate;

    @ApiModelProperty(value = "融资费用")
    private String cost;

    @ApiModelProperty(value = "金融机构uuid")
    private String fiUuid;

    @ApiModelProperty(value = "金融机构名称")
    private String fiName;

    @ApiModelProperty(value = "起息日")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date valueDate;


    @ApiModelProperty(value = "审核理由")
    private String reason;


}