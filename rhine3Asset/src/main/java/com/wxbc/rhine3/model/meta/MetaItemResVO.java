package com.wxbc.rhine3.model.meta;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * financing-service
 *
 * <AUTHOR> chen cheng
 * Date         : 2022/12/7 10:12
 * Jdk          : 1.8
 * Description  :
 */
@Data
public class MetaItemResVO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("元数据id")
    private Long metaId;

    /**
     * 字段名(编码用)
     */
    @ApiModelProperty("字段名(编码用)")
    private String fieldName;

    /**
     * 显示名
     */
    @ApiModelProperty("显示名")
    private String displayName;

    /**
     * 字段类型
     */
    @ApiModelProperty("字段类型")
    private String fieldType;

    @ApiModelProperty("字段长度")
    private Integer fieldLength;

    @ApiModelProperty("子字段列表")
    private List<MetaItemResVO> childList;

    /**
     * 是否必填
     */
    @ApiModelProperty("是否必填,0-允许为空，1-不允许为空")
    private Integer required;

    @ApiModelProperty("是否主要字段,0-非主要字段, 1-主要字段")
    private Integer primaryField;

    @ApiModelProperty("是否允许修改,0-允许修改, 1-不允许修改")
    private Integer allowUpdate;
}
