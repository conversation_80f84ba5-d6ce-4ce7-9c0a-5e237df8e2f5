package com.wxbc.rhine3.model.order.vo.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * User: yanhengfu
 * Date: 2022/11/7
 * Time: 17:10
 * Description: 订单融资 详情页 数据结构
 */
@Data
@ApiModel("订单融资详情")
public class FinanceDetailVO {

    @ApiModelProperty(value = "融资信息")
    private FinanceBaseInfoVO financeInfo;

    @ApiModelProperty(value = "订单信息")
    private FinanceOrderInfoVO orderInfo;


}