package com.wxbc.rhine3.model.order.dto;

import lombok.Data;

/**
 * financing-service
 *
 * <AUTHOR> chen cheng
 * Date         : 2022/11/23 11:44
 * Jdk          : 1.8
 * Description  :
 */
@Data
public class MetaItemDTO {


    private Long metaId;

    /**
     * 字段
     */
    private String fieldName;

    /**
     * 显示名称
     */
    private String displayName;

    /**
     * 字段的类型
     */
    private String fieldType;

    /**
     * 字段长度，0-不限制
     */
    private Integer fieldLength;

    /**
     * 0-允许为空，1-不允许为空
     */
    private Integer required;

    /**
     * 允许更新
     */
    private Integer allowUpdate;

}
