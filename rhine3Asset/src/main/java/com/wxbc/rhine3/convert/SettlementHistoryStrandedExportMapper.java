package com.wxbc.rhine3.convert;

import com.wxbc.rhine3.bean.response.SettlementHistoryStrandedExportResponse;
import com.wxbc.rhine3.bean.settlement.SettlementStranded;
import com.wxbc.rhine3.util.NumberFormatUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date : 2022-10-24 16:56
 * @Description :
 */
@Mapper(uses = UtilMapper.class, imports = NumberFormatUtil.class)
public interface SettlementHistoryStrandedExportMapper {
    SettlementHistoryStrandedExportMapper instance = Mappers.getMapper(SettlementHistoryStrandedExportMapper.class);

    @Mapping(target = "dueDate", expression = "java(UtilMapper.convertDate( settlementStranded.getDueDate()))")
    @Mapping(target = "couAmount", expression = "java(NumberFormatUtil.thousandsFormatAnd2f(settlementStranded.getCouAmount()))")
    SettlementHistoryStrandedExportResponse source2history(SettlementStranded settlementStranded);

    List<SettlementHistoryStrandedExportResponse> source2historyList(List<SettlementStranded> settlementStrandedList);

}
