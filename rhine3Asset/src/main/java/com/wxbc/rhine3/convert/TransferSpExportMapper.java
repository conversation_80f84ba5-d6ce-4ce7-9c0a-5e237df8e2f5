package com.wxbc.rhine3.convert;

import com.wxbc.cou.manager.api.constant.TransferStatus;
import com.wxbc.cou.manager.api.domain.Transfer;
import com.wxbc.rhine3.bean.transfer.TransferExportSpResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <p>描述</p>
 *
 * <AUTHOR>
 * @since 2022/2/11
 **/
@Mapper(uses = {UtilMapper.class})
public interface TransferSpExportMapper {
    TransferSpExportMapper instance = Mappers.getMapper(TransferSpExportMapper.class);

    @Mapping(target = "status", qualifiedByName = "convertStatus")
    @Mapping(target = "sumTransferAmountInYuan", expression = "java(UtilMapper.convertCent2Yuan(transferListResponse.getSumTransferAmountInCent()))")
    @Mapping(target = "createDate", expression = "java(UtilMapper.convertDate( transferListResponse.getCreateTime()))")
    @Mapping(target = "contractCode",ignore = true)
    @Mapping(target = "name",ignore = true)
    @Mapping(target = "amount",ignore = true)
    @Mapping(target = "signDate",ignore = true)
    TransferExportSpResponse source2Target(Transfer transferListResponse);

    List<TransferExportSpResponse> source2TargetList(List<Transfer> transferListResponse);


    @Named("convertStatus")
    default String convertStatus(String status) {
        switch (TransferStatus.valueOf(status)){
            case CONFIRMED:
                return "支付成功";
            case REJECTED:
            case WITHDRAWN:
                return "已拒绝";
            case WAITING:
                return "审核中";
            default:{
                return null;
            }
        }
    }


}
