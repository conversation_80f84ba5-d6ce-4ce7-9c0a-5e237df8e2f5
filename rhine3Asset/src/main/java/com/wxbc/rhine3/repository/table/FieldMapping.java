package com.wxbc.rhine3.repository.table;

/**
 * Description : auto generated table constants
 * JDK         : 1.8
 * ProjectName : financing-service
 * Date        : 2022-12-08 18:19:17
 * <AUTHOR> admin
 */
public class FieldMapping {
    private FieldMapping() {
        //doNothing
    }

    /**
     * pk
     */
    public static final String ID = "id";

    /**
     * 创建时间
     */
    public static final String CREATE_TIME = "create_time";

    /**
     * 更新时间
     */
    public static final String UPDATE_TIME = "update_time";

    /**
     * 0-删除,1-正常
     */
    public static final String STATUS = "status";

    /**
     * 实体类型 1-融资, 2-订单
     */
    public static final String ENTITY_TYPE = "entity_type";

    /**
     * 映射方向 1-in, 2-out
     */
    public static final String MAPPING_WAY = "mapping_way";

    /**
     * 所属公司, 0-全局使用
     */
    public static final String COMPANY_ID = "company_id";

    /**
     * 租户id, 0-全局使用
     */
    public static final String TENANT_ID = "tenant_id";

    /**
     * 创建人
     */
    public static final String CREATOR_ID = "creator_id";

}