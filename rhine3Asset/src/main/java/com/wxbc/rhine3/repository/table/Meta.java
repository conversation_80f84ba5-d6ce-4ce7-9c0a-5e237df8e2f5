package com.wxbc.rhine3.repository.table;

/**
 * Description : auto generated table constants
 * JDK         : 1.8
 * ProjectName : financing-service
 * Date        : 2022-12-08 18:19:17
 * <AUTHOR> admin
 */
public class Meta {
    private Meta() {
        //doNothing
    }

    /**
     * pk
     */
    public static final String ID = "id";

    /**
     * 创建时间
     */
    public static final String CREATE_TIME = "create_time";

    /**
     * 更新时间
     */
    public static final String UPDATE_TIME = "update_time";

    /**
     * 0-删除,1-正常
     */
    public static final String STATUS = "status";

    /**
     * 元数据类型,1-融资，2-订单
     */
    public static final String ENTITY_TYPE = "entity_type";

    /**
     * 所属公司, 0-全局使用
     */
    public static final String COMPANY_ID = "company_id";

    /**
     * 租户id, 0-全局使用
     */
    public static final String TENANT_ID = "tenant_id";

    /**
     * 创建人
     */
    public static final String CREATOR_ID = "creator_id";

}