package com.wxbc.rhine3.repository.mapper;

import com.wxbc.rhine3.bean.StatisticalCorePara;
import com.wxbc.rhine3.bean.contract.Contract;
import com.wxbc.rhine3.bean.contract.ContractCreatePara;
import com.wxbc.rhine3.bean.contract.ContractQueryPara;
import com.wxbc.rhine3.bean.contract.OoQueryContractListPara;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;

public interface ContractDao {

    @Insert("INSERT INTO contract(status,uuid,contract_code,name,product_name," +
            "company_buyer_uuid,company_buyer_name,company_seller_uuid,company_seller_name,amount,used_amount," +
            "sign_date,start_date,end_date,file_url,operator,operate_user_id,operating_organization_uuid,team_no)" +
            "values (#{status},#{uuid},#{contractCode},#{name},#{productName}," +
            "#{companyBuyerUuid},#{companyBuyerName},#{companySellerUuid},#{companySellerName},#{amount},#{usedAmount}, " +
            "#{signDate},#{startDate},#{endDate},#{fileUrl},#{operator},#{operateUserId},#{operatingOrganizationUuid},#{teamNo})")
    int insertContract(ContractCreatePara createPara);

    @Update(" update contract set used_amount = used_amount+#{payAmount} " +
            " where uuid = #{uuid} and amount>=used_amount+#{payAmount} ")
    int updateContractUsedAmount(@Param("payAmount") BigDecimal payAmount,
                                 @Param("uuid") String uuid);

    @Update(" update contract set used_amount =used_amount - #{payAmount} " +
            " where uuid = #{uuid} and used_amount>=#{payAmount} ")
    int subtractContractUsedAmount(@Param("payAmount") BigDecimal payAmount,
                                   @Param("uuid") String uuid);


    @Update("update contract set " +
            "status = #{status}," +
            "contract_code = #{contractCode}," +
            "name = #{name}," +
            "product_name = #{productName}," +
            "company_buyer_uuid = #{companyBuyerUuid}," +
            "company_buyer_name = #{companyBuyerName}," +
            "company_seller_uuid = #{companySellerUuid}," +
            "company_seller_name = #{companySellerName}," +
            "amount = #{amount}," +
            "sign_date = #{signDate}," +
            "start_date = #{startDate}," +
            "end_date = #{endDate}," +
            "file_url = #{fileUrl}," +
            "operator = #{operator} " +
            "where uuid = #{uuid}")
    int updateContract(ContractCreatePara createPara);

    @Select("<script>" +
            "select * from contract  " +
            " where operator = #{currCompanyUuid}   " +
            "<if test=\"contractCode != null and contractCode != ''\"> " +
            "  and contract_code like concat('%', #{contractCode},'%')" +
            "</if>" +
            "<if test=\"companyBuyerUuid != null and companyBuyerUuid != ''\"> " +
            "and company_buyer_uuid=#{companyBuyerUuid} </if>" +
            "<if test=\"companySellerUuid != null and companySellerUuid != ''\"> " +
            "and company_seller_uuid=#{companySellerUuid} </if>" +
            "<if test=\"availableAmountLimit==1\"> " +
            " and amount-used_amount>0 </if>" +
            " order by create_time desc " +
            "</script>")
    List<Contract> queryContracts(ContractQueryPara queryPara);

    @Select("<script>" +
            "select * from contract  " +
            " where operating_organization_uuid = #{operatingOrganizationUuid}   " +
            "<if test=\"contractCode != null and contractCode != ''\"> " +
            "  and contract_code=#{contractCode}" +
            "</if>" +
            "<if test=\"companyBuyerUuidList != null and companyBuyerUuidList.size()>0\"> " +
                " and company_buyer_uuid in " +
                "<foreach collection='companyBuyerUuidList' item='item' open='(' close=')' separator=','>" +
                "   #{item}" +
                "</foreach>" +
            "</if>" +
            "<if test=\"companySellerUuidList != null and companySellerUuidList.size()>0\"> " +
                " and company_seller_uuid in " +
                "<foreach collection='companySellerUuidList' item='item' open='(' close=')' separator=','>" +
                "   #{item}" +
                "</foreach>" +
            " </if>" +
            "<if test=\"startCreateTime != null\"> " +
            "and create_time &gt;= #{startCreateTime}  </if>" +
            "<if test=\"endCreateTime != null\"> " +
            "and create_time &lt;= #{endCreateTime}  </if>" +
            " order by create_time desc " +
            "</script>")
    List<Contract> ooQueryContracts(OoQueryContractListPara queryPara);

    @Select("<script>" +
            "select * from contract  " +
            " where  company_seller_uuid=#{companySellerUuid} " +
            " order by create_time desc " +
            "</script>")
    List<Contract> refundContracts(ContractQueryPara queryPara);

    @Select("<script>" +
            "select * from contract  " +
            " where operator = #{currCompanyUuid}   " +
            "<if test=\"operateUserId != null and operateUserId != ''\"> " +
            "  and operate_user_id=#{operateUserId}" +
            "</if>" +
            "<if test=\"contractCode != null and contractCode != ''\"> " +
            "  and contract_code = #{contractCode}" +
            "</if>" +
            "<if test=\"companyBuyerUuid != null and companyBuyerUuid != ''\"> " +
            "and company_buyer_uuid=#{companyBuyerUuid} </if>" +
            "<if test=\"companySellerUuid != null and companySellerUuid != ''\"> " +
            "and company_seller_uuid=#{companySellerUuid} </if>" +
            "<if test=\"teamNo != null and teamNo != ''\"> " +
            "and team_no=#{teamNo} </if>" +
            " order by create_time desc " +
            "</script>")
    List<Contract> queryBuildContracts(ContractQueryPara queryPara);

    @Select("select * from contract where contract_code = #{contractCode} and company_buyer_uuid = #{buyerUuid} ")
    Contract getContractByBuyerAndCode(@Param("buyerUuid") String buyerUuid, @Param("contractCode") String contractCode);

    @Select("select * from contract where uuid =#{uuid}")
    Contract getContractByUuid(@Param("uuid") String uuid);

    @Select("<script>" +
            "select * from contract where uuid in (" +
            " <foreach collection=\"uuidList\" item=\"item\" separator=\",\">" +
            " #{item}" +
            "</foreach> " +
            ")" +
            " order by create_time desc " +
            "</script>")
    List<Contract> getContractByUuids(@Param("uuidList") List<String> uuidList);


    @Select("<script> " +
            " select count(id) from contract" +
            " <where>" +
            " <if test=\"startDate != null \"> and create_time &gt;= #{startDate} </if> " +
            " <if test=\"endDate != null \"> and create_time &lt; #{endDate} </if> " +
            " </where></script>")
    Integer countUploadContract(StatisticalCorePara para);

    @Select("<script>" +
            "SELECT\n" +
            "\tt2.* \n" +
            "FROM\n" +
            "\t`asset_package_relation` t1\n" +
            "\tLEFT JOIN contract t2 ON t1.asset_uuid = t2.uuid \n" +
            "WHERE\n" +
            "\tt1.asset_type = #{assetType} \n" +
            "\tAND t1.package_uuid = #{packageUuid}" +
            " order by t2.create_time desc" +
            "</script>")
    List<Contract> queryAssetPackageContracts(@Param("packageUuid") String packageUuid, @Param("assetType") String assetType);


    @Select("SELECT\n" +
            "\tt1.* \n" +
            "FROM\n" +
            "\tcontract t1 left join asset_package_relation t2 on t1.uuid = t2.asset_uuid\n" +
            "WHERE\n" +
            " \n" +
            "\tt1.operator = #{currCompanyUuid} \n" +
            "\tAND (t1.company_buyer_uuid = #{companyBuyerUuid} or t1.company_buyer_name = #{companyBuyerName}) \n" +
            "\tAND (t1.company_seller_uuid = #{companySellerUuid} or t1.company_seller_name = #{companySellerName} )\n" +
            "  and ISNULL(t2.id)\n" +
            "Order BY\n" +
            "\tt1.create_time DESC")
    List<Contract> selectAssetPackageContracts(@Param("currCompanyUuid") String currCompanyUuid,
                                               @Param("companyBuyerUuid") String companyBuyerUuid, @Param("companySellerUuid") String companySellerUuid,
                                               @Param("companyBuyerName") String companyBuyerName, @Param("companySellerName") String companySellerName);

    @Select("<script> " +
            " select sum(amount) from contract" +
            " <where>" +
            " <if test=\"startDate != null \"> and create_time &gt;= #{startDate} </if> " +
            " <if test=\"endDate != null \"> and create_time &lt; #{endDate} </if> " +
            " </where></script>")
    BigDecimal sumUploadContractAmount(StatisticalCorePara para);
}
