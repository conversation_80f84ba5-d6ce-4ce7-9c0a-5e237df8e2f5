package com.wxbc.rhine3.repository.table;


/**
 * Description : auto generated entity
 * JDK         : 1.8
 * ProjectName : vena
 * Date        : 2023-01-04 17:14:55
 * <AUTHOR> zhengjiao
 */

public class FinanceZdw {
    private FinanceZdw() {
        //doNothing
    }

    /**
     * id
     */
    public static final String ID = "id";

    /**
     * 创建时间
     */
    public static final String CREATE_TIME = "create_time";

    /**
     * 更新时间
     */
    public static final String UPDATE_TIME = "update_time";

    /**
     *0-登记失败，1-登记成功,2-线上补登记，3-线下补登记，4-不登记
     */
    public static final String STATUS = "status";

    /**
     *
     */
    public static final String FINANCE_UUID = "finance_uuid";

    /**
     * 融资申请编号
     */
    public static final String FINANCE_NUMBER = "finance_number";

    /**
     * 融资企业uuid
     */
    public static final String COMPANY_UUID = "company_uuid";


    /**
     * 金融机构公司uuid
     */
    public static final String FI_UUID = "fi_uuid";

    /**
     * 金融机构公司uuid
     */
    public static final String OO_UUID = "operating_organization_uuid";

    /**
     * FINANCE-融信融资，ORDER-订单融资
     */
    public static final String SOURCE = "source";

    public static final String REG_TYPE = "reg_type";

    public static final String TIME_LIMIT = "time_limit";

    public static final String ZDW_REG_NO = "zdw_reg_no";

    public static final String ZDW_AUTH_CODE = "zdw_auth_code";

    public static final String ZDW_AMEND_NO = "zdw_amend_no";

    public static final String ZDW_EXTEND_NO = "zdw_extend_no";

    public static final String REQUEST_NO = "request_no";

    public static final String REQUEST_DATA = "request_data";
    public static final String REGISTER_STATUS = "register_status";
}
