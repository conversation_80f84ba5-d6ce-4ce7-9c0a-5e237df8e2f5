package com.wxbc.rhine3.repository.table;

/**
 * Description : auto generated table constants
 * JDK         : 1.8
 * ProjectName : financing-service
 * Date        : 2022-12-02 14:35:08
 * <AUTHOR> admin
 */
public class FieldMappingItem {
    private FieldMappingItem() {
        //doNothing
    }

    /**
     * pk
     */
    public static final String ID = "id";

    /**
     * 创建时间
     */
    public static final String CREATE_TIME = "create_time";

    /**
     * 更新时间
     */
    public static final String UPDATE_TIME = "update_time";

    /**
     * 0-删除,1-正常
     */
    public static final String STATUS = "status";

    /**
     * 所属mapping
     */
    public static final String MAPPING_ID = "mapping_id";

    /**
     * 源字段
     */
    public static final String SRC_FIELD = "src_field";

    /**
     * 源字段类型
     */
    public static final String SRC_TYPE = "src_type";

    /**
     * 目标字段
     */
    public static final String DST_FIELD = "dst_field";

    /**
     * 目标字段类型
     */
    public static final String DST_TYPE = "dst_type";

    /**
     * 0-非主要字段, 1-主要字段
     */
    public static final String PRIMARY_FIELD = "primary_field";

}