package com.wxbc.rhine3.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.wxbc.rhine3.repository.entity.OrderFinanceExtEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * Description : auto generated dao
 * JDK         : 1.8
 * ProjectName : financing-service
 * Date        : 2022-11-23 14:58:41
 * <AUTHOR> admin
 */
public interface OrderFinanceExtDao extends BaseMapper<OrderFinanceExtEntity> {


    @Select("<script>" +
            "   SELECT `id`, `create_time`, `update_time`, `status`, `reason`, `deposit_hash`, `tx_hash`, `tx_status`, " +
            "   `block_height`, `deposit_time`, `deposit_data`, `ext` FROM `order_finance_ext` " +
            "   where `id` in " +
            "   <foreach item=\"item\" collection=\"idList\" open=\"(\" close=\")\" index=\"index\" separator=\",\">" +
            "       #{item}" +
            "   </foreach>" +
            "</script>")
    @Results(id="orderFinanceExtResultMap", value = {
            @Result(column = "id", property = "id"),
            @Result(column = "create_time", property = "createTime"),
            @Result(column = "update_time", property = "updateTime"),
            @Result(column = "status", property = "status"),
            @Result(column = "reason", property = "reason"),
            @Result(column = "deposit_hash", property = "depositHash"),
            @Result(column = "tx_hash", property = "txHash"),
            @Result(column = "tx_status", property = "txStatus"),
            @Result(column = "block_height", property = "blockHeight"),
            @Result(column = "deposit_time", property = "depositTime"),
            @Result(column = "deposit_data", property = "depositData", typeHandler = JacksonTypeHandler.class),
            @Result(column = "ext", property = "ext", typeHandler = JacksonTypeHandler.class)
    })
    List<OrderFinanceExtEntity> queryExtByIdList(@Param("idList")List<Long> financeIdList);

}