package com.wxbc.rhine3.repository.mapper;

import com.wxbc.rhine3.bean.settlement.SettlementPushHistory;
import com.wxbc.rhine3.bean.settlement.SettlementPushHistoryListQueryPara;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * @company: 万向区块链
 * @description 清分数据查询类dao
 * @author: lixingxing
 * @create: 2020-10-26 11:29
 **/
public interface SettlementPushHistoryDao {

    String TABLE = "settlement_push_history";

    String TABLE_COLUMNS = "id,create_time,update_time,is_deleted,batch_no,settle_date," +
            "settle_push_status, settle_cgbz," +
            "settle_process_status,settle_return_msg,file_name,cou_publisher,cou_count,cou_amount,finance_flag";




    @Insert("insert into "+ TABLE + " (batch_no,settle_date,settle_process_status,file_name,cou_publisher,cou_count,cou_amount,finance_flag,payer_account,payer_account_name,minio_url) "
    + "values (#{batchNo},#{settleDate},#{settleProcessStatus},#{fileName},#{couPublisher},#{couCount},#{couAmount},#{financeFlag},#{payerAccount},#{payerAccountName},#{minioUrl})")
    int insertSettlementPushHistory(SettlementPushHistory data);


    @Update((" update "+ TABLE+" set settle_push_status= #{settlePushStatus}, settle_process_status = #{settleProcessStatus} where batch_no = #{batchNo}"))
    int updateSettleProcessStatusByBatchNo(@Param("batchNo") String batchNo,@Param("settlePushStatus") Integer settlePushStatus, @Param("settleProcessStatus") String settleProcessStatus);


    @Update((" update "+ TABLE+" set settle_process_status = #{settleProcessStatus},file_name=#{fileName},minio_url = #{minioUrl} where batch_no = #{batchNo}"))
    int updateSettleProcessStatusAndFileNameByBatchNo(@Param("batchNo") String batchNo, @Param("settleProcessStatus") String settleProcessStatus, @Param("fileName") String fileName, @Param("minioUrl") String minioUrl);



    @Update((" update "+ TABLE+" set settle_cgbz = #{cgbz}, settle_process_status = #{settleProcessStatus} ,settle_return_msg = #{settleReturnMsg}, settle_kkflag = #{kkflag}, settle_pczt = #{pczt} where batch_no = #{batchNo}"))
    int updateSettleResultByBatchNo(@Param("batchNo") String batchNo, @Param("cgbz") Integer cgbz, @Param("settleProcessStatus") String settleProcessStatus,@Param("settleReturnMsg") String settleReturnMsg, @Param("kkflag") Integer kkflag, @Param("pczt") String pczt);


    @Select("select * from "+ TABLE +" where settle_date = #{settleDate} and is_deleted=0 and finance_flag = #{financeFlag}")
    List<SettlementPushHistory> selectSettlementsBySettleDate(@Param("settleDate") String settleDate,@Param("financeFlag") Integer financeFlag);



    @Select("select * from "+ TABLE +" where id = #{id} and settle_process_status = #{settleProcessStatus} and is_deleted=0 ")
    SettlementPushHistory selectSettlementsById(@Param("id") Long id,@Param("settleProcessStatus") String settleProcessStatus);


    @Select("select * from "+ TABLE +" where id = #{id} and settle_push_status != #{settlePushStatus} and is_deleted=0 ")
    SettlementPushHistory selectUnNoticedSettlementsById(@Param("id") Long id,@Param("settlePushStatus") Integer settlePushStatus);

    @Select("select * from "+ TABLE +" where settle_date = #{settleDate} and is_deleted=0 and finance_flag = #{financeFlag} and settle_process_status =#{settleProcessStatus}")
    List<SettlementPushHistory> selectBySettleDateAndTypeAndProcessStatus(@Param("settleDate") String settleDate, @Param("financeFlag") Integer financeFlag, @Param("settleProcessStatus") String settleProcessStatus);

    @Select("select * from "+ TABLE +" where settle_date = #{settleDate} and is_deleted=0 and finance_flag = #{financeFlag} and (settle_process_status =#{settleProcessStatus} or settle_process_status =#{settleFailedProcessStatus})")
    List<SettlementPushHistory> selectBySettleDateAndTypeAndTwoProcessStatus(@Param("settleDate") String settleDate, @Param("financeFlag") Integer financeFlag, @Param("settleProcessStatus") String settleProcessStatus, @Param("settleFailedProcessStatus") String settleFailedProcessStatus);

    @Select("select * from "+ TABLE +" where settle_date = #{settleDate} and is_deleted=0 and finance_flag = #{financeFlag} and settle_process_status =#{settleProcessStatus} and cou_publisher=#{couPublisher}")
    List<SettlementPushHistory> selectByCouPublisherAndStatus(@Param("settleDate") String settleDate,
                                                                          @Param("financeFlag") Integer financeFlag,
                                                                          @Param("settleProcessStatus") String settleProcessStatus,
                                                                          @Param("couPublisher") String couPublisher);





    //select * from pledge_asset.settlement_push_history sph where  settle_date='20240407' and  not exists (select id from pledge_asset.settlement_push_history sph1 where sph.cou_publisher = sph1.cou_publisher and settle_type='finance');
    @Select("select * from "+ TABLE +" a where settle_date = #{settleDate} and is_deleted=0  and (settle_process_status=#{settleProcessStatus} or settle_process_status=#{settleFailedProcessStatus}) and " +
            " not exists (select id from settlement_push_history f where a.cou_publisher = f.cou_publisher and f.settle_date = #{settleDate} and f.finance_flag=#{financeFlag})")
    List<SettlementPushHistory> selectOnlyHoldData(@Param("settleDate") String settleDate,
                                                   @Param("financeFlag") Integer financeFlag,
                                                   @Param("settleProcessStatus") String settleProcessStatus,
                                                   @Param("settleFailedProcessStatus") String settleFailedProcessStatus
                                                   );
    @Select("select * from "+ TABLE +" where settle_date = #{settleDate} and is_deleted=0 and settle_process_status =#{settleProcessStatus}")
    List<SettlementPushHistory> selectBySettleDateAndProcessStatus(@Param("settleDate") String settleDate,@Param("settleProcessStatus") String settleProcessStatus);



    @Select("<script>" +
            "select * from " + TABLE +
            " <where> " +
            "<if test=\"pubKey != null and pubKey != ''\"> " +
            "  cou_publisher =#{pubKey} " +
            "</if>" +
            "<if test=\"financeFlag != null \"> " +
            "  and finance_flag =#{financeFlag} " +
            "</if>" +
            "<if test=\"batchNo != null and batchNo != ''\"> " +
            "  and batch_no like concat('%',#{batchNo},'%')" +
            "</if>" +
            "<if test=\"payerCompany != null and payerCompany != ''\"> " +
            "  and cou_publisher =#{payerCompany}" +
            "</if>" +
            "<if test=\"settlePushStatus != null \"> " +
            "  and settle_push_status =#{settlePushStatus} " +
            "</if>" +
            "<if test=\"settleKkflag != null \"> " +
            "  and settle_kkflag =#{settleKkflag} " +
            "</if>" +
            "<if test=\"settleBeginDate != null and settleEndDate != null \">" +
            "  and settle_date &gt;= #{settleBeginDate} and settle_date &lt;= #{settleEndDate} " +
            "</if>" +
            " </where> " +
            "order by  if(settle_kkflag = 0 or settle_kkflag = 1 ,0,1),create_time desc"+
            "</script>")
    List<SettlementPushHistory> queryPushHistoryList(SettlementPushHistoryListQueryPara params);

    @Select("select * from "+ TABLE +" where settle_date < #{settleDate} and is_deleted=0 and settle_process_status =#{settleProcessStatus}")
    List<SettlementPushHistory> selectNotTodayPushSuccessData(@Param("settleDate") String settleDate,@Param("settleProcessStatus") String settleProcessStatus);

    @Select("<script>" +
            "select * from "+ TABLE +
            " <where> " +
            "id in " +
            "<foreach collection='idList' item='item' open='(' close=')' separator=','>" +
            "   #{item}" +
            "</foreach>" +
            " </where> " +
            "</script>")
    List<SettlementPushHistory> selectSettlementListByIdList(@Param("idList") List<Long> idList);

    @Select("<script>" +
            "select * from "+ TABLE +
            " <where> " +
            "batch_no in " +
            "<foreach collection='batchNoList' item='item' open='(' close=')' separator=','>" +
            "   #{item}" +
            "</foreach>" +
            " </where> " +
            "</script>")
    List<SettlementPushHistory> selectSettlementListByBatchNoList(@Param("batchNoList") List<String> batchNoList);
}
