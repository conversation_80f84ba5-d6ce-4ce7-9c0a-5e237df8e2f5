package com.wxbc.rhine3.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * Description : auto generated entity
 * JDK         : 1.8
 * ProjectName : financing-service
 * Date        : 2022-12-08 18:16:55
 * <AUTHOR> admin
 */
@Data
@TableName("`flow_audit`")
public class FlowAuditEntity {

    /**
     * pk
     */
    @TableId(value = "`id`", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建时间
     */
    @TableField("`create_time`")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("`update_time`")
    private LocalDateTime updateTime;

    /**
     * 0-删除,1-正常
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 流程的key
     */
    @TableField("`flow_key`")
    private String flowKey;

    /**
     * 流程任务名称
     */
    @TableField("`task_name`")
    private String taskName;

    /**
     * 所属公司
     */
    @TableField("`company_id`")
    private String companyId;

    /**
     * 所属租户
     */
    @TableField("`tenant_id`")
    private String tenantId;

    /**
     * 审核的角色
     */
    @TableField("`role`")
    private String role;

    /**
     * 审核需要的额外参数
     */
    @TableField("`audit_param`")
    private String auditParam;

    /**
     * 项目组参数
     */
    @TableField("`team_param`")
    private String teamParam;

    /**
     * 是否是外部审核任务
     */
    @TableField("`external_task`")
    private Integer externalTask;

    /**
     * 创建者id
     */
    @TableField("`creator_id`")
    private Long creatorId;

    /**
     * 该任务节点在流程中的顺序
     */
    @TableField("`task_order`")
    private Integer taskOrder;


}