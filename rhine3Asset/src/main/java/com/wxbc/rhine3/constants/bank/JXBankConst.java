package com.wxbc.rhine3.constants.bank;

/**
 * @company: 万向区块链
 * @description 江西银行业务常量
 * @author: lixingxing
 * @create: 2020-09-18 10:08
 **/
public class JXBankConst {
    private JXBankConst() {
        //do nothing
    }

    public static final  int FINANCE_CALLBACK_STATUS_BY_LOAN = 1;
    public static final  int FINANCE_CALLBACK_STATUS_BY_REJECT_LOAN = 0;

    public static final  String FINANCE_URL = "/paygate/fund/FinaceApply";

    public static final  String SETTLEMENT_URL = "/paygate/fund/FinanPay";

    public static final  String SETTLEMENT_QUERY_URL = "/paygate/fund/QueryPayResult";

    public static final  String MOCK_SETTLEMENT_QUERY_URL = "/mock/paygate/fund/QueryPayResult";

    public static final  String JXB_FILE_URL_SEPARATOR_AT = "@";

    public static final  int JX_BANK_SETTLEMENT_FINANCE_FLAG_1 = 1;


    //江西银行中登网登记相关信息，用于人工手动调接口进行变更登记
    public static final String JXBANK_DEBTEE_NAME="江西银行股份有限公司";
    public static final String JXBANK_FININST_CODE="B0792H236010001";
    public static final String JXBANK_INDUSTRY_REGISTRATION_CODE="913601007055009885";
    public static final String JXBANK_DETAIL_ADDRESS="红谷滩新区金融大街699号";


}
