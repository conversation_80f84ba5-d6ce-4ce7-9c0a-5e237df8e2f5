package com.wxbc.rhine3.constants.zdw;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/12/13
 */
@Getter
public enum ZdwDebtType {

    FI("01", "金融机构"),
    E("02", "企业"),
    PI("03", "事业单位");

    private String code;
    private String desc;

    ZdwDebtType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ZdwDebtType from(String code) {
        for (ZdwDebtType zdwDebtType : ZdwDebtType.values()) {
            if (zdwDebtType.getCode().equals(code)) {
                return zdwDebtType;
            }
        }
        return null;
    }
}
