package com.wxbc.rhine3.constants;

import java.util.Arrays;

public enum FinanceProductTypeEnum {
    ORDER(0, "订单融资")

    ;

    private int code;
    private String desc;

    FinanceProductTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(int code) {
        return Arrays.stream(FinanceProductTypeEnum.values()).filter(x -> x.code == code).findFirst().map(FinanceProductTypeEnum::getDesc).orElse(null);
    }

    public static FinanceProductTypeEnum financeProductType(int code) {
        return Arrays.stream(FinanceProductTypeEnum.values()).filter(x -> x.code == code).findFirst().orElse(null);
    }
}
