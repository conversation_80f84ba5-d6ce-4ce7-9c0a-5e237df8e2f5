package com.wxbc.rhine3.constants.workflow;

import lombok.Getter;
import lombok.Setter;

public enum WorkflowProcessDefinitionKeyEnum {
    PROCESS_DEFINITION_KEY_COU_CREATE("create","融信开立流程"),
    PROCESS_DEFINITION_KEY_COU_PAY("pay","融信支付流程"),

    PROCESS_DEFINITION_KEY_DIRECT_FINANCE("finance","直连模式融资：例如江西银行融资流程"),
    PROCESS_DEFINITION_KEY_NON_DIRECT_FINANCE("rlFinance","非直连模式融资：例如润楼融资流程"),


    /**
     * todo val需要修改的对应工作流
     */
    //PROCESS_DEFINITION_KEY_PLEDGE_FINANCE("pledgeFinance","质押融资模式：例如江阴银行质押融资流程"),
    PROCESS_DEFINITION_KEY_PLEDGE_FINANCE("pledgeFinanceFinal","质押融资模式：例如江阴银行质押融资流程"),

    PROCESS_DEFINITION_KEY_REFUND_CREATE("refundCreate","融信开立退款流程"),
    PROCESS_DEFINITION_KEY_PLEDGE_REFUND_CREATE("pledgeRefundCreate","江阴银行融信开立退款流程"),
    PROCESS_DEFINITION_KEY_REFUND_PAY("refundPay","融信支付退款流程"),

    PROCESS_DEFINITION_KEY_COU_FINANCE_DESTROY("couFinanceDestroy","融资cou核销流程"),
    PROCESS_DEFINITION_KEY_COU_NON_FINANCE_DESTROY("couNonFinanceDestroy","非融资cou核销流程"),

    PROCESS_DEFINITION_KEY_ORDER_FINANCE("nonDirectOrderFinance", "订单融资审核流程"),

    PROCESS_DEFINITION_KEY_ORDER_FINANCE_DIRECT("directOrderFinance", "订单融资直连审核流程"),

    ;

    @Getter
    @Setter
    String val;

    @Getter
    @Setter
    String desc;

    WorkflowProcessDefinitionKeyEnum(String val, String desc) {
        this.val = val;
        this.desc = desc;
    }
}
