package com.wxbc.rhine3.constants.sign;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 付款凭证签章参数，与金融机构无关
 */

@Component
@RefreshScope
public class DestroyPaySignConfig {

    @Getter
    @Value("${destroy.pay.signature.type:2}")
    private String signType;
    @Getter
    @Value("${destroy.pay.coordinateX:30}")
    private String coordinateX;
    @Getter
    @Value("${destroy.pay.coordinateY:40}")
    private String coordinateY;
    @Getter
    @Value("${destroy.pay.pageIndex:1}")
    private String signPageIndex;

}
