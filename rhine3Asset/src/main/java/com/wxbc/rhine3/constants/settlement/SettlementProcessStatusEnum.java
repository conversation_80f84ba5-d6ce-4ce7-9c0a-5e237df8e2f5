package com.wxbc.rhine3.constants.settlement;

import lombok.Getter;
import lombok.Setter;

/**
 * 清分类型enum
 * value = "清分批次处理状态：dataInitialized:数据初始化，fileGenerateSuccess:文件生成成功，noticeSendSuccess:通知银行成功，fiSettleSuccess:银行清分成功,fiSettleFail:银行清分失败'"
 */
public enum SettlementProcessStatusEnum {

    SETTLEMENT_PROCESS_STATUS_DATA_INITIALIZED("dataInitialized","数据初始化"),
    SETTLEMENT_PROCESS_STATUS_FILE_GENERATE_SUCCESS("fileGenerateSuccess","文件生成成功"),
    SETTLEMENT_PROCESS_STATUS_NOTICE_SEND_SUCCESS("noticeSendSuccess","通知银行成功"),
    SETTLEMENT_PROCESS_STATUS_NOTICE_SEND_FAIL("noticeSendFail","通知银行失败"),
    SETTLEMENT_PROCESS_STATUS_FI_SETTLE_SUCCESS("fiSettleSuccess","银行清分成功"),
    SETTLEMENT_PROCESS_STATUS_FI_SETTLE_FAIL("fiSettleFail","银行清分失败'");


    @Getter
    @Setter
    private String val;

    @Getter
    @Setter
    private String desc;

    SettlementProcessStatusEnum(String val, String desc) {
        this.val = val;
        this.desc = desc;
    }

    public static SettlementProcessStatusEnum getEnum(String val) {
        for (SettlementProcessStatusEnum value : SettlementProcessStatusEnum.values()) {
            if (val.equals(value.getVal())) {
                return value;
            }
        }
        return null;
    }
}
