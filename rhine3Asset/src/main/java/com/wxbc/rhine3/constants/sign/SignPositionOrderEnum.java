package com.wxbc.rhine3.constants.sign;

import lombok.Getter;
import lombok.Setter;

public enum SignPositionOrderEnum {
    ONE(1, "第一个签章位置"),
    TWO(2, "第二个签章位置"),
    THREE(3, "第三个签章位置"),
    FOUR(4, "第四个签章位置");

    @Getter
    @Setter
    private int val;

    @Getter
    @Setter
    private String desc;


    SignPositionOrderEnum(int val,String desc) {
        this.val = val;
        this.desc = desc;
    }

    public static SignPositionOrderEnum getEnum(int val) {
        for (SignPositionOrderEnum value : SignPositionOrderEnum.values()) {
            if (val == (value.getVal())) {
                return value;
            }
        }
        return null;
    }
}
