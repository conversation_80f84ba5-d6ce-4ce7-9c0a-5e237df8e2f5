package com.wxbc.rhine3.constants.settlement;

import lombok.Getter;

/**
 * @author: shang<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/4/10 18:22
 * @description:
 */
@Getter
public enum JyHkztEnum {

    INIT("0","初始"),
    FAILED("1","失败"),
    TIMEOUT("3","超时"),
    SUCCESS("9","成功"),
    ;

    private String code;

    private String msg;

    JyHkztEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
