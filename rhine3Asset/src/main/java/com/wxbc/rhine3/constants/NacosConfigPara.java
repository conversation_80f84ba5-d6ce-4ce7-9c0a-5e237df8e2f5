package com.wxbc.rhine3.constants;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Component
@RefreshScope
public class NacosConfigPara {
    @Getter
    @Value("${cou.api.couServiceUrl:http://10.200.195.43:9079}")
    private String couServiceUrl;

    @Getter
    @Value("${asset.couDueDateIncrements:1}")
    private int couDueDateIncrements;

    @Getter
    @Value("${user.redisPwd:123}")
    private String redisPwd;

    @Getter
    @Value("${zdw.timeLimit:12}")
    private String zdwTimeLimit;

    @Getter
    @Value("${zdw.username:wxoperator}")
    private String zdwUsername;

    @Getter
    @Value("${zdw.password:wxoperator#2021}")
    private String zdwPassword;

    @Getter
    @Value("${remind.of.duePayment.day.offset:6}")
    private int remindOfDuePaymentDayOffset;

    @Getter
    @Value("${asset.periodOfGraceByC:360}")
    private int periodOfGraceByC;

    @Getter
    @Value("${asset.periodOfGraceMin:1}")
    private int periodOfGraceMin;

    @Getter
    @Value("${sms.finance.apply.tmpCodeHash:a4a488b737594f19894044475a853800}")
    public String smsFinanceApplyTmpCodeHash;

    @Getter
    @Value("${oss.admin.dir.permanent:pledge}")
    private String permanent;

    @Getter
    @Value("${oss.admin.func.finance:finance}")
    private String funcFinance;

    @Getter
    @Value("${oss.admin.func.transfer:transfer}")
    private String funcTransfer;

    @Getter
    @Value("${oss.admin.func.destroy:destroy}")
    private String funcDestroy;

    @Getter
    @Value("${sms.finance.zdw.reg.failed.tmpCodeHash:9ee0f9d57dd14d9cbf9427fc80292d70}")
    public String smsFinanceZdwRegFailedTmpCodeHash;

    @Getter
    @Value("${sms.finance.zdw.duplicateCheck.failed.tmpCodeHash:8f42e751cc2b4a228fa4fef909da4e50}")
    public String smsFinanceZdwDuplicateCheckFailedTmpCodeHash;

    @Getter
    @Value("${sms.finance.zdw.terminateReg.failed.tmpCodeHash:aef30928893547e1bd13b9754edbd581}")
    public String smsFinanceTerminateRegFailedTmpCodeHash;

    @Getter
    @Value("${oo.contract.list.export.limit:1000}")
    public int ooContractListExportLimit;

    @Getter
    @Value("${oo.invoice.list.export.limit:1000}")
    public int ooInvoiceListExportLimit;

    @Getter
    @Value("${available.invoice.check.flag:2}")
    public int availableInvoiceCheckFlag;

    @Getter
    @Value("${available.invoice.buy.seller.limit.flag:0}")
    public int availableInvoiceBuySellerFlag;



}
