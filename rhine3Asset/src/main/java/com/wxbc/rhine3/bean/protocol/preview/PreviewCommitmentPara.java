package com.wxbc.rhine3.bean.protocol.preview;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "预览付款承诺函协议")
@Data
public class PreviewCommitmentPara {
    @NotBlank
    @ApiModelProperty(value = "融信收款方公司uuid",required = true)
    private String receiverUuid;

    @NotNull
    @DecimalMin(value = "0.01",message = "金额必须大于0")
    @ApiModelProperty(value = "支付金额(单位:分)", required = true)
    private BigDecimal couAmountInCent;

    @NotNull
    @ApiModelProperty(value = "兑付到期日13位时间戳", required = true)
    private Date dueDate;

    @NotBlank
    @ApiModelProperty(value = "cou编号", required = true)
    private String couNo;

    @NotBlank
    @ApiModelProperty(value = "金融机构公司uuid", required = true)
    private String fiUuid;
}
