package com.wxbc.rhine3.bean.settlement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 重发批次参数
 *
 * <AUTHOR>
 * @since 2024/5/30
 */
@Data
@ApiModel("重发批次参数")
public class ResendSettlementPushHistoryPara {
    @ApiModelProperty(value = "主键列表", example = "[1,2,3]", required = true)
    @NotEmpty(message = "id列表不能为空")
    private List<Long> idList;
}
