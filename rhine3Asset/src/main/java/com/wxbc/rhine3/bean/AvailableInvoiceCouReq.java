package com.wxbc.rhine3.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
@Data
@Valid
public class AvailableInvoiceCouReq {
    @NotBlank(message = "融信uuid不能为空")
    @ApiModelProperty(value = "融信Cou的uuid")
    private String couUuid;
    @NotBlank(message = "融信原始uuid不能为空")
    @ApiModelProperty(value = "融信Cou原始的uuid")
    private String originalUuid;
}