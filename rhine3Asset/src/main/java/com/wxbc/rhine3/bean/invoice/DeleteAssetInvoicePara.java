package com.wxbc.rhine3.bean.invoice;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

@Getter
@Setter
@ToString(callSuper = true)
@ApiModel
public class DeleteAssetInvoicePara {

    @ApiModelProperty(value = "资产包uuid",required = true)
    @NotBlank(message = "资产包uuid不能为空")
    private String assetPackageUuid;

    @ApiModelProperty(value = "发票uuid",required = true)
    @NotBlank(message = "发票uuid不能为空")
    private String invoiceUuid;

}
