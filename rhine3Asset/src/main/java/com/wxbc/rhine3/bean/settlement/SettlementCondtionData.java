package com.wxbc.rhine3.bean.settlement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @company: 万向区块链
 * @description
 * @author: lixingxing
 * @create: 2022-01-12 10:34
 **/
@Data
@ApiModel
public class SettlementCondtionData {

    @ApiModelProperty("是否是融资数据")
    private Boolean financeFlag;

    @ApiModelProperty("是否需要跳过")
    private Boolean continueFlag;
}
