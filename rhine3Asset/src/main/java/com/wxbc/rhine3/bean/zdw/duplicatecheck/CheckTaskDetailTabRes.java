package com.wxbc.rhine3.bean.zdw.duplicatecheck;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel("查重任务详情中tab页结果")
@AllArgsConstructor
@NoArgsConstructor
public class CheckTaskDetailTabRes {

    @ApiModelProperty("查重任务详情中tab中列表命中发票的id集合")
    private List<Long> hits;

    @ApiModelProperty("查重任务详情中tab中列表未命中发票的id集合")
    private List<Long> missHits;


}
