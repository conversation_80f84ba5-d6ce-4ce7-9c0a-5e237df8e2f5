package com.wxbc.rhine3.bean.protocol;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 协议名称 ：《应收账款转让通知（to上一手支付人）
 * 描述：供应商融资通知上一手支付人
 * (已经核对模板)
 */
@Data
@ApiModel
public class TransferNotifyTPayTemplatePara {

    @ApiModelProperty(value = "发送方(持有人)")
    private String holderCompanyName;

    @ApiModelProperty(value = "接收方(转让融信所在流转记录的付款方)")
    private String transferCouFromCompanyName;

    @ApiModelProperty(value = "发送日期", example = "年月日")
    private String financeValueDate;

    @ApiModelProperty(value = "融信编号")
    private String couNo;

    @ApiModelProperty(value = "融资申请者公司全称")
    private String applicantCompanyName;

    @ApiModelProperty(value = "金融机构公司全称")
    private String fiCompanyName;

    @ApiModelProperty(value = "融信金额", example = "1000,000.00")
    private String couAmount;
}
