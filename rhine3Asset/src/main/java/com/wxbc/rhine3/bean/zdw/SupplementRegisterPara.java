package com.wxbc.rhine3.bean.zdw;

import com.wxbc.rhine3.common.constant.SupplementRegistrationType;
import com.wxbc.rhine3.constants.FinanceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel("中登网待补登操作参数")
public class SupplementRegisterPara {
    @ApiModelProperty(value = "登记类别", allowableValues = "ONLINE-线上，OFFLINE-线下，NON_REGISTRATION-不登记",required = true)
    @NotNull(message = "登记类别不能为空")
    private SupplementRegistrationType type;

    @ApiModelProperty(value = "融资申请编号",required = true)
    @NotBlank(message = "融资申请编号不能为空")
    private String financeNumber;

    @ApiModelProperty(value = "融资类型",notes = "目前只有融信融资，传FINANCE就行了",required = true)
    @NotNull(message = "融资类型不能为空")
    private FinanceTypeEnum financeType;

    @ApiModelProperty(value = "线下登记初始登记编号")
    private String initRegistrationNo;

}
