package com.wxbc.rhine3.bean.finance;

import com.wxbc.rhine3.bean.FinanceInvoicePara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel
public class FinanceCreatePara {

    @Valid
    @NotNull
    @ApiModelProperty(value = "融资创建基础参数",required = true)
    private FinanceCreateBasePara financeCreateBasePara;

    @Valid
    @NotNull
    @ApiModelProperty(value = "融资创建费率相关参数",required = true)
    private FinanceCreateRatePara financeCreateRatePara;

    @NotEmpty
    @Valid
    @ApiModelProperty(value = "必选，交易COU列表",required = true)
    List<SplitDetailCouExtPara> splitDetails;

    @NotEmpty
    @Valid
    @ApiModelProperty(value = "必选，交易发票列表",required = true)
    List<FinanceInvoicePara> invoiceList;

    @ApiModelProperty(value = "一手Cou标识")
    private boolean firsthand;

    /**
     * 协议类型 pledge-质押 pledgeFinance--借款（）
     */
    @ApiModelProperty(value = "协议类型,预览文件的时候传",required = true)
    private String protocolType;

}