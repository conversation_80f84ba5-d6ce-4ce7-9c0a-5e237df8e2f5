package com.wxbc.rhine3.bean.zdw.duplicatecheck;

import com.wxbc.rhine3.common.PagePara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date : 2024-01-24 11:36
 * @Description :
 */
@Data
@ApiModel("融资查重记录列表查询条件")
@EqualsAndHashCode(callSuper = true)
public class FinanceDuplicateQueryPara extends PagePara {
    //日期搜索是闭区间
    @ApiModelProperty(value = "融资创建日开始:yyyy-MM-dd")
    private String startCreateDate;

    @ApiModelProperty(value = "融资创建日结束:yyyy-MM-dd")
    private String endCreateDate;

    @ApiModelProperty(value = "融资申请编号")
    private String applicationNumber;
}
