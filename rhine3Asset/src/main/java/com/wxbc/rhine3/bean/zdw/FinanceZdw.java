package com.wxbc.rhine3.bean.zdw;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class FinanceZdw {

    /**
     * 0-登记失败，1-登记成功,2-线上补登记，3-线下补登记，4-不登记
     */
    @ApiModelProperty(name = "0-登记失败，1-登记成功,2-线上补登记，3-线下补登记，4-不登记")
    private Integer status;

    /**
     * 融资uuid
     */
    @ApiModelProperty(name = "融资uuid")
    private String financeUuid;

    /**
     * 融资编号
     */
    @ApiModelProperty(name = "融资编号")
    private String financeNumber;

    /**
     * FINANCE-融信融资，ORDER-订单融资
     */
    @ApiModelProperty(name = "FINANCE-融信融资，ORDER-订单融资")
    private String source;

    /**
     * 中登网登记类型
     * A00200-应收账款转让,A00100-应收账款质押
     */
    @ApiModelProperty(name = "中登网登记类型 A00200-应收账款转让,A00100-应收账款质押")
    private String regType;

    /**
     * 中登网登记编号
     */
    @ApiModelProperty(name = "中登网登记编号")
    private String zdwRegNo;

    /**
     * 中登网修改码(用户后续进行展期,变更登记时候使用)
     */
    @ApiModelProperty(name = "中登网修改码(用户后续进行展期,变更登记时候使用)")
    private String zdwAuthCode;

    /**
     * 中登网变更登记编号
     */
    @ApiModelProperty(name = "中登网变更登记编号")
    private String zdwAmendNo;

    /**
     * 中登网展期登记编号
     */
    @ApiModelProperty(name = "中登网展期登记编号")
    private String zdwExtendNo;

}
