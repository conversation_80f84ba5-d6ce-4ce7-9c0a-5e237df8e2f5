package com.wxbc.rhine3.bean.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 发票验真接口（第三方接口）的返回参数
 */

@Getter
@Setter
@ToString
@ApiModel
public class CheckInvoiceFromApiResponse  {

    /**状态返回码*/
    @ApiModelProperty("状态返回码")
    private String returnCode;
    /**响应数据*/
    @ApiModelProperty("响应数据")
    private CheckInvoiceResponse data;
    /**响应状态说明*/
    @ApiModelProperty("响应状态说明")
    private String returnDesc;


}


