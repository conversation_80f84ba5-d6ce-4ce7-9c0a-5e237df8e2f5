package com.wxbc.rhine3.bean;

import com.wxbc.rhine3.common.BaseInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class InvoiceCou extends BaseInfo {
    @ApiModelProperty(value = "流转uuid")
    private String transferUuid;
    @ApiModelProperty(value = "发票uuid")
    private String invoiceUuid;
}
