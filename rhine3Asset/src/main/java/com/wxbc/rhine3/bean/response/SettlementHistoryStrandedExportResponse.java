package com.wxbc.rhine3.bean.response;

import cn.hutool.core.annotation.Alias;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 請勿改动因为会影响excel生成字段顺序
 **/
@Data
@ApiModel
public class SettlementHistoryStrandedExportResponse {
    @Alias("融信编号")
    String couNo;
    @ApiModelProperty(value = "cou金额:元,千分位格式")
    @Alias("金额(￥)")
    String couAmount;

    @ApiModelProperty(value = "cou持有方的公司的名称")
    @Alias("持有方")
    String couHolderName;

    @ApiModelProperty(value = "cou发行方的公司的名称")
    @Alias("开立方")
    String couPublishName;

    @ApiModelProperty(value = "COU到期时间(格式:yyyy-MM-DD)")
    @Alias("兑付到期日")
    String dueDate;
}
