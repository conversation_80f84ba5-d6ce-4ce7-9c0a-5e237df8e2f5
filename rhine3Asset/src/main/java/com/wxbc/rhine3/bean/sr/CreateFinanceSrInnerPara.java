package com.wxbc.rhine3.bean.sr;

import com.wxbc.rhine3.bean.contract.Contract;
import com.wxbc.rhine3.bean.finance.CreateFinanceBaseInnerPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * @company: 万向区块链
 * @description 融资创建逻辑中后端需要用到的上饶参数
 **/
@Data
@ApiModel
@EqualsAndHashCode(callSuper = true)
public class CreateFinanceSrInnerPara extends CreateFinanceBaseInnerPara {
    @ApiModelProperty(value = "要拆分COU关联的交易合同，找零COU需要向上追溯父辈COU")
    Map<String, Contract> fromCouRelationContractMap;

    @ApiModelProperty(value = "原始COU关联的交易合同")
    Map<String, Contract> originalCouContractMap;

    @ApiModelProperty(value = "原始COU关联的融信转让承诺函")
    Map<String, String> originalCouPromiseMap;


}
