package com.wxbc.rhine3.bean.transfer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@ApiModel
public class TransferCompany {
    @ApiModelProperty(value = "公钥")
    private String couPubKey;
    @ApiModelProperty(value = "企业名")
    private String companyName;
    @ApiModelProperty(value = "企业uuid")
    private String companyUuid;
    @ApiModelProperty(value = "企业类型")
    private String companyType;
    @ApiModelProperty(value = "融资付息方式(supplier-供应商付息 center-核心企业付息)")
    private String interestPayWay;
}
