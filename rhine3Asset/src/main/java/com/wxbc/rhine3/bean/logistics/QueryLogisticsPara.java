package com.wxbc.rhine3.bean.logistics;

import com.wxbc.rhine3.common.PagePara;
import com.wxbc.rhine3.constants.asset.PackageAssetTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper=true)
@ApiModel("资产包查询物流列表")
public class QueryLogisticsPara extends PagePara {

    @ApiModelProperty(value = "资产包uuid",required = true)
    @NotBlank(message = "资产包uuid不能为空")
    private String assetPackageUuid;

    @ApiModelProperty(value = "物流编号")
    private String logisticsNo;

    @ApiModelProperty(value = "资产类型",hidden = true)
    private String assetType = PackageAssetTypeEnum.LOGISTICS.name();
}
