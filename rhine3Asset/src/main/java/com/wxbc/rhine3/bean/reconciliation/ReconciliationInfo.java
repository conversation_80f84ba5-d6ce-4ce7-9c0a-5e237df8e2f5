package com.wxbc.rhine3.bean.reconciliation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ReconciliationInfo {

    @ApiModelProperty("对账年")
    private String year;

    @ApiModelProperty("对账季度")
    private String quarter;

    @ApiModelProperty("数据截至日期")
    private String createTime;

//    @ApiModelProperty("融资明细")
//    private List<ReconciliationFinance> financeList;
//
//    @ApiModelProperty("融资余额明细")
//    private List<ReconciliationBalance> dateList;

    @ApiModelProperty("融资明细和融资余额明细")
    private ReconciliationFileTable table;

    @ApiModelProperty("季度首日")
    private String firstDayOfQuarter;

    @ApiModelProperty("季度尾日")
    private String lastDayOfQuarter;

    @ApiModelProperty("季度天数")
    private String daysOfQuarter;

    @ApiModelProperty("季度每天融资余额之和")
    private String deductNpaReaminAmountOfQuarter;

    @ApiModelProperty("季度融资日均")
    private String dailyAmountOfQuarter;

    @ApiModelProperty("季度融资日均 2亿以内服务费")
    private String dailyServiceFee1OfQuarter;

    @ApiModelProperty("季度融资日均 2亿~5亿服务费")
    private String dailyServiceFee2OfQuarter;

    @ApiModelProperty("季度融资日均 5亿~10亿服务费")
    private String dailyServiceFee3OfQuarter;

    @ApiModelProperty("季度融资日均 10亿以上服务费")
    private String dailyServiceFee4OfQuarter;

    @ApiModelProperty("不良资产服务费")
    private String npaChargeServiceFeeOfQuarter;

    @ApiModelProperty("季度应支付服务费")
    private String payableServiceFeeOfQuarter;




}
