package com.wxbc.rhine3.bean.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel
public class CheckInvoiceResponse {

    @ApiModelProperty(value = "请求code")
    private String code;

    @ApiModelProperty(value = "请求hostid")
    private String hostId;

    @ApiModelProperty(value = "发票类型：01-专票，02-货运运输业增值税专用票，03-机动车销售统一发票，04-普票，10-电子票，11-增值税普通发票（卷式），14-增值税普通发票（通行费）,15-二手车发票")
    private String invoiceType;

    //01-专票,04-普票,10-电子票

    @ApiModelProperty(value = "请求唯一ID")
    private String requestId		;
    @ApiModelProperty(value = "发票代码")
    private String invoiceCode		;   
    @ApiModelProperty(value = "发票号码")
    private String invoiceNumber	;
    @ApiModelProperty(value = "开票日期：YYYY-MM-DD")
    private String billingDate		;
    @ApiModelProperty(value = "发票金额")
    private String totalAmount		;
    @ApiModelProperty(value = "校验码")
    private String checkCode		;
    @ApiModelProperty(value = "购方税号")
    private String purchaserTaxNo	;       
    @ApiModelProperty(value = "购方名称")
    private String purchaserName	;       
    @ApiModelProperty(value = "校验码")
    private String salesTaxNo		;       
    @ApiModelProperty(value = "销方名称")
    private String salesName		;       
    @ApiModelProperty(value = "价税合计")
    private String amountTax		;       
    @ApiModelProperty(value = "发票状态 0 正常，1 失控，2 作废作废，3 红冲、7 部分红冲、8 全额红冲")
    private String state			;       
    @ApiModelProperty(value = "机器编码")
    private String machineCode		;       
    @ApiModelProperty(value = "查验时间")
    private String checkTime		;       
    @ApiModelProperty(value = "购方开户行账户")
    private String purchaserBank	;       
    @ApiModelProperty(value = "购方地址电话")
    private String purchaserAddressPhone;	
    @ApiModelProperty(value = "销方地址电话")
    private String salesAddressPhone	;	
    @ApiModelProperty(value = "销方开户行账户")
    private String salesBank			;	
    @ApiModelProperty(value = "合计税额")
    private String totalTax			;		
    @ApiModelProperty(value = "备注")
    private String remarks				;	
    @ApiModelProperty(value = "成品油标志 Y成品油发票 N⾮成品油发票")
    private String refinedOilState		;	
    @ApiModelProperty(value = "货物明细")
    private List<InvoiceGoodsDetail> items;

    //02货运运输业增值税专用票

    @ApiModelProperty(value = "购方开户行账户")
    private String receiverTaxNo	;  
    @ApiModelProperty(value = "收货人名称")
    private String receiverName		;  
    @ApiModelProperty(value = "发货人识别号")
    private String senderTaxNo		;  
    @ApiModelProperty(value = "发货人名称")
    private String senderName		;  
    @ApiModelProperty(value = "运输货物信息")
    private String goodsInfo		;  
    @ApiModelProperty(value = "起运地、经由、到达地")
    private String transportRoute	;  
    @ApiModelProperty(value = "税率")
    private String taxRate			;  
    @ApiModelProperty(value = "税控盘号")
    private String taxPanelNo		;  
    @ApiModelProperty(value = "车种车号")
    private String vehicleTypeNo	;  
    @ApiModelProperty(value = "吨位")
    private String tonnage			;  
    @ApiModelProperty(value = "主管税务机关代码")
    private String taxAuthorityNo	;  
    @ApiModelProperty(value = "主管税务名称")
    private String taxAuthorityName	;  
    

    //03-机动车销售统一发票,04-普票,10-电子票

    @ApiModelProperty(value = "身份证号码/组织机构代码")
    private String idCardNo			;   
    @ApiModelProperty(value = "车辆类型")
    private String vehicleType		;   
    @ApiModelProperty(value = "厂牌型号")
    private String brandModel		;   
    @ApiModelProperty(value = "产地")
    private String originPlace		;   
    @ApiModelProperty(value = "合格证号")
    private String certificateNo	;   
    @ApiModelProperty(value = "商检单号")
    private String inspectionListNo	;    
    @ApiModelProperty(value = "发动机号")
    private String engineNo			;    
    @ApiModelProperty(value = "进口证明书号")
    private String importCertificateNo	;    
    @ApiModelProperty(value = "车辆识别代号/车架号码")
    private String vehicleNo			;    
    @ApiModelProperty(value = "销方电话")
    private String salesPhone			;    
    @ApiModelProperty(value = "销方开户行账号")
    private String salesBankNo			;    
    @ApiModelProperty(value = "销方地址")
    private String salesAddress		;    
    @ApiModelProperty(value = "完税凭证号码")
    private String paymentVoucherNo	;    
    @ApiModelProperty(value = "限乘人数")
    private String passengersLimited	;    
    @ApiModelProperty(value = "代开标志 1：自开 2：代开")
    private String supplySign			;    
    @ApiModelProperty(value = "特殊政策标识")
    private String specialPolicySign	;    
    @ApiModelProperty(value = "实际税率")
    private String realTaxRate			;    
    @ApiModelProperty(value = "实际税额")
    private String realTax 			;    



    //11-增值税普通发票（卷式）

    @ApiModelProperty(value = "售货员")
    private String payee			;


    //14-增值税普通发票（通行费）

    @ApiModelProperty(value = "通行费标志")
    private String tollSign				;

    //15-二手车发票

    @ApiModelProperty(value = "购方地址")
    private String purchaserAddress	;
    @ApiModelProperty(value = "购方电话")
    private String purchaserPhone		;
    @ApiModelProperty(value = "车牌照号")
    private String carNumber			;
    @ApiModelProperty(value = "登记证号")
    private String registration		;
    @ApiModelProperty(value = "车辆类型")
    private String goodType			;
    @ApiModelProperty(value = "转入地车管所名称")
    private String transferVehicle		;
    @ApiModelProperty(value = "经营、拍卖单位")
    private String auctionUnit			;
    @ApiModelProperty(value = "经营、拍卖单位地址")
    private String auctionUnitAddress	;
    @ApiModelProperty(value = "经营、拍卖单位纳税人识别号")
    private String auctionUnitTaxNo	;
    @ApiModelProperty(value = "开户银行及账号")
    private String auctionBankAndNo	;
    @ApiModelProperty(value = "经营、拍卖单位电话")
    private String auctionUnitPhone	;
    @ApiModelProperty(value = "二手车市场")
    private String usedCarMarket		;
    @ApiModelProperty(value = "二手车市场纳税人识别号")
    private String usedCarMarketTaxNo	;
    @ApiModelProperty(value = "二手车市场地址")
    private String usedCarMarketAddress;
    @ApiModelProperty(value = "二手车市场开户银行及账号")
    private String usedCarBankAndNo	;
    @ApiModelProperty(value = "二手车市场电话")
    private String usedCarPhone		;

}
