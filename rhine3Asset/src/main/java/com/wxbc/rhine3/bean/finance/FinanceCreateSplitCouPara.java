package com.wxbc.rhine3.bean.finance;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @company: 万向区块链
 * @description 融资创建选择拆分的cou参数
 * @author: lixingxing
 * @create: 2022-07-01 15:48
 **/
@Data
@ApiModel
public class FinanceCreateSplitCouPara {

    @NotBlank
    @ApiModelProperty(value = "待使用的cou uuid",required = true)
    @Size(max = 64)
    private String fromCouUuid;

    @NotBlank
    @ApiModelProperty(value = "待使用的cou的原始couUuid",required = true)
    @Size(max = 64)
    private String originalUuid;

    @NotNull
    @ApiModelProperty(value = "给对方的cou的数额，单位：分 RMB",required = true)
    private BigDecimal toCouAmountInCent;

    @NotNull
    @ApiModelProperty(value = "留给对接平台自己校验，Sarah本身不校验到期日",
            name = "dueDate", example = "2000-01-01", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dueDate;

    @NotBlank
    @ApiModelProperty(value = "给对方的cou编号，可不传，传入时应当保证唯一性",required = true,example = "COU21011991000001")
    private String toCouNo;

    @NotBlank
    @ApiModelProperty(value = "找零的cou编号，可不传，传入时应当保证唯一性",required = true,example = "COU21011991000001")
    private String changeCouNo;
}
