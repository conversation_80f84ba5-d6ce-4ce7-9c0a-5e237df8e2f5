package com.wxbc.rhine3.bean.settlement;

import com.wxbc.rhine3.common.PagePara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("清分异常查询参数类")
public class SettlementStrandedQueryPara extends PagePara {

    @ApiModelProperty(value = "cou编号")
    private String couNo;

    @ApiModelProperty(value = "cou开立方公司名称")
    private String publishName;

    @ApiModelProperty(value = "兑付开始日期")
    private Date dueBeginDate;

    @ApiModelProperty(value = "兑付结束日期")
    private Date dueEndDate;

    @ApiModelProperty(value = "cou发行方的公司的pubKey", hidden = true)
    private String couPublishPubKey;

    @ApiModelProperty(value = "想要排除的状态值(默认排除success)", hidden = true)
    private String notStatus;

    @ApiModelProperty(value = "运营机构的公司uuid", hidden = true)
    private String operatingOrganizationUuid;

}
