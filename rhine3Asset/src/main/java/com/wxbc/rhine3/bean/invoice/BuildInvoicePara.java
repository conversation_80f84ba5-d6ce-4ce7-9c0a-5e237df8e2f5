package com.wxbc.rhine3.bean.invoice;

import com.wxbc.rhine3.common.PagePara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Size;

@Getter
@Setter
@ToString(callSuper = true)
@ApiModel
public class BuildInvoicePara extends PagePara {

    @ApiModelProperty(value = "操作方公司uuid",hidden = true)
    private String operateUuid;

    @ApiModelProperty(value = "操作员id",hidden = true)
    private String operateUserUuid;

    @ApiModelProperty(value = "发票号码",required = true)
    @Size(min=1, max=10,message = "发票号码10字符限制")
    private String invoiceNumber;

    @ApiModelProperty(value = "发票验真标识")
    private int checkFlag = 2;
    @ApiModelProperty(value = "发票验真状态 0 正常，1 失控，2 作废作废，3 红冲、7 部分红冲、8 全额红冲")
    private String checkState;

    @ApiModelProperty(value = "买方公司名称")
    private String buyerName;

    @ApiModelProperty(value = "项目组编号")
    private String teamNo;
}