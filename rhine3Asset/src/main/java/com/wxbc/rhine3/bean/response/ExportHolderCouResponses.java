package com.wxbc.rhine3.bean.response;

import cn.hutool.core.annotation.Alias;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

//請勿改动因为会影响excel生成字段顺序
@Data
@ApiModel
public class ExportHolderCouResponses {
    @Alias("融信编号")
    String couNo;

    @Alias("持有方")
    private String holderName;
    @Alias("开立方")
    String publishName;
    @Alias("授信方")
    String creditName;

    @Alias("金额（¥）")
    BigDecimal couAmountInYuan;

    @Alias("兑付到期日")
    String dueDate;
    @Alias("兑付状态")
    String cashStatus;

    @Alias("质押状态")
    private String pledgeStatus;

    @Alias("原始融信编号")
    String originalCouNo;
    @Alias("原始融信金额（¥）")
    BigDecimal originalCouAmountInYuan;

    @Alias("创建日期")
    String createTime;
}
