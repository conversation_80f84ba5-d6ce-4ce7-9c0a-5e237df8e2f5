package com.wxbc.rhine3.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * @company: 万向区块链
 * @description 关联交易实体类(仅后端使用)
 * @author: lixingxing
 * @create: 2021-05-31 16:45
 **/
@Data
@ApiModel
public class RelationTransferInfo {

    @ApiModelProperty(value = "卖方公司名称")
    private String fromCompanyName;

    @ApiModelProperty(value = "交易关联的合同id集合")
    private Set<String> relationContractUuids;

}
