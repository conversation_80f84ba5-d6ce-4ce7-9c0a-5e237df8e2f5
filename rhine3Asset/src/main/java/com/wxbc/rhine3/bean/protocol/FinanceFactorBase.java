package com.wxbc.rhine3.bean.protocol;

import com.wxbc.rhine3.bean.protocol.jx.FactorInner;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@ApiModel
public class FinanceFactorBase {
    @ApiModelProperty(value = "协议编号: 融资申请编号 ")
    private String applicationNumber;

    @ApiModelProperty(value = "协议申请日期：申请签章日期 ", example = "年月日")
    private String signDate;

    @ApiModelProperty(value = "融资申请企业全称")
    private String applicantCompanyName;

    @ApiModelProperty(value = "融资申请企业社会信用代码")
    private String applicantSocialCode;

    @ApiModelProperty(value = "涉及表格数据,字段名字必须是table")
    private FactorInner table;

    @ApiModelProperty(value = "保理融资金额，单位：元，格式：千分位、保留两位小数", example = "21，410.00")
    private String financeAmountTotal;
    @ApiModelProperty(value = "应收账款金额合计，单位：元，格式：千分位、保留两位小数", example = "21，410.00")
    private String toCouAmountTotal;
    @ApiModelProperty(value = "发票金额合计，单位：元，格式：千分位、保留两位小数", example = "21，410.00")
    private String invoicePreTaxAmountTotal;
    @ApiModelProperty(value = "付款承诺函应付账款 总金额，单位：元，格式：千分位、保留两位小数", example = "21，410.00")
    private String commitmentAmountTotal;

    @ApiModelProperty(value = "保理融资银行企业全称")
    private String financeFi;

    @ApiModelProperty(value = "保理融资到期日，格式：年月日")
    private String financeDueDate;

    @ApiModelProperty(value = "年化利率，格式：%")
    private String interestRate;

    @ApiModelProperty(value = "保理手续费率，格式：%")
    private String factoringRate;

    @ApiModelProperty(value = "平台服务费率，格式：%")
    private String serviceFeeRate;

    @ApiModelProperty(value = "放款账户户名：融资企业的融资收款账户的户名")
    private String bankCardAccountName;

    @ApiModelProperty(value = "放款账户账号：融资企业的融资收款账户的账号")
    private String bankCardAccountNum;

    @ApiModelProperty(value = "放款账户开户行：融资企业的融资收款账户的开户行")
    private String bankCardAccountBank;
}
