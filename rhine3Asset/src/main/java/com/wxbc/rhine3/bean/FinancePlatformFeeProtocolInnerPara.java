package com.wxbc.rhine3.bean;

import com.wxbc.rhine3.repository.entity.ProtocolPlatformFeeSignEntity;
import com.wxbc.rhine3.repository.entity.ProtocolPlatformFeeSignExtEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 融资创建逻辑中后端需要用到的平台服务协议相关的内部参数
 *
 */
@Data
@ApiModel
public class FinancePlatformFeeProtocolInnerPara {

    //平台协议签章后生成的数据库记录主实体
    private ProtocolPlatformFeeSignEntity protocolPlatformFeeSignEntity;

    //平台协议签章首次签章生成的数据库记录扩展实体
    private ProtocolPlatformFeeSignExtEntity firstSignEntity;

    //平台协议签章第二次签章生成的数据库记录扩展实体
    private ProtocolPlatformFeeSignExtEntity secondSignEntity;

}
