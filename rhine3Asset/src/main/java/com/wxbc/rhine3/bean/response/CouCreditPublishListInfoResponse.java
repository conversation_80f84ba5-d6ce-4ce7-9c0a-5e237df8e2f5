package com.wxbc.rhine3.bean.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * @company: 万向区块链
 * @description
 * @author: lixingxing
 * @create: 2021-08-10 16:26
 **/
@Data
@ApiModel("融信的发行方和授信方信息")
public class CouCreditPublishListInfoResponse {
    @ApiModelProperty("cou发行方的公钥key集合")
    private List<KeyValueResponse> publishPubKeys;
    @ApiModelProperty("cou授信方的公钥集合")
    private List<KeyValueResponse> creditPubKeys;
}
