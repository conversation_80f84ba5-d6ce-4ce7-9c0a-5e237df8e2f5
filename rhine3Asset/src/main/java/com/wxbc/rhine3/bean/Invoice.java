package com.wxbc.rhine3.bean;

import com.wxbc.rhine3.common.BaseInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

@Data
@ApiModel
@EqualsAndHashCode(callSuper=true)
public class Invoice extends BaseInfo {

    @ApiModelProperty(value = "卖方公司uuid")
    private String supplier;
    @ApiModelProperty(value = "卖方公司名称")
    private String supplierName;
    @ApiModelProperty(value = "供应商uuid",hidden = true)
    private List<String> supplierUuids;

    @ApiModelProperty(value = "买方公司uuid")
    private String buyer;
    @ApiModelProperty(value = "买方公司名称")
    private String buyerName;
    @ApiModelProperty(value = "供应商uuid",hidden = true)
    private List<String> buyerUuids;
    @ApiModelProperty(value = "购买方名称",hidden = true)
    private String businessName;

    @ApiModelProperty(value = "开票日期")
    private Date invoiceDate;
    @ApiModelProperty(value = "发票类型")
    private String invoiceType;

    @ApiModelProperty(value = "发票代码")
    @Size(min=1, max=12,message = "发票代码12字符限制")
    private String invoiceCode;

    @ApiModelProperty(value = "发票号码")
    @Size(min=1, max=8,message = "发票号码8字符限制")
    private String invoiceNumber;

    @ApiModelProperty(value = "含税金额")
    private String pretaxAmount;

    @ApiModelProperty(value = "不含税金额")
    private String amount;

    @ApiModelProperty(value = "6位校验码")
    private String checkCode;

    @ApiModelProperty(value = "是否要过滤已融资的发票")
    private String filterFinance;

    @ApiModelProperty(value = "操作者公司的uuid",hidden = true)
    private String operator;

    @ApiModelProperty(value = "发票验真标识 1 成功 0 失败或未验真  2 全部")
    private Integer checkFlag = 2;

    @ApiModelProperty(value = "发票验真状态 国票通发票查验接口，返回的发票状态字段值：" +
            "发票验真状态：0 正常，1 失控，2 作废作废，3 红冲、7 部分红冲、8 全额红冲")
    private String checkState;

    @ApiModelProperty(value = "发票验真失败原因")
    private String checkMsg;

    @ApiModelProperty(value = "文件url json串")
    private String fileUrl;

    @ApiModelProperty(value = "当前用户id",hidden = true)
    private String operateUserUuid;

    @ApiModelProperty(value = "项目组编号")
    private String teamNo;

    /**
     * 多余的属性,给前端使用
     */

    @ApiModelProperty(value = "操作企业的名称")
    private String operatorCompanyName;

    @ApiModelProperty(value = "操作用户(手机号)")
    private String operatorUser;

    @ApiModelProperty(value = "已占用金额")
    private String usedAmount;

    @ApiModelProperty(value = "可用金额")
    private String availableAmount;

    @ApiModelProperty(value = "使用金额")
    private String applyAmount;
}
