package com.wxbc.rhine3.bean.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel("更新金融产品配置")
public class UpdateFinanceProductConfigReqVo {

    @ApiModelProperty(value = "产品编号",required = true)
    @NotBlank(message = "产品编号不能为空")
    private String serialNo;

    @ApiModelProperty(value = "融资申请前缀",required = true)
    @NotBlank(message = "请输入融资申请前缀")
    private String applyNoPrefix;

    @ApiModelProperty(value = "对接方式（0-非直连，1-直连）",required = true,allowableValues = "0,1")
    @NotNull(message = "请选择对接方式")
    private int dockingType;

    @ApiModelProperty(value = "对接标识")
    private String dockingIdentity;

    @ApiModelProperty(value = "1 卖方应收账款转让 2 卖方应收账款质押 3 不登记",required = true,allowableValues = "1,2,3")
    @NotNull(message = "请选择中登网登记类型")
    private int zdwAssigneeFlag;

}
