package com.wxbc.rhine3.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;


/**
 * @company: 万向区块链
 * @description
 * @author: lixingxing
 * @create: 2020-08-18 13:52
 **/
@Data
@ApiModel("协议查询参数实体")
public class ProtocolQueryPara {

    @NotBlank
    @ApiModelProperty(value = "金融机构uuid",required =true)
    private String financeUuid;

    @ApiModelProperty(value = "协议类型")
    private List<String> protocolType;


    @ApiModelProperty(value = "金融产品编号")
    private String financeProductNo;
}
