package com.wxbc.rhine3.bean.reconciliation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 按季度生成对账记录参数
 *
 * <AUTHOR>
 * @since 2024/10/15
 */
@Data
@ApiModel("按季度生成对账记录参数")
public class GenerateReconciliationByQuarterRequest {
    @ApiModelProperty("年度")
    private String year;

    @ApiModelProperty("季度")
    private String quarter;
}
