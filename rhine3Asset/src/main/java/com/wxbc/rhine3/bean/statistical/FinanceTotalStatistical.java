package com.wxbc.rhine3.bean.statistical;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel
public class FinanceTotalStatistical {
    @ApiModelProperty(value = "融资放款总笔数")
    private Integer total;

    @ApiModelProperty(value = "融资放款总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "放款融资申请公司数量")
    private Integer companyQuantity;

}
