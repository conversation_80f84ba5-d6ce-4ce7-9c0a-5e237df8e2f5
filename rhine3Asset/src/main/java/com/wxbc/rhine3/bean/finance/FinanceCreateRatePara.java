package com.wxbc.rhine3.bean.finance;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @company: 万向区块链
 * @description 融资创建费率相关参数
 * @author: lixingxing
 * @create: 2022-07-01 15:32
 **/
@Data
@ApiModel
public class FinanceCreateRatePara {
    @Valid
    @ApiModelProperty(value = "年化融资费率",required = true)
    private BigDecimal interestRate;

    @NotNull
    @Valid
    @ApiModelProperty(value = "平台服务费率",required = true)
    private BigDecimal serviceFeeRate;


    @NotNull
    @Valid
    @ApiModelProperty(value = "平台服务费",required = true)
    private BigDecimal serviceFee;

    @NotNull
    @Valid
    @ApiModelProperty(value = "保理费率",required = true)
    private BigDecimal factoringRate;

    @ApiModelProperty(value = "授信类型",required = true)
    private Integer creditType;
}
