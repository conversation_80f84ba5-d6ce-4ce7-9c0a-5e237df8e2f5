package com.wxbc.rhine3.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;

@Data
@ApiModel
public class FinanceInvoicePara {

    @NotBlank
    @ApiModelProperty(value = "发票uuid",required = true)
    private String uuid;

    @ApiModelProperty(value = "发票号码")
    @Size(min=1, max=25,message = "发票号码25字符限制")
    private String invoiceNumber;

    //20221213 161版本修改成发票代码可以为空
    @ApiModelProperty(value = "发票代码")
    private String invoiceCode;

    @ApiModelProperty(value = "卖方公司名称")
    private String supplierName;
    @ApiModelProperty(value = "买方公司名称")
    private String buyerName;

    @ApiModelProperty(value = "不含税金额,单位：元")
    private String amount;
    @ApiModelProperty(value = "含税金额,单位：元")
    private String pretaxAmount;

    @ApiModelProperty(value = "开票日期")
    @Valid
    private Date invoiceDate;

    @ApiModelProperty(value = "发票验真标识 1 成功 0 失败或未验真  2 全部")
    private int checkFlag;

    @ApiModelProperty(value = "文件url json串")
    private String fileUrl;

    @ApiModelProperty(value = "已占用金额")
    private String usedAmount;

    @ApiModelProperty(value = "可用金额")
    private String availableAmount;

    @ApiModelProperty(value = "使用金额")
    private String applyAmount;

}
