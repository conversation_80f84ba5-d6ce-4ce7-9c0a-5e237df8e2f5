package com.wxbc.rhine3.bean.jrcb;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @company: 万向区块链
 * @description 放款通知参数
 * @author: lixingxing
 * @create: 2024-03-21 14:06
 **/
@Data
@ApiModel
public class LoanNoticePara {

    @ApiModelProperty("签名串")
    @NotBlank(message = "签名串不能为空")
    private String signStr;

    @ApiModelProperty("融资企业社会信用代码")
    @NotBlank(message = "融资企业社会信用代码不能为空")
    private String socialCreditCode;

    @ApiModelProperty("融资申请编号")
    @NotBlank(message = "融资申请编号不能为空")
    private String applyNo;

    @ApiModelProperty("融资放款状态 0-拒绝，1-已放款 2-已还款 3-已签署协议")
    @NotNull(message = "融资放款状态不能为空")
    private Integer loanStatus;

    @ApiModelProperty("拒绝信息:loanStatus=0时有值")
    private LoanNoticeRejectInfo rejectInfo;

    @ApiModelProperty("放款信息:loanStatus=1时有值")
    private LoanNoticeLoanInfo loanInfo;

    @ApiModelProperty("还款信息:loanStatus=2时有值")
    private LoanNoticeRefundInfo refundInfo;

    @ApiModelProperty("协议签署信息:loanStatus=3时有值")
    private LoanNoticeSignProtocolInfo signFileInfo;

    @ApiModelProperty("协议签署融资申请企业信息:loanStatus=3时有值")
    private LoanNoticeCompanyInfo companyInfo;

}
