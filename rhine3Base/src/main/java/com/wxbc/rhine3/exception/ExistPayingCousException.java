package com.wxbc.rhine3.exception;

import com.wxbc.rhine3.constants.ReturnCode;

public class ExistPayingCousException  extends Exception {
    private ReturnCode returnCode;
    public ExistPayingCousException(String msg){
        super(msg);
    }

    public ExistPayingCousException(ReturnCode returnCode){
        this.returnCode = returnCode;
    }

    public ReturnCode getReturnCode() {
        return returnCode;
    }
}
