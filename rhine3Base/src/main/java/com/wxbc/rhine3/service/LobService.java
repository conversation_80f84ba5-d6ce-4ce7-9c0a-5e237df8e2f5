package com.wxbc.rhine3.service;


import com.google.common.collect.Lists;
import com.wxbc.rhine3.bean.response.CompanyTypeGroup;
import com.wxbc.rhine3.common.lob.CompanyType;
import com.wxbc.rhine3.common.lob.Lob;
import com.wxbc.rhine3.common.lob.LobConstant;
import com.wxbc.rhine3.common.lob.Project;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * service-vena
 *
 * <AUTHOR> chen cheng
 * Date         : 2023/4/11 14:28
 * Jdk          : 1.8
 * Description  :
 */
@Service
@Slf4j
public class LobService {

    public List<Lob> lobList(Boolean all) {
        boolean includeAll = all == null || all;
        return Arrays.stream(Lob.values())
                .filter(lob -> {
                    if (includeAll) {
                        return true;
                    }
                    return !lob.name().equalsIgnoreCase(LobConstant.ALL);
                })
                .collect(Collectors.toList());

    }

    /**
     * 返回业务线
     * @return List
     */
    public List<Lob> lobList() {
        return lobList(true);
    }


    /**
     * 返回业务线下的端
     * @param lob 业务线
     * @return List
     */
    public List<Project> listProjectByLob(String lob) {
        return Arrays.stream(Project.values()).filter(project -> project.getLob().equals(lob)
                        || LobConstant.ALL.equals(project.getLob()))
                .collect(Collectors.toList());
    }

    /**
     * 获取业务端， 非ALL的端
     * @return Project List
     */
    public List<Project> listAllBizProject() {
        return Arrays.stream(Project.values()).filter(project -> !LobConstant.ALL.equals(project.getLob()))
                .collect(Collectors.toList());
    }

    /**
     * 返回业务线下的企业类型
     * @param lob        业务线
     * @param includeAll 包含非业务线的企业类型
     * @return List
     */
    public List<CompanyType> listCompanyTypeByLob(String lob, Boolean includeAll) {
        boolean all = includeAll != null && includeAll;
        return Arrays.stream(CompanyType.values())
                .filter(c -> {
                    if (all) {
                        return c.getLob().equalsIgnoreCase(lob) || LobConstant.ALL.equalsIgnoreCase(c.getLob());
                    }
                    return c.getLob().equalsIgnoreCase(lob);
                })
                .collect(Collectors.toList());
    }

    /**
     * 查询业务线下的公司类型分组
     * @param lob 业务线
     * @return List
     */
    public List<CompanyTypeGroup> listCompanyTypeGroup(String lob) {
        final Map<Integer, List<CompanyType>> groupMap = listCompanyTypeByLob(lob, false).stream().collect(Collectors.groupingBy(CompanyType::getGroupNo));
        List<CompanyTypeGroup> list = new ArrayList<>();
        groupMap.forEach((k, v) -> {
            CompanyTypeGroup group = new CompanyTypeGroup();
            group.setGroupNo(lob + k);
            group.setCompanyTypeList(v.stream().map(CompanyType::name).collect(Collectors.toList()));
            list.add(group);
        });
        return list;
    }


    /**
     * 过滤给定的企业类型列表
     * 找出列表中属于业务线的企业类型
     * @param lob             业务线
     * @param companyTypeList 公司类型
     * @return List
     */
    public List<String> filterCompanyTypeByLob(Lob lob, Collection<String> companyTypeList) {
        return Arrays.stream(CompanyType.values())
                .filter(c -> c.getLob().equalsIgnoreCase(lob.name()))
                .filter(c -> companyTypeList.contains(c.name())
                        || companyTypeList.contains(c.name().toLowerCase()))
                .map(CompanyType::name)
                .collect(Collectors.toList());
    }

    /**
     * 过滤给定的企业类型列表
     * 找出列表中属于业务端的企业类型
     * @param project         业务端编码
     * @param companyTypeList 公司类型
     * @return List
     */
    public List<String> filterCompanyTypeByProject(Project project, Collection<String> companyTypeList) {
        return Arrays.stream(CompanyType.values())
                .filter(c -> c.getProjectCode().equalsIgnoreCase(project.code()))
                .filter(c -> companyTypeList.contains(c.name()) || companyTypeList.contains(c.name().toLowerCase()))
                .map(CompanyType::name)
                .collect(Collectors.toList());
    }

    /**
     * 过滤给定的企业类型列表
     * 删除列表中属于业务线的企业类型
     * @param lob             业务线
     * @param companyTypeList 公司类型
     * @return List
     */
    public List<String> removeCompanyTypeFromListByLob(Lob lob, Collection<String> companyTypeList) {
        if(CollectionUtils.isEmpty(companyTypeList)) {
            return Lists.newArrayList();
        }

        //查询业务线下的公司类型
        List<String> lobCompanyList = listCompanyTypeByLob(lob).stream().map(CompanyType::name)
                .collect(Collectors.toList());
        //
        return companyTypeList.stream().filter(c -> !lobCompanyList.contains(c))
                .collect(Collectors.toList());
    }

    /**
     * 查询业务线下的公司类型
     * @param lob 业务线
     * @return List
     */
    public List<CompanyType> listCompanyTypeByLob(Lob lob) {
        return Arrays.stream(CompanyType.values())
                .filter(c -> c.getLob().equalsIgnoreCase(lob.name()))
                .collect(Collectors.toList());
    }


    /**
     * 查询业务端的企业类型
     * @return 业务端企业类型列表
     */
    public List<CompanyType> queryBizCompanyType() {
        return Arrays.stream(CompanyType.values()).filter(c -> !c.getLob().equalsIgnoreCase(LobConstant.ALL))
                .collect(Collectors.toList());
    }


    /**
     * 判断企业类型列表是否有属于业务端的企业类型
     * @param project         业务端编码
     * @param companyTypeList 企业类型列表
     * @return true - 有， false - 没有
     */
    public boolean hasCompanyTypeOfProject(Project project, List<String> companyTypeList) {
        return !CollectionUtils.isEmpty(filterCompanyTypeByProject(project, companyTypeList));
    }

    /**
     * 判断企业类型列表是否有属于业务线的企业类型
     * @param lob             业务线
     * @param companyTypeList 企业类型列表
     * @return true - 有， false - 没有
     */
    public boolean hasCompanyTypeOfLob(Lob lob, List<String> companyTypeList) {
        return !CollectionUtils.isEmpty(filterCompanyTypeByLob(lob, companyTypeList));
    }

}
