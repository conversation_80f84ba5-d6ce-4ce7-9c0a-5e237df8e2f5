package com.wxbc.rhine3.service.jrcb;

import com.wxbc.base.util.JsonUtil;
import com.wxbc.rhine3.bean.jrbank.JrBankConstants;
import com.wxbc.rhine3.bean.jrbank.media.JrMediaBaseReq;
import com.wxbc.rhine3.bean.jrbank.media.JrMediaRes;
import com.wxbc.rhine3.config.bank.JiangYinConfig;
import com.wxbc.rhine3.constants.CommonConst;
import com.wxbc.rhine3.constants.ConfigReturnCode;
import com.wxbc.rhine3.httpclient.HttpClient;
import com.wxbc.scaffold.common.definition.response.ResponseFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Objects;

/**
 * @author: shangmengxiao
 * @date: 2024/3/12 9:11
 * @description:
 */
@Service
@Slf4j
public class JrBankMediaService {

    @Resource
    private JiangYinConfig bankConfig;


    /**
     * 调用江阴银行接口
     * @param req
     * @return
     */
    public final ResponseFormat<JrMediaRes> invokeJrcbMedia(JrMediaBaseReq req) {
        String res = sendRequest(req);
        return genResponse(res);
    }


    /**
     * 发送请求到江阴银行影响系统
     * @param req
     * @return
     */
    public String sendRequest(JrMediaBaseReq req) {
        if (Objects.isNull(req)) {
            log.info("rcb invoke media request json is null");
            return null;
        }
        req.setParentId(bankConfig.getMediaParentId());
        req.setTreeId(bankConfig.getMediaTreeId());
        req.setCreateName(bankConfig.getMediaCreateName());
        req.setCreateUser(bankConfig.getMediaCreateUser());
        req.setTitle(bankConfig.getTitle());
        String res = null;
        try {
            String requestJson = JsonUtil.object2String(req);
            if (requestJson.length() <= CommonConst.LOG_PRINT_LMIT_10480_BYTE) {
                log.info("jrcb invoke media request json : {}" ,requestJson);
            }
            res = HttpClient.sendJrcbPost(req.getUrl(), req);
            log.info("jrcb invoke media response json : {}",res);
        } catch (IOException e) {
            log.error("jrcb invoke media exception ,msg : {}",e.getMessage());
        }
        return res;
    }

    /**
     * 生成通用返回
     * @param res
     * @return
     */
    public ResponseFormat<JrMediaRes> genResponse(String res) {

        log.info("交易返回的信息: {}",res);
        if(StringUtils.isBlank(res)){
            return ResponseFormat.fail(ConfigReturnCode.JRCB_SEND_FAILED);
        }

        JrMediaRes jrMediaRes = JsonUtil.jsonStr2Object(res, JrMediaRes.class);


        log.info("交易转化后的信息: {}",jrMediaRes);
        Integer respCode = jrMediaRes.getCode();
        String msg = jrMediaRes.getMsg();
        if (JrBankConstants.MEDIA_RESP_CODE_SUCCESS.equals(respCode)) {
            return ResponseFormat.success(jrMediaRes);
        }else{
            ResponseFormat responseFormat = new ResponseFormat();
            responseFormat.setReturnCode(ConfigReturnCode.JRCB_MEDIA_RESPONSE_FAILED.getValue());
            responseFormat.setReturnDesc(ConfigReturnCode.JRCB_MEDIA_RESPONSE_FAILED.getDesc());
            if(StringUtils.isNotBlank(msg)){
                responseFormat.setReturnDesc(msg);
            }
            return responseFormat;
        }

    }


}
