package com.wxbc.rhine3.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * service-vena
 *
 * <AUTHOR> chen cheng
 * Date         : 2023/4/13 18:11
 * Jdk          : 1.8
 * Description  :
 */
@Data
@ApiModel
public class UpdateUser2AdminReq {

    @NotNull(message = "用户id必填")

    private Long userId;

    @NotEmpty
    @Valid
    @Size(min = 1, message = "业务线和业务端必填, key是业务线，value是业务端")
    @ApiModelProperty(value = "业务线和业务端",required = true)
    private List<LobEntry> entryList;

}
