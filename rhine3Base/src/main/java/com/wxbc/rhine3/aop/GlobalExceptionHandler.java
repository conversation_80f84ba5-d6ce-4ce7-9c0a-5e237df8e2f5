package com.wxbc.rhine3.aop;

import com.wxbc.rhine3.common.Response;
import com.wxbc.rhine3.constants.ReturnCode;
import com.wxbc.rhine3.exception.*;
import com.wxbc.scaffold.common.definition.exception.BizException;
import com.wxbc.scaffold.common.definition.response.ResponseFormat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import javax.servlet.ServletException;
import java.security.SignatureException;

@RestControllerAdvice(basePackages = {"com.wxbc.rhine3","com.wxbc.workflow"})
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(BizException.class)
    public Response servletException(BizException e) {
        log.error(e.getMessage(), e);
        Response response = new Response<>();
        if (null != e.getMessage())
        {
            response = Response.of(e.getReturnCode().getValue(), e.getMessage());
        }
        else
        {
            response = Response.of(e.getReturnCode().getValue(), e.getReturnCode().getDesc());
        }
        return response;
    }
    @ExceptionHandler(Exception.class)
    public Response exception(Exception e) {
        return generateResponse(e);
    }

    @ExceptionHandler(BusinessException.class)
    public Response businessException(BusinessException e) {
        return generateResponse(e);
    }

    @ExceptionHandler(ParameterException.class)
    public Response paramException(ParameterException e) {
        return generateResponse(e);
    }

    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public Response uploadException(MaxUploadSizeExceededException e) {
        log.error(e.getMessage());
        return Response.of(ReturnCode.FILE_SIZE_ERROR);
    }

    @ExceptionHandler(HystrixFallBackException.class)
    public Response hystrixFallbackException(HystrixFallBackException e) {
        return generateResponse(e);
    }

    private Response generateResponse(Exception e){
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    @ExceptionHandler(SignatureException.class)
    public Response signException(SignatureException e) {
        log.error(e.getMessage(), e);
        return Response.of(ReturnCode.INVALID_SIGN);
    }

    @ExceptionHandler(InvalidAccessException.class)
    public Response accessException(InvalidAccessException e) {
        log.error(e.getMessage(), e);
        return Response.of(ReturnCode.WRONG_VERIFY_CODE);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public Response illegalArgumentException(IllegalArgumentException e) {
        return generateResponse(e);
    }

    @ExceptionHandler(PermissionDenyException.class)
    public Response permissionDenyException(PermissionDenyException e) {
        log.error(e.getMessage(), e);
        return Response.of(ReturnCode.PERMISSONDENY);
    }
    @ExceptionHandler(InvalidDataStatusException.class)
    public Response permissionDenyException(InvalidDataStatusException e) {
        log.error(e.getMessage(), e);
        return Response.of(ReturnCode.INVALID_DATA_STATUS);
    }

    @ExceptionHandler(CheckFailException.class)
    public Response checkFailException(CheckFailException e) {
        log.error(e.getMessage(), e);
        return Response.of(ReturnCode.UNI_LOGIN_INCORRECT_NAME_OR_PASSWD);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Response methodArgumentNotValidException(MethodArgumentNotValidException e) {
        log.error("front parameter exception:",e);
        return Response.fail( e.getBindingResult().getFieldError().getDefaultMessage());
    }

    @ExceptionHandler(ServletException.class)
    public Response servletException(ServletException e) {
        log.error(e.getMessage(), e);
        return Response.of(ReturnCode.REQUEST_FAILED,e.getMessage());
    }

}
