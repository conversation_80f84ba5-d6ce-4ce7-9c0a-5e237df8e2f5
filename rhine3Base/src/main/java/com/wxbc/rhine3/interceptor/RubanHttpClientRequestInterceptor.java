package com.wxbc.rhine3.interceptor;

import com.kunlun.crypto.asym.EccCrypto;
import com.wxbc.base.util.MessageFormat;
import com.wxbc.base.util.StringUtil;
import com.wxbc.rhine3.constants.CommonConst;
import com.wxbc.rhine3.constants.ReturnCode;
import com.wxbc.rhine3.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.*;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.protocol.HttpContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Hana HttpClient的请求拦截器
 * <AUTHOR>
 * @date 2023/2/20
 */
@Component
@Slf4j
public class RubanHttpClientRequestInterceptor implements HttpRequestInterceptor {

    private static final String H_SIGN = "wx-gw-sign";

    private static final String H_TIME = "wx-gw-request-time";

    private static final String SIGN_DATA_TEMPLATE = "Content-MD5={}&reqTime={}";

    @Value("${ruban.server.accessKey: }")
    private String accessKey;

    private final EccCrypto eccCrypto = new EccCrypto();

    @Override
    public void process(HttpRequest request, HttpContext context) throws HttpException, IOException {
        String path = request.getRequestLine().getUri();
        if (path.contains(CommonConst.SERVICE_CFCA) || path.contains(CommonConst.SERVICE_DENALI)) {
            try {
                apply(request);
            } catch (URISyntaxException e) {
                throw new BusinessException(ReturnCode.USERNAME_NOT_EXISTED);
            }
        }
    }

    private void apply(HttpRequest request) throws IOException, URISyntaxException {
        long reqTime = System.currentTimeMillis();
        String sign = "";
        String md5Hex = "";
        if (RequestMethod.GET.name().equals(request.getRequestLine().getMethod())) {
            URIBuilder uriBuilder = new URIBuilder(request.getRequestLine().getUri());
            List<NameValuePair> queryParams = uriBuilder.getQueryParams();
            Map<String, String> queries = queryParams.stream()
                    .collect(Collectors.toMap(NameValuePair::getName, NameValuePair::getValue));
            String collect = queries.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .map(e -> e.getKey() + "=" + e.getValue())
                    .collect(Collectors.joining("&"));
            md5Hex = DigestUtils.md5Hex(collect);
        } else if (RequestMethod.POST.name().equals(request.getRequestLine().getMethod())) {
            HttpEntityEnclosingRequest enclosingRequest = (HttpEntityEnclosingRequest) request;
            md5Hex = DigestUtils.md5Hex(enclosingRequest.getEntity().getContent());
        }
        String data = MessageFormat.format(SIGN_DATA_TEMPLATE, md5Hex, reqTime);
        try {
            sign = eccCrypto.signRSV(data, accessKey);
        } catch (Exception e) {
            throw new BusinessException(ReturnCode.ENCRYPT_ERROR);
        }
        request.addHeader(H_SIGN, sign);
        request.addHeader(H_TIME, StringUtil.valueOf(reqTime,""));
    }
}
