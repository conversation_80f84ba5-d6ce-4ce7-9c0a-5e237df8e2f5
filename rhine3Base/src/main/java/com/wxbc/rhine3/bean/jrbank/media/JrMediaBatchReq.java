package com.wxbc.rhine3.bean.jrbank.media;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: shangmeng<PERSON>o
 * @date: 2024/3/30 15:39
 * @description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class JrMediaBatchReq extends JrMediaBaseReq{


    private List<JrMediaFile> list;


    @ApiModelProperty(value = "文件树ID")
    private String treeId;

    public JrMediaBatchReq() {
        super();
    }
}
