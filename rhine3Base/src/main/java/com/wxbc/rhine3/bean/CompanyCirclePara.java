package com.wxbc.rhine3.bean;
/*
 *项目名: rhine3Asset
 *文件名: CompanyCirclePara
 *创建者: superYan
 *创建时间:2022/4/20 14:24
 */

import com.wxbc.rhine3.common.PagePara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class CompanyCirclePara extends PagePara {

    @ApiModelProperty(value = "金融机构Uuid", required = true)
    @NotBlank
    private String fiUuid;

    @ApiModelProperty(value = "金融机构名称")
    private String fiName;

    @ApiModelProperty(value = "企业Uuid", required = true)
    @NotBlank
    private String companyUuid;

    @ApiModelProperty(value = "企业名称")
    private String companyName;


    @ApiModelProperty(value = "是否需要进行销毁授权")
    private Boolean needDestroyAuth = false;

}
