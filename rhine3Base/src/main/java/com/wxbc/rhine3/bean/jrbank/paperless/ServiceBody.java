package com.wxbc.rhine3.bean.jrbank.paperless;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class ServiceBody {

    @JsonProperty(value = "request")
    private Request request;

//    @JsonProperty(value = "response")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private ResponseHead response;
}
