package com.wxbc.rhine3.bean.jrbank;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 活体验证请求
 *
 * <AUTHOR>
 * @since 2025/6/18
 */
@Data
public class YLT001Req {
    @ApiModelProperty(value = "流水号",required = true)
    @NotBlank
    @JsonProperty("SerialNo")
    private String serialNo;
}
