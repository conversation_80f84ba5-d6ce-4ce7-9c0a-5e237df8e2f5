package com.wxbc.rhine3.bean.jrbank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: shangmeng<PERSON>o
 * @date: 2024/6/3 10:36
 * @description:
 */
@Data
@ApiModel("万向扣款失败批次重发")
public class WXDF005Req {

    @ApiModelProperty(value = "批次号")
    private String pch;

    @ApiModelProperty(value = "核心企业清分摘要")
    String dscrtx;

}
