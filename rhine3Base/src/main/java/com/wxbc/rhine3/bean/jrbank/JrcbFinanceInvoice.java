package com.wxbc.rhine3.bean.jrbank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class JrcbFinanceInvoice {
    @ApiModelProperty(value = "发票代码",required = true)
    private String invoiceCode;
    @ApiModelProperty(value = "开票日期",required = true)
    private String invoiceDate;
    @ApiModelProperty(value = "发票号码",required = true)
    private String invoiceNumber;
    @ApiModelProperty(value = "销售方名称",required = true)
    private String sellerName;
    @ApiModelProperty(value = "购买方名称",required = true)
    private String buyerName;
    @ApiModelProperty(value = "含税金额(分)",required = true)
    private String pretaxAmount;
    @ApiModelProperty(value = "不含税金额(分)")
    private String noTaxAmount;
    @ApiModelProperty(value = "发票文件地址",required = true)
    private String invoiceFile;    //单个发票文件

    @ApiModelProperty(value = "发票验真标识 1-成功,0-失败")
    private Integer checkFlag;
}
