package com.wxbc.rhine3.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel
public class ProtocolPara {

    @NotBlank
    @ApiModelProperty(value = "待签文件的url", required = true)
    String fileUrl;

    @ApiModelProperty(value = "签章类型", hidden = true)
    String signType;

    @ApiModelProperty(value = "x坐标", hidden = true)
    String coordinateX;

    @ApiModelProperty(value = "y坐标", hidden = true)
    String coordinateY;

    @ApiModelProperty(value = "签章关键字", hidden = true)
    String keyword;

    @ApiModelProperty(value = "签章页码", hidden = true)
    String pageIndex;

    @ApiModelProperty(value = "是否需要对文件进行base64")
    private Boolean needBase64Flag = false;

    @ApiModelProperty(value = "签章公司的uuid", hidden = true)
    private String companyUuid;

    @ApiModelProperty(value = "签章公司名称", hidden = true)
    private String companyName;
}
