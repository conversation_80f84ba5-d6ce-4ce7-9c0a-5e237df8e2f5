package com.wxbc.rhine3.bean.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @company: 万向区块链
 * @description
 * @author: lixingxing
 * @create: 2021-11-01 11:06
 **/

@Data
@ApiModel
@NoArgsConstructor
public class SendMessageRequest {

    @NotBlank
    @ApiModelProperty(value = "手机号，多个逗号分隔",required = true)
    private String phoneNumber;

    @NotEmpty
    @Valid
    @ApiModelProperty(value = "短信参数",required = true)
    private List<String> smsParams;

    @NotNull
    @ApiModelProperty(value = "内部模板号",required = true)
    private String templateCode;

    @ApiModelProperty(value = "短信内容,如果使用内部模板,短信内容可以不填入")
    private String message;
}
