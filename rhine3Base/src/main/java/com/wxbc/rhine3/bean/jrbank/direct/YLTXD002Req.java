package com.wxbc.rhine3.bean.jrbank.direct;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("开立推动占额信息")
public class YLTXD002Req {

    @ApiModelProperty(value = "占用流水号")
    private String serno;

    @ApiModelProperty(value = "核心企业三证合一码")
    private String coreRegCode;

    @ApiModelProperty(value = "对手企业名称")
    private String finaName;

    @ApiModelProperty(value = "对手企业三证合一码")
    private String finRegCode;

    @ApiModelProperty(value = "占用金额")
    private String applyAmount;

    @ApiModelProperty(value = "占用起始日")
    private String loanStartDate;

    @ApiModelProperty(value = "占用到期日")
    private String loanEndDate;

    @ApiModelProperty(value = "核心企业已用金额")
    private String outStandAmt;

    @ApiModelProperty(value = "Gaps转发url")
    private String url;

    @ApiModelProperty(value = "类型")
    private String fwdInstId = "PUBHTTP";

    @ApiModelProperty(value = "交易码")
    private String transCode = "YLTXD002";
}
