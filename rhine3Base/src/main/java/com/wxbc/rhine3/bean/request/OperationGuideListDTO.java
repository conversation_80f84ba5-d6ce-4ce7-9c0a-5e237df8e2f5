package com.wxbc.rhine3.bean.request;

import com.wxbc.rhine3.common.PagePara;
import com.wxbc.rhine3.common.enums.OperationGuideTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询操作指导列表DTO
 *
 * <AUTHOR>
 * @since 2024/8/1
 */
@Data
@ApiModel("查询操作指导列表DTO")
@EqualsAndHashCode(callSuper = true)
public class OperationGuideListDTO extends PagePara {
    /**
     * {@link OperationGuideTypeEnum}
     */
    @ApiModelProperty("指引文件类型")
    private Integer type;
}
