package com.wxbc.rhine3.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * Description : auto generated entity
 * JDK         : 1.8
 * ProjectName : vena
 * Date        : 2023-05-16 10:23:00
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 */
@Data
@ApiModel
@TableName("`third_invoke_log`")
public class ThirdInvokeLogEntity {

    /**
     * 
     */
    @ApiModelProperty(value = "日志id")
    @TableId(value = "`id`", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 
     */
    @ApiModelProperty(value = "创建时间")
    @TableField("`create_time`")
    private LocalDateTime createTime;

    /**
     * 
     */
    @ApiModelProperty(value = "更新时间")
    @TableField("`update_time`")
    private LocalDateTime updateTime;

    /**
     * 
     */
    @ApiModelProperty(value = "状态")
    @TableField("`status`")
    private String status;

    /**
     * 调用目标名称(目标平台名称或API名称)
     */
    @ApiModelProperty(value = "skyworking的日志路径id")
    @TableField("`trace_id`")
    private String traceId;

    /**
     * 调用目标名称(目标平台名称或API名称)
     */
    @ApiModelProperty(value = "调用目标名称(目标平台名称或API名称)")
    @TableField("`target_name`")
    private String targetName;

    /**
     * 关联的业务编号
     */
    @ApiModelProperty(value = "关联的业务编号")
    @TableField("`relation_no`")
    private String relationNo;

    /**
     * 目标API的url
     */
    @ApiModelProperty(value = "目标API的url")
    @TableField("`api_url`")
    private String apiUrl;

    /**
     * 发送时间
     */
    @ApiModelProperty(value = "发送时间")
    @TableField("`send_time`")
    private LocalDateTime sendTime;

    /**
     * 接收时间
     */
    @ApiModelProperty(value = "接收时间")
    @TableField("`receive_time`")
    private LocalDateTime receiveTime;

    /**
     * 接口返回信息
     */
    @ApiModelProperty(value = "接口返回信息")
    @TableField("`return_result`")
    private String returnResult;

    /**
     * 接口返回码
     */
    @ApiModelProperty(value = "接口返回码")
    @TableField("`return_code`")
    private String returnCode;

    /**
     * 发送内容
     */
    @ApiModelProperty(value = "发送内容")
    @TableField("`send_info`")
    private String sendInfo;

}