package com.wxbc.rhine3.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * User: yanhengfu
 * Date: 2023/1/5
 * Time: 11:07
 * Description: des
 */
@Data
@ApiModel
public class OrderServiceFeeResponse {

    @ApiModelProperty(value = "订单服务费率id")
    private Long id;


    @ApiModelProperty(value = "关联的企业Uuid")
    private String companyUuid;

    @ApiModelProperty(value = "关联企业的名称")
    private String companyName;

    @ApiModelProperty(value = "金融产品名称")
    private String productName;


    @ApiModelProperty(value = "金融产品编号")
    private String serialNo;


    @ApiModelProperty(value = "服务费率")
    private BigDecimal serviceFeeRate;

    @ApiModelProperty(value = "业务模式：1-年化 0-单笔")
    private Integer type;

    @ApiModelProperty(value = "运营机构uuid")
    private String operatingOrganizationUuid;

}