package com.wxbc.rhine3.bean.jrbank.direct;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: shang<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/7/31 10:18
 * @description:
 */
@Data
@ApiModel("查询是否存在征信报告返回")
@Deprecated
public class YLTXD001Res {

    @ApiModelProperty(value = "是否有征信报告 1-存在，2- 不存在")
    private String flag;
}
