package com.wxbc.rhine3.bean;

import com.wxbc.rhine3.common.BaseInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <p>描述</p>
 *
 * <AUTHOR>
 * @since 2022/7/11
 **/
@Data
@ApiModel
@EqualsAndHashCode(callSuper = true)
public class BusinessProtocolCommonPara extends BaseInfo {
    @ApiModelProperty("协议html")
    private String protocolHtml;
    @ApiModelProperty("协议描述")
    private String protocolDesc;
    @ApiModelProperty("金融产品编号")
    private String financeProductNo;

    //1:html,2:word
    @NotNull
    @ApiModelProperty(value = "协议模板类型，1:html,2:word",required = true)
    private Integer fileType;

    @ApiModelProperty("协议内容")
    private byte[] fileContent;
}
