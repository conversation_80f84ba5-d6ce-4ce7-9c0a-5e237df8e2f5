package com.wxbc.rhine3.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: shang<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/4/27 16:17
 * @description:
 */
@Data
public class FaceRecognitionReq {

    @ApiModelProperty(value = "用户ID",required = true)
    @NotNull
    private Long userId;

    @ApiModelProperty(value = "姓名",required = true)
    @NotBlank
    private String idCName;

    @ApiModelProperty(value = "身份证号",required = true)
    @NotBlank
    private String idCCode;


    @ApiModelProperty(value = "身份证起始时间",required = false)
    private String idCBegin;

    @ApiModelProperty(value = "身份证截止时间",required = false)
    private String idCEnd;


    @ApiModelProperty(value = "图片的base64")
    @NotBlank
    private String photoBase64;

    @NotBlank
    @ApiModelProperty(value = "图片的类型，比如PNG,JPG等")
    private String photoType;

}
