package com.wxbc.rhine3.bean.jrbank.paperless;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wxbc.rhine3.bean.jrbank.WZH017Res;
import lombok.Data;

@Data
public class WZH017ServiceBody {

    @JsonProperty(value = "request")
    private Request request;

    @JsonProperty(value = "response")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private WZH017Res response;
}
