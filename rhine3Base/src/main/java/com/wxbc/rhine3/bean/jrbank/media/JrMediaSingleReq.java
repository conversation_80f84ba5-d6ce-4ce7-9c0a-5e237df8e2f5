package com.wxbc.rhine3.bean.jrbank.media;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: shangmengxiao
 * @date: 2024/3/30 15:39
 * @description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class JrMediaSingleReq extends JrMediaBaseReq{


    @ApiModelProperty(value = "文件树ID")
    private String treeId;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "文件的base64")
    private String file;

    public JrMediaSingleReq() {
        super();
    }

}
