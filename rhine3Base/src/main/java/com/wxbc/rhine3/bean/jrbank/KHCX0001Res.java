package com.wxbc.rhine3.bean.jrbank;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: s<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/3/12 10:44
 * @description:
 */
@Data
public class KHCX0001Res {

    @NotBlank(message = "信贷开户标识不能为空")
    @ApiModelProperty(value = "信贷开户标识 1-是，0-否",required = true)
    @JsonProperty("OPEN_FLAG")
    private String OPEN_FLAG;
}
