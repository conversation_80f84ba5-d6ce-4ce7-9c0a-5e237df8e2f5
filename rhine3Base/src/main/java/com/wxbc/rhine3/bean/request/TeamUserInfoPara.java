package com.wxbc.rhine3.bean.request;

import com.wxbc.rhine3.constants.TeamQuotaTransferTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * @company: 万向区块链
 * @description
 * @author: lixingxing
 * @create: 2023-11-20 13:47
 **/
@Data
@ApiModel
@NoArgsConstructor
public class TeamUserInfoPara {

    @NotNull
    @ApiModelProperty(value = "用户id",required = true)
    private Long userId;

    public TeamUserInfoPara(Long userId){
        this.userId=userId;
    }

}
