package com.wxbc.rhine3.bean.jrbank;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 2025/2/11
 */
public class PUb010Req {
    @ApiModelProperty(value = "短信内容",required = true)
    @JsonProperty("MSG")
    private String MSG;

    @ApiModelProperty(value = "手机号码",required = true)
    @JsonProperty("ADDRESS")
    private String ADDRESS;

    @ApiModelProperty(value = "交易协议",required = true)
    @JsonProperty("PROTOCOL")
    private String PROTOCOL = "C200";

    @ApiModelProperty(value = "交易模版",required = true)
    @JsonProperty("TEMPCODE")
    private String TEMPCODE = "8888";

    @ApiModelProperty(value = "短信平台流水号",required = true)
    @JsonProperty("TRANSID")
    private String TRANSID;

    @ApiModelProperty(value = "来源机构",required = true)
    @JsonProperty("SRCBRANCH")
    private String SRCBRANCH = "010058";

    @ApiModelProperty(value = "系统标识",required = true)
    @JsonProperty("SRCSYSTEM")
    private String SRCSYSTEM = "A20259";

    @ApiModelProperty(value = "时间戳（YYYYMMDDHHmmss）",required = true)
    @JsonProperty("TIMESTAMP")
    private String TIMESTAMP;

    @JsonIgnore
    public String getMSG() {
        return MSG;
    }

    public void setMSG(String MSG) {
        this.MSG = MSG;
    }

    @JsonIgnore
    public String getADDRESS() {
        return ADDRESS;
    }

    public void setADDRESS(String ADDRESS) {
        this.ADDRESS = ADDRESS;
    }

    @JsonIgnore
    public String getPROTOCOL() {
        return PROTOCOL;
    }

    public void setPROTOCOL(String PROTOCOL) {
        this.PROTOCOL = PROTOCOL;
    }

    @JsonIgnore
    public String getTEMPCODE() {
        return TEMPCODE;
    }

    public void setTEMPCODE(String TEMPCODE) {
        this.TEMPCODE = TEMPCODE;
    }

    @JsonIgnore
    public String getTRANSID() {
        return TRANSID;
    }

    public void setTRANSID(String TRANSID) {
        this.TRANSID = TRANSID;
    }

    @JsonIgnore
    public String getSRCBRANCH() {
        return SRCBRANCH;
    }

    public void setSRCBRANCH(String SRCBRANCH) {
        this.SRCBRANCH = SRCBRANCH;
    }

    @JsonIgnore
    public String getSRCSYSTEM() {
        return SRCSYSTEM;
    }

    public void setSRCSYSTEM(String SRCSYSTEM) {
        this.SRCSYSTEM = SRCSYSTEM;
    }

    @JsonIgnore
    public String getTIMESTAMP() {
        return TIMESTAMP;
    }

    public void setTIMESTAMP(String TIMESTAMP) {
        this.TIMESTAMP = TIMESTAMP;
    }
}
