package com.wxbc.rhine3.bean.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @company: 万向区块链
 * @description
 * @author: lj
 * @create: 2022-07-28 17:35
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("授信审核-请求参数")
public class CreditAuditPara  {

    @ApiModelProperty(value = "授信记录id")
    private int id;

    @ApiModelProperty(value = "0 拒绝  1 通过")
    private int flag;

    @ApiModelProperty(value = "审核意见")
    private String auditReason;

}
