package com.wxbc.rhine3.bean.request.oo;

import com.wxbc.rhine3.common.OoRegExceptionConstant;
import com.wxbc.rhine3.constants.CompanyStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import static com.wxbc.rhine3.common.OoRegExceptionConstant.AUDIT_REMARK_LEN_FAIL;
import static com.wxbc.rhine3.common.OoRegExceptionConstant.AUDIT_STATUS_BLANK;


@Data
@ApiModel
public class OoAudit {
    @NotNull(message = AUDIT_STATUS_BLANK)
    @ApiModelProperty(value = "公司审核状态", required = true)
    private CompanyStatusEnum status;

    @ApiModelProperty(value = "审核意见")
    @Size(max = 100, message = AUDIT_REMARK_LEN_FAIL)
    private String remark;

    @NotBlank(message = OoRegExceptionConstant.SERVICE_AUTHORITY_BLANK)
    @ApiModelProperty(value = "业务权限:bill-票据,factor-保理,order-订单(多选逗号分隔)",required = true)
    @Size(max = 500, message = OoRegExceptionConstant.SERVICE_AUTHORITY_LEN_FAIL)
    private String serviceAuthority;
}
