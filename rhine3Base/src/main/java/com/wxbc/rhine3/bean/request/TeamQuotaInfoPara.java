package com.wxbc.rhine3.bean.request;

import com.wxbc.rhine3.constants.TeamQuotaTransferTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * @company: 万向区块链
 * @description
 * @author: lixingxing
 * @create: 2023-11-20 13:47
 **/
@Data
@ApiModel
@NoArgsConstructor
public class TeamQuotaInfoPara {

    @NotNull
    @ApiModelProperty(value = "用户id",required = true)
    private Long userId;

    @NotNull
    @ApiModelProperty(value = "需要查询的额度类型 CREATE-开立  PAY-支付/融资",required = true)
    private TeamQuotaTransferTypeEnum teamQuotaTransferTypeEnum;

    public TeamQuotaInfoPara(Long userId){
        this.userId=userId;
    }

}
