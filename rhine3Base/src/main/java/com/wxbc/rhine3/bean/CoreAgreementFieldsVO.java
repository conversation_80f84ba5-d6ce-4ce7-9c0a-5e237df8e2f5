package com.wxbc.rhine3.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 获取核心企业合作协议签署字段VO
 *
 * <AUTHOR>
 * @since 2024/7/5
 */
@Data
public class CoreAgreementFieldsVO {
    @ApiModelProperty("签署主体")
    private String orgName;

    @ApiModelProperty("企业地址")
    private String address;

    @ApiModelProperty("企业法人")
    private String legalPerson;

    @ApiModelProperty("联系人")
    private String adminRealName;

    @ApiModelProperty("联系电话")
    private String adminMobile;

    @ApiModelProperty("合作额度，单位：元 RMB")
    private BigDecimal quotaInYuan;

    @ApiModelProperty("到期日")
    private String creditDueDate;

    @ApiModelProperty("户名")
    private String accountName;

    @ApiModelProperty("银行账号")
    private String accountNum;

    @ApiModelProperty("开户行")
    private String accountBank;

    @ApiModelProperty("授信机构法人")
    private String creditLegalPerson;

    @ApiModelProperty("总行的法人")
    private String headOfficeLegalPerson;

    @ApiModelProperty("授信机构联系电话")
    private String creditMobile;

    @ApiModelProperty("授信申请编号")
    private String creditApplicationNo;

    @ApiModelProperty("当前日期")
    private LocalDate currentDate;

    @ApiModelProperty("授信ID")
    private Long creditId;

    @ApiModelProperty(value = "授信金融机构")
    private String relationFi;
}
