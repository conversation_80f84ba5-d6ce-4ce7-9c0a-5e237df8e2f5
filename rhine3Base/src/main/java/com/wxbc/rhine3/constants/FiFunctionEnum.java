package com.wxbc.rhine3.constants;

import com.wxbc.rhine3.exception.BusinessException;
import lombok.Getter;
import lombok.Setter;

// 该字段用于在接口调用与签章参数调用时，区分调用哪个银行的接口服务
// 该字段必须与SP中金融服务商 / 业务配置 页面配置的“接口与签章调用标识”一致
public enum FiFunctionEnum {
    JXB(1,"江西银行的标识"),
    FJRL(2,"润楼的标识"),
    SRB(3,"上饶银行的标识"),
    JRBC(9,"江阴银行的标识");

    @Getter
    @Setter
    int val;

    @Getter
    @Setter
    String desc;


    FiFunctionEnum(int val, String desc) {
        this.val = val;
        this.desc = desc;
    }

    public static FiFunctionEnum getEnum(Integer key) {
        for (FiFunctionEnum value : FiFunctionEnum.values()) {
            if (key.equals(value.getVal())) {
                return value;
            }
        }
        throw new BusinessException("请联系运营，检查金融机构配置");
    }
}
