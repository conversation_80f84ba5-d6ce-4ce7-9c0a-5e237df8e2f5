package com.wxbc.rhine3.constants;

import lombok.Getter;

/**
 * 江阴短信发送渠道
 *
 * <AUTHOR>
 * @since 2025/2/11
 */
@Getter
public enum JrMsgChannelEnum {
    /**
     * 手机短信
     */
    SMS(0, "手机短信"),

    /**
     * 企业微信短信
     */
    WECOM(1, "企微短信");

    /**
     * 渠道编码
     */
    private final Integer code;

    /**
     * 渠道描述
     */
    private final String desc;

    JrMsgChannelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据编码获取枚举
     */
    public static JrMsgChannelEnum getByCode(Integer code) {
        for (JrMsgChannelEnum channel : values()) {
            if (channel.getCode().equals(code)) {
                return channel;
            }
        }
        return null;
    }
}
