package com.wxbc.rhine3.httpclient;

import com.wxbc.base.util.JsonUtil;
import com.wxbc.rhine3.constants.CommonConst;
import org.apache.http.HttpEntity;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;

@Service
public class HttpClient {
    protected static Logger logger = LoggerFactory.getLogger("HttpClient");

    @Autowired(required = false)
    private CloseableHttpClient client;

    private static HttpClient httpClient;

    @PostConstruct
    public void init() {
        httpClient = this;
        client = HttpClients.createDefault();
    }

    public static SSLContext createIgnoreVerifySSL() throws NoSuchAlgorithmException, KeyManagementException {
        SSLContext sc = SSLContext.getInstance("TLSv1.2");

        X509TrustManager trustManager = new X509TrustManager() {
            @Override
            public void checkClientTrusted(java.security.cert.X509Certificate[] paramArrayOfX509Certificate,
                                           String paramString) throws CertificateException {
            }

            @Override
            public void checkServerTrusted(java.security.cert.X509Certificate[] paramArrayOfX509Certificate,
                                           String paramString) throws CertificateException {
            }

            @Override
            public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                return null;
            }
        };

        sc.init(null, new TrustManager[]{trustManager}, null);
        return sc;
    }

    public static String sendPost(String url, Object object)
            throws IOException {
        String jsonBody = JsonUtil.object2String(object);
        if(jsonBody.getBytes().length<= CommonConst.LOG_PRINT_LMIT_10480_BYTE){
            logger.info("sendPost:{},para:{}",  url,jsonBody);
        }else{
            logger.info("sendPost:{}",  url);
        }

        HttpPost httpPost = new HttpPost(url);
        StringEntity entity = new StringEntity(jsonBody, "utf-8");
        httpPost.setEntity(entity);
        httpPost.setHeader("Content-Type", "application/json");
        //添加headre部分验证
        httpPost.setHeader("token", CommonConst.PROJECT);
        CloseableHttpResponse response = httpClient.client.execute(httpPost);
        return getBody(httpPost, response);
    }


    /**
     * 发送江阴银行接口
     * @param url
     * @param object
     * @return
     * @throws IOException
     */
    public static String sendJrcbPost(String url, Object object)
            throws IOException {
        String jsonBody = JsonUtil.object2String(object);
        if(jsonBody.getBytes().length<= CommonConst.LOG_PRINT_LMIT_10480_BYTE){
            logger.info("sendPost:{},para:{}",  url,jsonBody);
        }else{
            logger.info("sendPost:{}",  url);
        }

        HttpPost httpPost = new HttpPost(url);
        StringEntity entity = new StringEntity(jsonBody, ContentType.APPLICATION_JSON);
        entity.setContentEncoding(new BasicHeader("Content-Type", "application/json"));
        httpPost.setEntity(entity);
        CloseableHttpResponse response = httpClient.client.execute(httpPost);
        return getBody(httpPost, response);
    }

    /**
     * 模拟请求
     *
     * @param url 资源地址
     * @return
     * @throws NoSuchAlgorithmException
     * @throws KeyManagementException
     * @throws IOException
     * @throws ClientProtocolException
     */
    public static String sendPostByHttps(String url, Object object) throws KeyManagementException, NoSuchAlgorithmException, ClientProtocolException, IOException {
        String jsonBody = JsonUtil.object2String(object);
        if(jsonBody.getBytes().length<= CommonConst.LOG_PRINT_LMIT_10480_BYTE){
            logger.info("sendPost:{},para:{}",  url,jsonBody);
        }else{
            logger.info("sendPost:{}",  url);
        }
        //采用绕过验证的方式处理https请求
        SSLContext sslcontext = createIgnoreVerifySSL();

        // 设置协议http和https对应的处理socket链接工厂的对象
        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.INSTANCE)
                .register("https", new SSLConnectionSocketFactory(sslcontext))
                .build();
        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
        HttpClients.custom().setConnectionManager(connManager);

        //创建自定义的httpclient对象
        CloseableHttpClient client = HttpClients.custom().setConnectionManager(connManager).build();
        // CloseableHttpClient client = HttpClients.createDefault();

        //创建post方式请求对象
        HttpPost httpPost = new HttpPost(url);

        StringEntity entity = new StringEntity(jsonBody, "utf-8");//装填参数
        //设置参数到请求对象中
        httpPost.setEntity(entity);
        //设置header信息
        httpPost.setHeader("Content-Type", "application/json");

        //执行请求操作，并拿到结果（同步阻塞）
        CloseableHttpResponse response = client.execute(httpPost);
        return getBody(httpPost, response);
    }

    public static String sendPostStr(String url, String str)
            throws IOException {
        logger.info("url:{},sendPost:{}",url,str);

        HttpPost httpPost = new HttpPost(url);
        StringEntity entity = new StringEntity(str, "utf-8");
        httpPost.setEntity(entity);
        httpPost.setHeader("Content-Type", "application/json");
        CloseableHttpResponse response = httpClient.client.execute(httpPost);
        return getBody(httpPost, response);
    }

    public static String send(String url,HttpEntity entity) throws IOException {
        HttpPost post = new HttpPost(url);
        post.setEntity(entity);
        CloseableHttpResponse response = httpClient.client.execute(post);
        return getBody(post, response);
    }

    public static String sendGet(String url)
            throws IOException {
        HttpGet httpGet = new HttpGet(url);
        httpGet.setHeader("Content-Type", "application/json");
        CloseableHttpResponse response = httpClient.client.execute(httpGet);
        return getBody(httpGet, response);
    }

    private static String getBody(HttpRequestBase httpRequestBase, CloseableHttpResponse response) throws IOException {
        String body = "";
        try {
            if (response != null) {
                HttpEntity entity2 = response.getEntity();
                if (entity2 != null) {
                    body = EntityUtils.toString(entity2, "UTF-8");
                }
                EntityUtils.consume(entity2);
            }
        } finally {
            if (httpRequestBase!=null) {
                httpRequestBase.releaseConnection();
            }
            if (null != response) {
                response.close();
            }
        }
        if(body.getBytes().length<= CommonConst.LOG_PRINT_LMIT_10480_BYTE){
            logger.info("httpclient return body:{}", body);
        }
        return body;
    }


}
