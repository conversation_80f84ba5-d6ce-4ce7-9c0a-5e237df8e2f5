package com.wxbc.rhine3.util;

import java.util.concurrent.ThreadLocalRandom;

/**
 * @author: shang<PERSON>g<PERSON><PERSON>
 * @date: 2022/4/21 15:26
 * @description:
 */
public class CodeGenerationUtil {


    public static final String DEFAULT_STR = "0123456789";

    /**
     * 生成指定位数随机码
     *
     * @param size
     * @param sources
     * @return
     */
    public static String generateRandomCode(int size, String sources) {
        if (sources == null || sources.length() == 0) {
            sources = DEFAULT_STR;
        }
        int codesLen = sources.length();
        ThreadLocalRandom rand = getThreadRandom();
        StringBuilder verifyCode = new StringBuilder(size);
        for (int i = 0; i < size; i++) {
            verifyCode.append(sources.charAt(rand.nextInt(codesLen - 1)));
        }
        return verifyCode.toString();
    }

    public static ThreadLocalRandom getThreadRandom(){
        return ThreadLocalRandom.current();
    }

}


