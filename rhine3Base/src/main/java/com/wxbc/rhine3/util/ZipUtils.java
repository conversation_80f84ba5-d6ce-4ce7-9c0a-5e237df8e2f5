package com.wxbc.rhine3.util;

import com.wxbc.rhine3.constants.CommonConst;
import com.wxbc.rhine3.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;


@Slf4j
public class ZipUtils {
    private static final int BUFFER_SIZE = 2 * 1024;

    /**
     * 压缩成ZIP 方法1
     *
     * @param srcDir           压缩文件夹路径
     * @param out              压缩文件输出流
     * @param KeepDirStructure 是否保留原来的目录结构,true:保留目录结构;
     *                         false:所有文件跑到压缩包根目录下(注意：不保留目录结构可能会出现同名文件,会压缩失败)
     * @throws BusinessException 压缩失败会抛出运行时异常
     */
    public static void toZip(String srcDir, OutputStream out, Boolean KeepDirStructure) {

        try (ZipOutputStream zos = new ZipOutputStream(out)) {
            File sourceFile = new File(srcDir);
            compress(sourceFile, zos, sourceFile.getName(), KeepDirStructure);
        } catch (Exception e) {
            throw new BusinessException(CommonConst.ZIP_UTIL_EXCEPTION, e);
        }

    }

    public static void childrenFolderToZip(String srcDir, OutputStream out, Boolean KeepDirStructure) {

        try (ZipOutputStream zos = new ZipOutputStream(out)) {

            File sourceFile = new File(srcDir);
            File[] listFiles = sourceFile.listFiles();
            assert listFiles != null;
            for (File file : listFiles) {
                compress(file, zos, file.getName(), KeepDirStructure);
            }
        } catch (Exception e) {
            throw new BusinessException(CommonConst.ZIP_UTIL_EXCEPTION, e);
        }

    }

    /**
     * 压缩成ZIP 方法2 (内部文件名被数字代替(中登网专用))
     *
     * @param fileBytes 需要压缩的文件字节数据列表
     * @param out       压缩文件输出流
     * @throws BusinessException 压缩失败会抛出运行时异常
     */
        public static List<String> toZipByZdw(List<byte[]> fileBytes, OutputStream out) throws BusinessException {
        ZipOutputStream zos = null;
        List<String> names = new ArrayList<>();
        try {
            zos = new ZipOutputStream(out);
            int i = 0;
            for (byte[] item : fileBytes) {
                if (item == null) {
                    continue;
                }
                zos.putNextEntry(new ZipEntry(i + CommonConst.FILE_PDF_SUFFIX));
                zos.write(item);
                zos.closeEntry();
                names.add(i + CommonConst.FILE_PDF_SUFFIX);
                i++;
            }
        } catch (Exception e) {
            log.error("zip error", e);
            throw new BusinessException(CommonConst.ZIP_UTIL_EXCEPTION, e);
        } finally {
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException e) {
                    log.error("zip error 2", e);
                }
            }
        }
        return names;
    }


    /**
     * 压缩成ZIP 方法3 (内部文件名保持原始文件名)
     *
     * @param fileUrlAndByteMap 需要压缩的文件字节数据Map(key=文件url,value=文件对应的byte数组)
     * @param out               压缩文件输出流
     * @throws BusinessException 压缩失败会抛出运行时异常
     */
    public static List<String> toZip(Map<String, byte[]> fileUrlAndByteMap, OutputStream out) throws IOException {
        ZipOutputStream zos = null;
        List<String> names = new ArrayList<>();
        try {
            zos = new ZipOutputStream(out);
            for (Map.Entry<String, byte[]> entry : fileUrlAndByteMap.entrySet()) {
                if (entry.getValue() == null) {
                    continue;
                }
                String fileSuffix = UploadUtil.getFileSuffix(entry.getKey());
                String fileName = UploadUtil.getFileName(entry.getKey());
                zos.putNextEntry(new ZipEntry(fileName + "." + fileSuffix));
                zos.write(entry.getValue());
                zos.closeEntry();
                names.add(fileName + "." + fileSuffix);
            }
        } catch (Exception e) {
            log.error("zip error", e);
            throw new BusinessException(CommonConst.ZIP_UTIL_EXCEPTION, e);
        } finally {
            FileUtil.close(zos);
        }
        return names;
    }

    /**
     * 压缩成ZIP 方法3 (内部文件名保持原始文件名)
     *
     * @param fileNameAndByteMap 需要压缩的文件字节数据Map(key=文件名,value=文件对应的byte数组)
     * @param out                压缩文件输出流
     * @throws BusinessException 压缩失败会抛出运行时异常
     */
    public static List<String> toZip4FileName(Map<String, byte[]> fileNameAndByteMap, OutputStream out) throws IOException {
        ZipOutputStream zos = null;
        List<String> names = new ArrayList<>();
        try {
            zos = new ZipOutputStream(out);
            for (Map.Entry<String, byte[]> entry : fileNameAndByteMap.entrySet()) {
                if (entry.getValue() == null) {
                    continue;
                }
                zos.putNextEntry(new ZipEntry(entry.getKey() + "." + CommonConst.FILE_PDF_SUFFIX));
                zos.write(entry.getValue());
                zos.closeEntry();
                names.add(entry.getKey() + "." + CommonConst.FILE_PDF_SUFFIX);
            }
        } catch (Exception e) {
            log.error("zip error", e);
            throw new BusinessException(CommonConst.ZIP_UTIL_EXCEPTION, e);
        } finally {
            FileUtil.close(zos);
        }
        return names;
    }

    /**
     * 扫描添加文件Entry
     *
     * @param base   基路径
     * @param source 源文件
     * @param zos    Zip文件输出流
     * @throws IOException
     */
    private static void addEntry(String base, File source, ZipOutputStream zos)
            throws IOException {
        // 按目录分级，形如：/aaa/bbb.txt
        String entry = base + source.getName();
        if (source.isDirectory()) {
            for (File file : source.listFiles()) {
                // 递归列出目录下的所有文件，添加文件Entry
                addEntry(entry + "/", file, zos);
            }
        } else {
            try (FileInputStream fis = new FileInputStream(source);
                 BufferedInputStream bis = new BufferedInputStream(fis)) {
                byte[] buffer = new byte[1024 * 10];
                int read = 0;
                zos.putNextEntry(new ZipEntry(entry));
                while ((read = bis.read(buffer, 0, buffer.length)) != -1) {
                    zos.write(buffer, 0, read);
                }
                zos.closeEntry();
            }

        }
    }


    /**
     * 递归压缩方法
     *
     * @param sourceFile       源文件
     * @param zos              zip输出流
     * @param name             压缩后的名称
     * @param KeepDirStructure 是否保留原来的目录结构,true:保留目录结构;
     *                         false:所有文件跑到压缩包根目录下(注意：不保留目录结构可能会出现同名文件,会压缩失败)
     */
    private static void compress(File sourceFile, ZipOutputStream zos, String name,
                                 Boolean KeepDirStructure) throws Exception {
        byte[] buf = new byte[BUFFER_SIZE];
        if (sourceFile.isFile()) {
            try (FileInputStream in = new FileInputStream(sourceFile)) {
                // 向zip输出流中添加一个zip实体，构造器中name为zip实体的文件的名字
                zos.putNextEntry(new ZipEntry(name));
                // copy文件到zip输出流中
                int len;
                while ((len = in.read(buf)) != -1) {
                    zos.write(buf, 0, len);
                }
            } catch (Exception e) {
                log.error("zip exception", e);
            } finally {
                // Complete the entry
                zos.closeEntry();
            }
        } else {
            File[] listFiles = sourceFile.listFiles();
            if (listFiles == null || listFiles.length == 0) {
                // 需要保留原来的文件结构时,需要对空文件夹进行处理
                if (KeepDirStructure) {
                    // 空文件夹的处理
                    zos.putNextEntry(new ZipEntry(name + "/"));
                    // 没有文件，不需要文件的copy
                    zos.closeEntry();
                }


            } else {
                for (File file : listFiles) {
                    // 判断是否需要保留原来的文件结构
                    if (KeepDirStructure) {
                        // 注意：file.getName()前面需要带上父文件夹的名字加一斜杠,
                        // 不然最后压缩包中就不能保留原来的文件结构,即：所有文件都跑到压缩包根目录下了
                        compress(file, zos, name + "/" + file.getName(), true);
                    } else {
                        compress(file, zos, file.getName(), false);
                    }

                }
            }
        }
    }

    /**
     * @param data 原始压缩字节数组
     * @param name 文件名
     * @return
     */
    public static byte[] unzip(byte[] data, String name) {
        try (ByteArrayInputStream bis = new ByteArrayInputStream(data); ZipInputStream zip = new ZipInputStream(bis)) {
            ZipEntry zipEntry;
            while (null != (zipEntry = zip.getNextEntry())) {
                if (zipEntry.getName().equals(name)) {
                    try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                        IOUtils.copy(zip, baos);
                        return baos.toByteArray();
                    }
                }
            }
        } catch (Exception ex) {
            log.error("ZipUtils.unzip error:" + ex.getMessage(), ex);
        }
        return new byte[0];
    }

    public static void main(String[] args) throws Exception {

    }

    public static void check(File file) throws IOException {
        String fileName = file.getName();
        file.mkdirs();
        if (file.exists()) {
            if (file.delete()) {
                log.info("file delete success");
            }
            if (!fileName.contains("."))
                file.mkdirs();
        }
        File parentFile = file.getParentFile();
        if (!parentFile.exists()) {
            parentFile.mkdirs();
        }
        if (file.createNewFile()) {
            log.info("file createNewFile success");
        }
    }

    private void generateCouFolder(String couUuid) throws Exception {
        File zipPath = new File(System.getProperty("user.dir") + "/" + couUuid);
        addTransferContractFolder(couUuid, zipPath);
        addCouPathFolder(couUuid);
        payCommitFile(couUuid);
        couTransferPath(couUuid);
    }

    private void addTransferContractFolder(String couUuid, File file) throws Exception {


    }

    private void payCommitFile(String fileUrl) {

    }

    private void couTransferPath(String fileUrl) {

    }


    private void addCouPathFolder(String couUuid) {

    }


}
