package com.wxbc.rhine3.api;

import com.wxbc.rhine3.bean.User;
import com.wxbc.rhine3.bean.request.oo.OoLoginPara;
import com.wxbc.rhine3.common.Response;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * service-vena
 *
 * <AUTHOR> chen cheng
 * Date         : 2023/6/29 16:32
 * Jdk          : 1.8
 * Description  :
 */
public interface OoUserLoginApi {

    Response<User> login(@RequestBody OoLoginPara loginPara);
}
