USE rhine3_asset;

drop table if exists `protocol_platform_fee_sign`;
create table if not exists `protocol_platform_fee_sign`
(
    `id`          bigint   not null AUTO_INCREMENT COMMENT 'id',
    `create_time` datetime not null default CURRENT_TIMESTAMP() comment '创建时间',
    `update_time` datetime not null default CURRENT_TIMESTAMP() on update CURRENT_TIMESTAMP() comment '更新时间',
    `status`      tinyint  not null default 1 comment '保留字段',


    `protocol_name` varchar(80) NOT NULL COMMENT '协议模板名称',
    `relation_no` varchar(64) NOT NULL COMMENT '关联业务编号',
    `project_code` varchar(50)  NOT NULL COMMENT '协议所属平台',
    `url` varchar(256) NOT NULL COMMENT '协议文件url',
    `operating_organization_uuid` varchar(64) NOT NULL COMMENT '运营机构uuid',

    primary key (`id`) USING BTREE,
    UNIQUE INDEX `ux_relation_no` (`relation_no`) USING BTREE
    )engine innodb, charset utf8mb4, comment '平台服务费协议签署表';

drop table if exists `protocol_platform_fee_sign_ext`;
create table if not exists `protocol_platform_fee_sign_ext`
(
    `id`          bigint   not null AUTO_INCREMENT COMMENT 'id',
    `create_time` datetime not null default CURRENT_TIMESTAMP() comment '创建时间',
    `update_time` datetime not null default CURRENT_TIMESTAMP() on update CURRENT_TIMESTAMP() comment '更新时间',
    `status`      tinyint  not null default 1 comment '保留字段',

    `relation_id` bigint   not null COMMENT 'protocol_platform_fee_sign的id',
    `sign_company_name` varchar(64) NOT NULL COMMENT '签署企业名称',
    `sign_time` datetime  NOT NULL default CURRENT_TIMESTAMP() COMMENT '签署时间',

    primary key (`id`) USING BTREE
    )engine innodb, charset utf8mb4, comment '平台服务费协议签署明细表';
