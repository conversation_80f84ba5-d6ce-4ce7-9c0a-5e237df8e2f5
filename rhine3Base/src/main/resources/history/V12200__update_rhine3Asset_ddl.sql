USE rhine3_asset;

--
-- VIEW structure for table `act_id_group`
--

DROP VIEW IF EXISTS `act_id_group`;
CREATE VIEW `act_id_group` AS
SELECT DISTINCT
    CONCAT(`r`.`role_name`, `u`.`company_uuid`) AS `ID_`,
    NULL AS `REV_`,
    `r`.`role_name` AS `NAME_`,
    'assignment' AS `TYPE_`
FROM
    ((`rhine3_user`.`sys_role` `r`
        JOIN `rhine3_user`.`sys_user_role` `ur`)
        JOIN `rhine3_user`.`sys_user` `u`)
WHERE
    ((`ur`.`role_id` = `r`.`id`)
        AND (`ur`.`user_id` = `u`.`id`)
        AND (`u`.`status` = 'active'));

--
-- VIEW structure for table `act_id_user`
--

DROP VIEW IF EXISTS `act_id_user`;
CREATE VIEW `act_id_user` AS
SELECT
    `u`.`email` AS `ID_`,
    0 AS `REV_`,
    `u`.`username` AS `FIRST_`,
    '' AS `LAST_`,
    `u`.`email` AS `EMAIL_`,
    `u`.`password` AS `PWD_`,
    '' AS `PICTURE_ID_`
FROM
    `rhine3_user`.`sys_user` `u`;

--
-- VIEW structure for table `act_id_membership`
--

DROP VIEW IF EXISTS `act_id_membership`;
CREATE VIEW `act_id_membership` AS
SELECT
    (SELECT
         `u`.`email`
     FROM
         `rhine3_user`.`sys_user` `u`
     WHERE
         ((`u`.`id` = `ur`.`user_id`)
             AND (`u`.`status` = 'active'))) AS `USER_ID_`,
    (SELECT
         CONCAT(`r`.`role_name`, `u`.`company_uuid`)
     FROM
         (`rhine3_user`.`sys_role` `r`
             JOIN `rhine3_user`.`sys_user` `u`)
     WHERE
         ((`r`.`id` = `ur`.`role_id`)
             AND (`ur`.`user_id` = `u`.`id`)
             AND (`u`.`status` = 'active'))) AS `GROUP_ID_`
FROM
    `rhine3_user`.`sys_user_role` `ur`;

ALTER TABLE `finance_application`
 ADD COLUMN `send_bank_status` VARCHAR(10) NOT NULL DEFAULT 'WAIT' COMMENT '发送给银行接口的状态：WAIT,SUCCESS' ;
