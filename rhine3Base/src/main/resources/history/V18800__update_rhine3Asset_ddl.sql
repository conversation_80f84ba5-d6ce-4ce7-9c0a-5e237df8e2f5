USE rhine3_asset;

alter table zd_task_match add column  origin_register_no varchar(64) DEFAULT NULL COMMENT '原始登记证明编号(只有异议登记记录才会有值)' after register_no;

-- zd_asset_import表修改
ALTER TABLE `zd_asset_import`
    ADD COLUMN `source_type` varchar(10) NOT NULL COMMENT '数据来源类型' AFTER `status`,
    ADD COLUMN `biz_uuid`    varchar(64) NULL COMMENT '业务关联UUID' AFTER `source_type`;

-- zd_check_task表修改
ALTER TABLE `zd_check_task`
    CHANGE COLUMN `finance_uuid` `biz_uuid` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '融资申请UUID' AFTER `asset_import_id`,
    ADD COLUMN `error_desc`  varchar(2000) NULL COMMENT '错误描述' AFTER `query_support_file`,
    ADD COLUMN `source_type` varchar(10)   NOT NULL COMMENT '数据来源类型' AFTER `status`;
ALTER TABLE `finance_zdw`
    ADD COLUMN `register_status` tinyint(4) DEFAULT 1 COMMENT '登记状态' AFTER `request_data`;

DROP TABLE IF EXISTS `zd_terminate_reg`;
CREATE TABLE `zd_terminate_reg`
(
    `id`                          bigint(20)  NOT NULL AUTO_INCREMENT,
    `create_time`                 datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                 datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                      tinyint(4)  NOT NULL DEFAULT '1' COMMENT '0-登记失败，1-登记成功',
    `finance_uuid`                varchar(64) NOT NULL COMMENT '融资uuid',
    `company_uuid`                varchar(64) NOT NULL COMMENT '融资企业uuid',
    `fi_uuid`                     varchar(64) NOT NULL COMMENT '金融机构公司uuid',
    `operating_organization_uuid` varchar(64) NOT NULL COMMENT '运营机构uuid',
  `zdw_init_no` varchar(255) DEFAULT NULL COMMENT '中登网初始登记编号',
	`zdw_terminate_no` varchar(255) DEFAULT NULL COMMENT '中登网注销登记编号',
  `request_no` varchar(255) DEFAULT NULL COMMENT '业务系统自己生成的登记请求号',
  `reason` varchar(50) DEFAULT NULL COMMENT '注销原因(01-主债权消灭,02-担保权实现,03-权利人放弃登记载明的担保权,04-其他导致所登记权利消灭的情形)',
	`reason_desc` text DEFAULT NULL COMMENT '注销原因描述',
    `source` varchar(64) NOT NULL COMMENT '注销来源',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='中登网注销登记表';

ALTER TABLE `invoice`
DROP INDEX `index_c_n_u` ,
ADD UNIQUE INDEX `index_c_n_u` (`invoice_code` ASC, `invoice_number` ASC, `unique_version` ASC);


ALTER TABLE `rhine3_asset`.`zd_task_match`
    ADD COLUMN `property_desc` varchar(8000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '财产描述or异议陈述' AFTER `operating_organization_uuid`;
