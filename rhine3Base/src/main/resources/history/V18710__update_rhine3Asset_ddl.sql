USE rhine3_asset;

ALTER TABLE `invoice`
    ADD COLUMN `check_state` VARCHAR(5) NULL COMMENT '发票验真状态：0 正常，1 失控，2 作废作废，3 红冲、7 部分红冲、8 全额红冲' AFTER `check_flag` ;
ALTER TABLE `invoice`
    ADD COLUMN `check_msg` VARCHAR(255) NULL COMMENT '发票验真失败原因' AFTER `check_state` ;


ALTER TABLE `invoice_alarm`
    ADD COLUMN `check_flag` tinyint(4) DEFAULT '0' COMMENT '发票验真标识 1验真成功 0未验真' AFTER `invoice_status` ;

ALTER TABLE `invoice_alarm`
    ADD COLUMN  `finance_value_date` date DEFAULT NULL COMMENT '融资起息日' AFTER `finance_create_date` ;


ALTER TABLE `invoice_alarm`
    CHANGE COLUMN `invoice_status` `check_state` VARCHAR(5) NULL COMMENT '发票验真状态：0 正常，1 失控，2 作废作废，3 红冲、7 部分红冲、8 全额红冲' ;

ALTER TABLE `invoice_alarm`
    ADD COLUMN `check_msg` VARCHAR(255) NULL COMMENT '发票验真失败原因' AFTER `check_state` ;

drop table if exists `invoice_alarm_task`;
CREATE TABLE `invoice_alarm_task` (
                        `id` bigint(20) NOT NULL AUTO_INCREMENT,
                        `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态 1-正常  0-已删除',

                        `task_no` varchar(50) NOT NULL  COMMENT '任务编号',
                        `task_status` tinyint(4) NOT NULL  COMMENT '任务状态',
                        `start_time` datetime NOT NULL COMMENT '任务启动时间',
                        `end_time` datetime DEFAULT NULL COMMENT '任务结束时间',

                        `operating_organization_uuid` varchar(64) NOT NULL COMMENT '运营机构uuid',
                        PRIMARY KEY (`id`) USING BTREE,
                        UNIQUE KEY `idx_task_no` (`task_no`),
                        KEY `idx_status` (`status`),
                        KEY `idx_task_status` (`task_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发票验真任务表';







