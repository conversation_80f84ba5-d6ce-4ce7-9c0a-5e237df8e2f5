USE rhine3_user;


CREATE TABLE `third_invoke_log` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` varchar(20) DEFAULT NULL,
  `target_name` varchar(128) NOT NULL COMMENT '调用目标名称(目标平台名称或API名称)',
  `relation_no` varchar(1024) DEFAULT NULL COMMENT '关联的业务编号',
  `api_url` varchar(256) DEFAULT NULL COMMENT '目标API的url',
	`send_time` datetime DEFAULT NULL COMMENT '调用接口时间',
	`trace_id` varchar(256) DEFAULT NULL COMMENT 'skyworking的日志追踪路径id',
  `receive_time` datetime DEFAULT NULL COMMENT '接收时间',
  `return_result` text DEFAULT NULL COMMENT '接口返回信息',
	`return_code` varchar(20) DEFAULT NULL COMMENT '接口返回码',
  `send_info` mediumtext COMMENT '发送内容',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT '内部三方服务调用日志表';

ALTER TABLE `rhine3_user`.`credit`
ADD COLUMN `enable` tinyint(4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '1-启用，0-禁用' AFTER `operating_organization_uuid`;