package com.wxbc.pledge.config;

import com.wxbc.signature.vo.SealInfo;

import java.util.ArrayList;
import java.util.List;

public class StringTest {
    public static void main(String[] args) {

        //sealImageUrl 为cfca minio的地址，需要转换为代理地址，才可以使用
        String sealImageUrl = "https://minioit.i.wxblockchain.com/cfca/cfca/20240311/382719ba-a179-4ee2-b8c5-ee074523d01d?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minioit%2F20240321%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20240321T135219Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=9a7c8af108ebf9788fda17a619033396ae036de46411e9d4790a3e6393136181";

        String cfcaImagePreurl = "http://**************:8333";
        String cfcaStr = "/cfca";
//        sealImageUrl = cfcaImagePreurl.concat(sealImageUrl.substring(sealImageUrl.indexOf(cfcaStr)));
//        System.out.println(sealImageUrl);

        List<SealInfo> sealInfos = urlTransfer(sealImageUrl, cfcaImagePreurl);
        System.out.println(sealInfos.get(0).getSealImageUrl());
    }

    public static List<SealInfo> urlTransfer(String sealImageUrl, String cfcaImagePreurl ){

        SealInfo sealInfo = new SealInfo();
        sealInfo.setSealImageUrl(sealImageUrl);
        List<SealInfo> sealInfos = new ArrayList<SealInfo>();
        sealInfos.add(sealInfo);

        String cfcaStr = "/cfca";
        sealInfos.stream().forEach(seal -> {
            String ImageUrl = seal.getSealImageUrl();
            ImageUrl = cfcaImagePreurl.concat(ImageUrl.substring(ImageUrl.indexOf(cfcaStr)));
            seal.setSealImageUrl(ImageUrl);
        });
        return sealInfos;
    }
}
