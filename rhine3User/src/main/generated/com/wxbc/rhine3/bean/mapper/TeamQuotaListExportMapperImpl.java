package com.wxbc.rhine3.bean.mapper;

import com.wxbc.rhine3.bean.response.team.ExportTeamQuotaListResponses;
import com.wxbc.rhine3.repository.entity.TeamQuotaEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-05-15T15:31:33+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 1.8.0_391 (Oracle Corporation)"
)
public class TeamQuotaListExportMapperImpl implements TeamQuotaListExportMapper {

    @Override
    public ExportTeamQuotaListResponses source2Target(TeamQuotaEntity teamQuotaEntity) {
        if ( teamQuotaEntity == null ) {
            return null;
        }

        ExportTeamQuotaListResponses exportTeamQuotaListResponses = new ExportTeamQuotaListResponses();

        exportTeamQuotaListResponses.setQuotaNo( teamQuotaEntity.getQuotaNo() );

        exportTeamQuotaListResponses.setQuotaInYuan( com.wxbc.rhine3.util.NumberFormatUtil.thousandsFormatAnd2f(teamQuotaEntity.getQuotaInYuan()) );
        exportTeamQuotaListResponses.setCreateTime( teamQuotaEntity.getCreateTime().format(java.time.format.DateTimeFormatter.ofPattern(com.wxbc.rhine3.util.TimeUtils.DATA_FORMAT_YYYY_MM_DD_HH_MM_SS)) );
        exportTeamQuotaListResponses.setUpdateTime( teamQuotaEntity.getUpdateTime().format(java.time.format.DateTimeFormatter.ofPattern(com.wxbc.rhine3.util.TimeUtils.DATA_FORMAT_YYYY_MM_DD_HH_MM_SS)) );

        return exportTeamQuotaListResponses;
    }

    @Override
    public List<ExportTeamQuotaListResponses> source2TargetList(List<TeamQuotaEntity> teamQuotaEntityList) {
        if ( teamQuotaEntityList == null ) {
            return null;
        }

        List<ExportTeamQuotaListResponses> list = new ArrayList<ExportTeamQuotaListResponses>( teamQuotaEntityList.size() );
        for ( TeamQuotaEntity teamQuotaEntity : teamQuotaEntityList ) {
            list.add( source2Target( teamQuotaEntity ) );
        }

        return list;
    }
}
