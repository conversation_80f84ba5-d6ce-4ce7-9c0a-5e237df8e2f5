package com.wxbc.rhine3.service.invoice;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import com.wxbc.base.util.BeanUtil;
import com.wxbc.base.util.JsonUtil;
import com.wxbc.rhine3.bean.Company;
import com.wxbc.rhine3.bean.EmailAttachment;
import com.wxbc.rhine3.bean.EmailInfoWithAttachment;
import com.wxbc.rhine3.bean.User;
import com.wxbc.rhine3.bean.invoice.*;
import com.wxbc.rhine3.bean.response.oo.OoInfoResp;
import com.wxbc.rhine3.constant.EmailContentConst;
import com.wxbc.rhine3.constants.InvoiceTypeEnum;
import com.wxbc.rhine3.constants.StatusConst;
import com.wxbc.rhine3.exception.BusinessException;
import com.wxbc.rhine3.repository.entity.CompanyInvoiceEntity;
import com.wxbc.rhine3.repository.entity.CompanyInvoicingInfoEntity;
import com.wxbc.rhine3.repository.mapper.CompanyInvoiceDao;
import com.wxbc.rhine3.repository.mapper.CompanyInvoicingInfoDao;
import com.wxbc.rhine3.repository.table.CompanyInvoice;
import com.wxbc.rhine3.repository.table.CompanyInvoicingInfo;
import com.wxbc.rhine3.service.EmailService;
import com.wxbc.rhine3.service.OoQueryService;
import com.wxbc.rhine3.service.company.CompanyQueryService;
import com.wxbc.venus.user.dto.AccountUserDTO;
import com.wxbc.venus.user.util.UserUtil;
import com.wxbc.scaffold.common.definition.util.Assert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class CompanyInvoiceService {


    @Resource
    private CompanyInvoiceDao companyInvoiceDao;
    @Resource
    private CompanyInvoicingInfoDao companyInvoicingInfoDao;
    @Resource
    private OoQueryService ooQueryService;
    @Resource
    private CompanyQueryService companyQueryService;
    @Resource
    private EmailService emailService;

    public PageInfo<CompanyInvoiceResp> queryCompanyInvoiceList(CompanyInvoiceQueryRequest request) {
        List<CompanyInvoiceResp> list = companyInvoiceDao.queryCompanyInvoiceList(request);
        log.error("查询企业发票列表");
        if (CollectionUtils.isEmpty(list)) {
            return new PageInfo<>(Collections.emptyList());
        }
        return new PageInfo<>(list);
    }


    @Transactional(rollbackFor = Exception.class)
    public InvoiceErrorMsg batchAddCompanyInvoice(BatchAddCompanyInvoiceRequest request) {
        InvoiceErrorMsg invoiceError = new InvoiceErrorMsg();
        List<CompanyInvoiceEntity> entityList = buildCompanyInvoiceList(request.getList(), invoiceError);
        if (!CollectionUtils.isEmpty(entityList)) {
            entityList.forEach(x -> {
                try {
                    companyInvoiceDao.insert(x);
                } catch (Exception e) {
                    invoiceError.getErrorList().add(x.getInvoiceCode() + "_" + x.getInvoiceNumber());
                    log.error("运营机构上传企业发票失败：发票代码：{}，发票号码：{}", x.getInvoiceCode(), x.getInvoiceNumber());
                }
            });
        }
        return invoiceError;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteCompanyInvoice(CompanyInvoiceDeleteRequest request) {
        int selectCount = companyInvoiceDao.selectCount(new QueryWrapper<CompanyInvoiceEntity>()
                .eq(CompanyInvoice.STATUS, StatusConst.VALID)
                .eq(CompanyInvoice.ID, request.getId()));
        Assert.isTrue(selectCount == 1, "未找到该发票数据");

        int delCount = companyInvoiceDao.update(null, new UpdateWrapper<CompanyInvoiceEntity>()
                .set(CompanyInvoice.STATUS, StatusConst.INVALID)
                .eq(CompanyInvoice.STATUS, StatusConst.VALID)
                .eq(CompanyInvoice.ID, request.getId()));
        Assert.isTrue(delCount == 1, "删除失败");
    }

    public void notifyCompanyInvoice(CompanyInvoiceNotifyRequest request) {
        List<CompanyInvoiceEntity> dataList = filterCompanyInvoiceList(request);
        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException("未找到可推送数据");
        }
        boolean isSingle = !CollectionUtils.isEmpty(request.getIdList()) && request.getIdList().size() == 1;
        //推送邮件
        dataList.forEach(x -> pushEmailNotify(x, isSingle));
    }

    private void pushEmailNotify(CompanyInvoiceEntity entity, boolean isSingle) {
        Set<String> emailSet = getPushEmailAddress(entity, isSingle);
        if (CollectionUtils.isEmpty(emailSet))
            return;
        //组装发邮件参数
        EmailInfoWithAttachment info = new EmailInfoWithAttachment();
        info.setEmailAddress(Joiner.on(",").join(emailSet));
        info.setSubject(String.format(EmailContentConst.COMPANY_INVOICE_PUSH_SUBJECT, entity.getLinkBiz()));
        info.setMessageText(String.format(EmailContentConst.COMPANY_INVOICE_PUSH_TEXT, entity.getLinkBiz()));

        JSONObject json = JsonUtil.jsonStr2Object(entity.getFileUrl(), JSONObject.class);
        Map.Entry<String, Object> entry = json.entrySet().stream().findFirst().orElse(null);
        if (entry != null) {
            EmailAttachment attachment = new EmailAttachment();
            attachment.setAttachmentName(entry.getKey());
            attachment.setAttachment((String) entry.getValue());
            info.setAttachment(attachment);
        }
        emailService.sendEmailWithAttachment(info);
    }

    private Set<String> getPushEmailAddress(CompanyInvoiceEntity entity, boolean isSingle) {
        Set<String> emailSet = new HashSet<>();

        if (!StringUtils.isEmpty(entity.getBuyer())) {
            //查询发票买方企业
            Company buyer = companyQueryService.queryCompanyByUuid(entity.getBuyer());
            if (null != buyer) {
                emailSet.add(buyer.getEmail());
                //买方开票信息
                CompanyInvoicingInfoEntity companyInvoicingInfo = companyInvoicingInfoDao.selectOne(new QueryWrapper<CompanyInvoicingInfoEntity>()
                        .eq(CompanyInvoicingInfo.COMPANY_UUID, buyer.getUuid())
                        .last(" limit 1"));
                if (null != companyInvoicingInfo) {
                    emailSet.add(companyInvoicingInfo.getRecipientEmail());
                }
            }
        } else if (isSingle) {
            throw new BusinessException("买方不存在");
        }
        return emailSet;
    }

    private List<CompanyInvoiceEntity> filterCompanyInvoiceList(CompanyInvoiceNotifyRequest request) {
        boolean idNotEmpty = !CollectionUtils.isEmpty(request.getIdList());
        boolean invoiceDateRange = !StringUtils.isEmpty(request.getInvoiceDateFrom())
                && !StringUtils.isEmpty(request.getInvoiceDateTo())
                && DateUtil.betweenDay(DateUtil.parseDate(request.getInvoiceDateFrom()), DateUtil.parseDate(request.getInvoiceDateTo()), true) <= 90;
        //两个筛选条件都不满足提示错误
        if (!idNotEmpty && !invoiceDateRange) {
            throw new BusinessException("请确保推送日期在90个自然日内或者推送数据ID存在");
        }
        QueryWrapper<CompanyInvoiceEntity> qw = new QueryWrapper<>();
        qw.in(idNotEmpty, CompanyInvoice.ID, request.getIdList());
        if (invoiceDateRange) {
            qw.between(CompanyInvoice.INVOICE_DATE, DateUtil.parseDate(request.getInvoiceDateFrom()), DateUtil.parseDate(request.getInvoiceDateTo()));
        }
        qw.eq(CompanyInvoice.STATUS, StatusConst.VALID);
        return companyInvoiceDao.selectList(qw);
    }

    private List<CompanyInvoiceEntity> buildCompanyInvoiceList(List<CompanyInvoiceDto> companyInvoiceDtoList, InvoiceErrorMsg invoiceError) {
        List<CompanyInvoiceEntity> list = new ArrayList<>();
        for (CompanyInvoiceDto dto : companyInvoiceDtoList) {
            int selectCount = companyInvoiceDao.selectCount(new QueryWrapper<CompanyInvoiceEntity>()
                    .eq(CompanyInvoice.INVOICE_CODE, dto.getInvoiceCode())
                    .eq(CompanyInvoice.INVOICE_NUMBER, dto.getInvoiceNumber())
                    .eq(CompanyInvoice.STATUS, StatusConst.VALID));
            if (selectCount > 0) {
                invoiceError.getRepeatList().add(dto.getInvoiceCode() + "_" + dto.getInvoiceNumber());
            } else {
                CompanyInvoiceEntity entity = checkAndBuildInvoiceEntity(dto);
                list.add(entity);
            }
        }
        return list;
    }

    private CompanyInvoiceEntity checkAndBuildInvoiceEntity(CompanyInvoiceDto companyInvoiceDto) {

        //1.非增值税普通发票，不含税金额必填
        if (!companyInvoiceDto.getInvoiceType().equals(InvoiceTypeEnum.VATCOMMON.getVal()) && companyInvoiceDto.getAmount() == null) {
            throw new BusinessException(String.format("发票号码：%s，发票代码：%s，非增值税普通发票，不含税金额必填", companyInvoiceDto.getInvoiceNumber(), companyInvoiceDto.getInvoiceCode()));
        }
        //2.校验不含税金额是否小于等于含税金额
        if (null != companyInvoiceDto.getAmount() && companyInvoiceDto.getAmount().compareTo(companyInvoiceDto.getPretaxAmount()) > 0) {
            throw new BusinessException(String.format("发票号码：%s，发票代码：%s，不含税金额需小于含税金额", companyInvoiceDto.getInvoiceNumber(), companyInvoiceDto.getInvoiceCode()));
        }
        //3.电子发票没有发票代码
        if (!Arrays.asList(InvoiceTypeEnum.ELEI_SVATI.getVal(), InvoiceTypeEnum.ELEI_OI.getVal()).contains(companyInvoiceDto.getInvoiceType())) {
            if (StringUtils.isEmpty(companyInvoiceDto.getInvoiceCode())) {
                throw new BusinessException(String.format("发票号码：%s，发票代码：%s，非电子发票发票代码不能为空", companyInvoiceDto.getInvoiceNumber(), companyInvoiceDto.getInvoiceCode()));
            }
            if (!companyInvoiceDto.getInvoiceCode().matches("^[A-Za-z0-9]{0,12}$")) {
                throw new BusinessException(String.format("发票号码：%s，发票代码：%s，发票代码最长12个字符，支持数字/字母", companyInvoiceDto.getInvoiceNumber(), companyInvoiceDto.getInvoiceCode()));
            }
        }
        AccountUserDTO user = UserUtil.get();
        CompanyInvoiceEntity entity = new CompanyInvoiceEntity();

        //买卖方企业查询，关联企业uuid
        Company buyer = companyQueryService.searchCompanyByFullName(companyInvoiceDto.getBuyerName());
        if (null != buyer && !StringUtils.isEmpty(buyer.getUuid())) {
            entity.setBuyer(buyer.getUuid());
        }
        OoInfoResp supplier = ooQueryService.accurateSearchByCompanyName(companyInvoiceDto.getSupplierName());
        if (null != supplier && !StringUtils.isEmpty(supplier.getUuid())) {
            entity.setSupplier(supplier.getUuid());
        }

        BeanUtil.copyProperties(companyInvoiceDto, entity);
        entity.setInvoiceDate(LocalDateTimeUtil.parseDate(companyInvoiceDto.getInvoiceDate()));
        entity.setOperator(user.getCompanyUuid());
        entity.setOperateUserUuid(user.getUuid());
        return entity;
    }
}
