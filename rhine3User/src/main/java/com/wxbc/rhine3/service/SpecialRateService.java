package com.wxbc.rhine3.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.wxbc.base.util.StringUtil;
import com.wxbc.rhine3.bean.*;
import com.wxbc.rhine3.bean.response.SpecialRateResponse;
import com.wxbc.rhine3.common.lob.CompanyType;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.repository.entity.SpecialRateEntity;
import com.wxbc.rhine3.repository.mapper.SpecialRateDao;
import com.wxbc.rhine3.repository.table.SpecialRateTableConst;
import com.wxbc.rhine3.service.company.CompanyService;
import com.wxbc.venus.user.dto.AccountUserDTO;
import com.wxbc.venus.user.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;

import static com.wxbc.rhine3.constant.ExceptionConstant.*;

@Service
@Slf4j
public class SpecialRateService extends ServiceImpl<SpecialRateDao, SpecialRateEntity> {

    @Resource
    private SpecialRateDao specialRateDao;

    @Resource
    private CompanyService companyService;

    /**
     * 新增 特殊利率
     * @param para 特殊利率
     * @throws ParameterException 异常处理
     */
    @Transactional(rollbackFor = Exception.class)
    public void specialRateSave(SpecialRatePara para) throws ParameterException {
        if (!validCompany(para)) {
            throw new ParameterException(SPECIAL_RATE_IS_NOT_S);
        }

        QueryWrapper<SpecialRateEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(SpecialRateTableConst.COMPANY_UUID,para.getCompanyUuid()).eq(SpecialRateTableConst.FI_UUID,para.getFiUuid());
        SpecialRateEntity rateEntity = specialRateDao.selectOne(queryWrapper);
        Assert.isNull(rateEntity,SPECIAL_RATE_EXISTS_S);

        SpecialRateEntity specialRateEntity = new SpecialRateEntity();
        BeanUtils.copyProperties(para, specialRateEntity);

        specialRateDao.insert(specialRateEntity);
    }


    @Transactional(rollbackFor = Exception.class)
    public void specialRateModify(SpecialRateModifyPara para) throws ParameterException {
        Company company = companyService.searchCompanyByFullName(para.getCompanyName());
        if(company==null){
            throw new ParameterException(SPECIAL_RATE_IS_NOT_S);
        }
        AccountUserDTO user = UserUtil.get();

        QueryWrapper<SpecialRateEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(SpecialRateTableConst.COMPANY_UUID,company.getUuid()).eq(SpecialRateTableConst.FI_UUID,user.getCompanyUuid());
        SpecialRateEntity rateEntity = specialRateDao.selectOne(queryWrapper);

        Assert.isTrue(para.getId().equals(rateEntity.getId()),PARA_FAIL);

        SpecialRateEntity specialRateEntity = new SpecialRateEntity();
        BeanUtils.copyProperties(para, specialRateEntity);

        specialRateDao.updateById(specialRateEntity);
    }


    /**
     * 获取特殊利率列表
     * @param para 查询参数
     * @return 列表
     */
    public PageInfo<SpecialRateResponse> getSpecialRateList(SpecialRateQueryPara para) {
        para.setFiUuid(UserUtil.get().getCompanyUuid());
        return new PageInfo<>(specialRateDao.getSpecialRateList(para));
    }

    /**
     * 删除特利率率
     * @param id 利率id
     */
    public void deleteSpecialRateById(int id) {
        SpecialRateEntity specialRateEntity = specialRateDao.selectById(id);
        Assert.isTrue(UserUtil.get().getCompanyUuid().equals(specialRateEntity.getFiUuid()),PARA_FAIL);
        specialRateDao.deleteById(id);
    }


    /**
     * 校验供应商信息
     * @param para 特殊利率入参
     * @return 是否通过校验
     */
    public Boolean validCompany(SpecialRatePara para){
        AccountUserDTO  user = UserUtil.get();
        Company company = companyService.searchCompanyByFullName(para.getCompanyName());
        if(company==null){
            return false;
        }
        String fiOperatingOrganizationUuid = StringUtil.valueOf(user.getCompany().getOperatingOrgId());
        para.setCompanyUuid(company.getUuid());
        para.setFiUuid(user.getCompanyUuid());
      //  输入的供应商名称存在且身份为供应商或双身份企业
        //  该供应商与本金融机构归属于同一个运营机构
        return (company.getType().contains(CompanyType.S.name())
                && fiOperatingOrganizationUuid.equals(company.getOperatingOrganizationUuid()));
    }

    /**
     * 根据fiUuid companyUuid 查询指定特殊利率
     * @param para 查询特殊利率入参 uuid  fiUuid
     * @return 特殊利率
     */
    public SpecialRateResponse getSpecialRateDetail(SpecialRateQueryPara para){
        SpecialRateResponse response = new SpecialRateResponse();
        QueryWrapper<SpecialRateEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(SpecialRateTableConst.COMPANY_UUID,para.getCompanyUuid()).eq(SpecialRateTableConst.FI_UUID,para.getFiUuid());
        SpecialRateEntity specialRateEntity = specialRateDao.selectOne(queryWrapper);
        if(specialRateEntity != null){
            BeanUtils.copyProperties(specialRateEntity,response);
            return response;
        }
        return null;
    }


}
