package com.wxbc.rhine3.service.user;

import com.wxbc.rhine3.bean.ActivateAccountPara;
import com.wxbc.rhine3.bean.LoginCodeInfo;
import com.wxbc.rhine3.bean.User;
import com.wxbc.rhine3.common.lob.CompanyType;
import com.wxbc.rhine3.constant.Constant;
import com.wxbc.rhine3.constant.UserStatus;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.repository.mapper.UserDao;
import com.wxbc.rhine3.service.MessageService;
import com.wxbc.rhine3.service.rbac.VenaRoleService;
import com.wxbc.rhine3.service.rbac.VenaUserRoleService;
import com.wxbc.security.modules.sys.service.SysUserRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.UUID;

import static com.wxbc.rhine3.constant.Constant.DEFAULT_COMPANY_UUID_1;
import static com.wxbc.rhine3.constant.Constant.DEFAULT_OO_UUID_1;

@Service
@Slf4j
public class AdminRegisterService {

    @Autowired
    private UserDao userDao;
    @Autowired
    private PasswordEncoder passwordEncoder;

    @Resource
    private VenaRoleService venaRoleService;

    @Autowired
    private MessageService messageService;
    @Autowired
    private SysUserRoleService sysUserRoleService;

    @Autowired
    private VenaUserRoleService venaUserRoleService;


    @Transactional(rollbackFor = Exception.class)
    public void createCompanyAdmin(ActivateAccountPara para) throws ParameterException, IOException {
        messageService.verifyPhoneCode(para.getMobile(), para.getMobileCode(), LoginCodeInfo.CODE_TYPE_REGISTER);

        // 添加用户
        User user = insertUser(para);

        // 配置游客（双重游客）
        insertAdminRole(user);
    }

    private void insertAdminRole(User user) {
        //注册设置成游客角色
        venaUserRoleService.updateUserRole(user.getId().longValue(), venaRoleService.queryGuestRolesOfAllBiz());
    }

    private User insertUser(ActivateAccountPara para) {
        User user = new User();
        user.setUuid(UUID.randomUUID().toString());
        if(CompanyType.OFI.getProjectCode().equals(para.getProjectCode())){
            user.setUsername(Constant.COMPANY_ORDER_FI_ADMIN_CHINESE);
        }else {
            user.setUsername(Constant.COMPANY_ADMIN_CHINESE);
        }

        user.setEmail(para.getEmail());
        user.setMobile(para.getMobile());
        user.setStatus(UserStatus.ACTIVE.getVal());
        user.setProjectCode(para.getProjectCode());

        user.setCompanyUuid(DEFAULT_COMPANY_UUID_1);
        user.setOperatingOrganizationUuid(DEFAULT_OO_UUID_1);

        user.setPassword(passwordEncoder.encode(para.getPassword()));
        userDao.insertUser(user);
        return user;
    }
}
