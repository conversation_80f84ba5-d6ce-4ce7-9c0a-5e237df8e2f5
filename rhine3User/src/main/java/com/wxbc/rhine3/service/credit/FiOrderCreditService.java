package com.wxbc.rhine3.service.credit;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageInfo;
import com.wxbc.rhine3.bean.Company;
import com.wxbc.rhine3.exception.ExistException;
import com.wxbc.rhine3.model.dto.CompanyUuidsDto;
import com.wxbc.rhine3.model.vo.req.credit.CreateOrderCreditVo;
import com.wxbc.rhine3.model.vo.req.credit.ModifyOrderCreditVo;
import com.wxbc.rhine3.model.vo.req.credit.QueryOrderCreditVo;
import com.wxbc.rhine3.model.vo.res.credit.OrderCreditVo;
import com.wxbc.rhine3.repository.entity.OrderCreditEntity;
import com.wxbc.rhine3.repository.mapper.OrderCreditDao;
import com.wxbc.rhine3.repository.table.OrderCredit;
import com.wxbc.rhine3.service.WaveIdService;
import com.wxbc.rhine3.service.company.CompanyService;
import com.wxbc.rhine3.util.TimeUtils;
import com.wxbc.venus.user.dto.AccountUserDTO;
import com.wxbc.venus.user.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.UUID;

import static com.wxbc.rhine3.constant.Constant.CREDIT_PRE;
import static com.wxbc.rhine3.constant.ExceptionConstant.CREDIT_NOTFOUND_COMPANY;
import static com.wxbc.rhine3.constant.ExceptionConstant.DATA_NOT_FOUND;


/**
 * User: yanhengfu
 * Date: 2022/11/4
 * Time: 10:40
 * Description: 金融机构授信相关 服务层
 */
@Service
@Slf4j
public class FiOrderCreditService extends OrderCreditService {

    @Autowired
    private OrderCreditDao orderCreditDao;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private WaveIdService waveIdService;

    @Transactional(rollbackFor = Exception.class)
    public void createCredit(CreateOrderCreditVo param) {
        AccountUserDTO loginUser= UserUtil.get();
        Company company = companyService.searchCompanyByFullName(param.getCompanyName());
        Assert.notNull(company,CREDIT_NOTFOUND_COMPANY);
        //Assert.isTrue(loginUser.getCompany().getOperatingOrganizationUuid().equals(company.getOperatingOrganizationUuid()),CREDIT_NOTFOUND_COMPANY);

        checkDuplicateCredit(company.getUuid(),param.getProductNo(),loginUser.getCompanyUuid(),null);

        OrderCreditEntity orderCreditEntity = new OrderCreditEntity();
        orderCreditEntity.setCompanyUuid(company.getUuid());
        orderCreditEntity.setCreditDueDate(TimeUtils.getFormatDate(param.getCreditDueDate(),TimeUtils.DATA_FORMAT_YYYY_MM_DD));
        orderCreditEntity.setOperatingOrganizationUuid(company.getOperatingOrganizationUuid());
        BeanUtils.copyProperties(param,orderCreditEntity);
        orderCreditEntity.setFiUuid(loginUser.getCompanyUuid());
        orderCreditEntity.setUuid(UUID.randomUUID().toString());
        orderCreditEntity.setCreditNo(waveIdService.initNumberByWaveId(CREDIT_PRE));
        orderCreditDao.insert(orderCreditEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    public void modifyCredit(ModifyOrderCreditVo param) {
        AccountUserDTO loginUser= UserUtil.get();
        OrderCreditEntity credit = orderCreditDao.selectOne(
                new QueryWrapper<OrderCreditEntity>().eq(OrderCredit.UUID, param.getUuid())
        );
        Assert.isTrue(param.getTotalInYuan().compareTo(credit.getUsedInYuan()) > 0,"");
        Assert.isTrue( loginUser.getCompanyUuid().equals(credit.getFiUuid()) ,"");

        Assert.notNull(credit,DATA_NOT_FOUND);

        checkDuplicateCredit(credit.getCompanyUuid(),param.getProductNo(),loginUser.getCompanyUuid(),credit.getUuid());

        OrderCreditEntity creditEntity = new OrderCreditEntity();
        BeanUtils.copyProperties(param,creditEntity);
        creditEntity.setCreditDueDate(TimeUtils.getFormatDate(param.getCreditDueDate(),TimeUtils.DATA_FORMAT_YYYY_MM_DD));
        creditEntity.setId(credit.getId());
        orderCreditDao.updateById(creditEntity);
    }

    private void checkDuplicateCredit(String companyUuid,String productNo,String fiUuid,String ignoreUuid) {
        OrderCreditEntity orderCredit = orderCreditDao.selectOne(
                new QueryWrapper<OrderCreditEntity>().eq(OrderCredit.COMPANY_UUID, companyUuid)
                        .eq(OrderCredit.PRODUCT_NO,productNo)
                        .eq(OrderCredit.FI_UUID,fiUuid)
        .ne(!StringUtils.isEmpty(ignoreUuid),OrderCredit.UUID,ignoreUuid));

        if(orderCredit != null){
            throw new ExistException("授信已存在，请勿重复添加");
        }
    }

    public PageInfo<OrderCreditVo> queryCreditList(QueryOrderCreditVo para){
        String companyUuid = UserUtil.get().getCompanyUuid();
        return queryList(
                para,
                new CompanyUuidsDto(null,companyUuid,null)
                );
    }


}