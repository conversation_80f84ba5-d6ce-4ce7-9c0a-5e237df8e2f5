package com.wxbc.rhine3.service.card;

import com.wxbc.rhine3.bean.BankCardPara;
import com.wxbc.rhine3.bean.BankCardQueryDTO;
import com.wxbc.rhine3.bean.BankCardQueryPara;
import com.wxbc.rhine3.bean.CompanyBankCardResponse;
import com.wxbc.rhine3.bean.SupplementCompanyPara;
import com.wxbc.rhine3.bean.jrbank.WXZHCXReq;
import com.wxbc.rhine3.bean.jrbank.WXZHCXRes;
import com.wxbc.rhine3.common.BusinessType;
import com.wxbc.rhine3.common.CardType;
import com.wxbc.rhine3.config.bank.JiangYinConfig;
import com.wxbc.rhine3.constant.ExceptionConstant;
import com.wxbc.rhine3.constants.CreditSignStatusEnum;
import com.wxbc.rhine3.constants.CreditTypeEnum;
import com.wxbc.rhine3.constants.ErrorConstants;
import com.wxbc.rhine3.constants.ReturnCode;
import com.wxbc.rhine3.constants.StatusConst;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.repository.mapper.BankCardDao;
import com.wxbc.rhine3.repository.mapper.CreditModifyDao;
import com.wxbc.rhine3.service.company.CompanyService;
import com.wxbc.rhine3.service.jrbank.JrBankInvokeService;
import com.wxbc.scaffold.common.definition.response.BaseResponse;
import com.wxbc.scaffold.common.definition.response.ResponseFormat;
import com.wxbc.scaffold.common.definition.util.Assert;
import com.wxbc.venus.user.dto.AccountUserDTO;
import com.wxbc.venus.user.util.UserUtil;
import com.wxbc.verification.vo.BankCodeQueryPara;
import com.wxbc.verification.vo.BankCodeQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @company: 万向区块链
 * @description
 * @author: lixingxing
 * @create: 2021-06-25 09:47
 **/
@Slf4j
@Service
public class BankCardService {

    @Resource
    private BankCardDao bankCardDao;

    @Resource
    private CompanyService companyService;

    @Resource
    private BankBranchService bankBranchService;

    @Resource
    private JiangYinConfig jiangYinConfig;

    @Resource
    private JrBankInvokeService jrBankInvokeService;

    @Autowired
    private CreditModifyDao creditModifyDao;

    public void supplementBankCard(SupplementCompanyPara para) throws ParameterException, IOException {
        AccountUserDTO user = UserUtil.get();
        String companyUuid = user.getCompanyUuid();


        List<BankCardPara> generalBankCard = bankCardDao.selectBankCardByCompanyUuidAndType(companyUuid, CardType.GENERAL.name());
        BankCardPara bankCardPara = CollectionUtils.isEmpty(generalBankCard) ? new BankCardPara() : generalBankCard.get(0);

        // 若开户行带有【江阴农村商业银行】，则调用银行账户信息校验接口，进行开户信息校验
        String accountBankCode = checkJrcbBankCard(para.getAccountName(), para.getAccountNum(), para.getAccountBank());
        bankCardPara.setAccountName(para.getAccountName());
        bankCardPara.setAccountNum(para.getAccountNum());
        bankCardPara.setAccountBank(para.getAccountBank());
        bankCardPara.setAccountBankCode(accountBankCode);

        if (CollectionUtils.isEmpty(generalBankCard)) {
            bankCardPara.setCardType(CardType.GENERAL);
            bankCardPara.setStatus(StatusConst.INIT);
            bankCardPara.setBusinessType(BusinessType.PAY.name() + ","
                    + BusinessType.CASH_RECEIVE.name() + "," + BusinessType.FINANCING_RECEIVE.name());
            bankCardPara.setCompanyUuid(companyUuid);

            bankCardDao.insertBankCard(bankCardPara);
            return;
        }

        if (generalBankCard.size() > 1) {
            throw new ParameterException(ErrorConstants.UPDATE_FAIL);
        }
        bankCardDao.updateBankCard(bankCardPara);
    }

    public void modifyBankCard(BankCardPara para) throws ParameterException, IOException {
        BankCardPara dbSameCard = getBankInfoByBusinessType(para);

        // 若开户行带有【江阴农村商业银行】，则调用银行账户信息校验接口，进行开户信息校验
        String accountBankCode = checkJrcbBankCard(para.getAccountName(), para.getAccountNum(), para.getAccountBank());

        para.setAccountBankCode(accountBankCode);

        if (Objects.isNull(para.getId())) {
            Assert.isNull(dbSameCard, ErrorConstants.BANKCARD_DUPLICATE);
            para.setStatus(StatusConst.INIT);
            bankCardDao.insertBankCard(para);
            return;
        }

        if (CardType.SPECIAL.equals(para.getCardType())
                && Objects.nonNull(dbSameCard) && !para.getId().equals(dbSameCard.getId())) {
            //防止恶意串改他人银行卡数据
            throw new ParameterException(ErrorConstants.BANKCARD_DUPLICATE);
        }
        BankCardPara preBankCard = bankCardDao.selectBankCardById(para.getId());
        bankCardDao.updateBankCard(para);

        // Pledge-815 修改银行卡，增加重置开立额度的协议签署状态
        boolean ifAccountNumChanged = !StringUtils.equalsIgnoreCase(preBankCard.getAccountNum(), para.getAccountNum());
        if (ifAccountNumChanged) {
            creditModifyDao.updateCreditSignStatusByCompanyUuidAndType(para.getCompanyUuid(), CreditTypeEnum.CREATE.getValue(), CreditSignStatusEnum.NOT_SIGNED.getValue());
        }
    }

    public void deleteBankCard(int id) {
        AccountUserDTO user = UserUtil.get();
        bankCardDao.deleteBankCardById(id, user.getCompanyUuid());
    }

    public List<BankCardPara> selectBankCardListByCompanyUuid4Internal(BankCardQueryPara bankCardQueryPara) {
        List<BankCardPara> bankCardParas = bankCardDao.selectBankCardByCompanyUuid(bankCardQueryPara);
        for (BankCardPara bankCardPara : bankCardParas) {
            if (StringUtils.isNotBlank(bankCardPara.getRelationFi())) {
                bankCardPara.setRelationFiName(companyService.selectCompanyName(bankCardPara.getRelationFi()));
            }
        }
        return bankCardParas;
    }

    public List<BankCardPara> selectBankCardListByCompanyUuid(BankCardQueryPara bankCardQueryPara) throws ParameterException {
        AccountUserDTO userDTO = UserUtil.get();
        //修复水平越权问题
        if (!userDTO.getCompanyUuid().equals(bankCardQueryPara.getCompanyUuid())) {
            throw new ParameterException(ExceptionConstant.DO_NOT_EXCEED_YOUR_AUTHORITY);
        }
        List<BankCardPara> bankCardParas = bankCardDao.selectBankCardByCompanyUuid(bankCardQueryPara);
        for (BankCardPara bankCardPara : bankCardParas) {
            if (StringUtils.isNotBlank(bankCardPara.getRelationFi())) {
                bankCardPara.setRelationFiName(companyService.selectCompanyName(bankCardPara.getRelationFi()));
            }
        }
        return bankCardParas;
    }

    /**
     * 如果是专业卡的时关联的金融机构id必传
     */
    public BankCardPara queryAvailableBankCardByCompanyAndBusinessType(BankCardQueryPara bankCardQueryPara) throws ParameterException {
        List<BankCardPara> bankCardListRes = selectBankCardListByCompanyUuid(bankCardQueryPara);

        BankCardPara generalCard = null;
        BankCardPara specialCard = null;

        for (BankCardPara bankCardPara : bankCardListRes) {
            if (CardType.SPECIAL.name().equals(bankCardPara.getCardType().name())
                    && bankCardPara.getRelationFi().equals(bankCardQueryPara.getRelationFi())
                    && bankCardPara.getBusinessType().contains(bankCardQueryPara.getBusinessType().name())) {
                specialCard = new BankCardPara();
                BeanUtils.copyProperties(bankCardPara, specialCard);
                break;
            }
            if (CardType.GENERAL.name().equals(bankCardPara.getCardType().name())) {
                generalCard = new BankCardPara();
                BeanUtils.copyProperties(bankCardPara, generalCard);
            }
        }

        if (specialCard != null) {
            return specialCard;
        }
        return generalCard;
    }


    public List<BankCardPara> queryBankCardListByOrgAndType(BankCardQueryDTO bankCardQueryDTO) {
        return bankCardDao.selectBankCardByCompanyUuidAndType(bankCardQueryDTO.getOrgId(), bankCardQueryDTO.getCardType());
    }

    /**
     * 查询江阴银行可用的银行卡(通用卡)
     *
     * @param bankCardQueryDTO BankCardQueryDTO
     * @return BankCardPara
     */
    public BankCardPara queryAvailableBankCardByCompanyAndBankName(BankCardQueryDTO bankCardQueryDTO) {
        String bankName = jiangYinConfig.getBankName();
        List<String> bankNameList = Arrays.stream(bankName.split(",")).collect(Collectors.toList());
        return bankCardDao.selectBankCardByCompanyUuidAndBankName(bankCardQueryDTO.getOrgId(), CardType.GENERAL.name(), bankNameList);
    }

    /**
     * 若开户行带有【江阴农村商业银行】，则调用银行账户信息校验接口，进行开户信息校验
     *
     * @param accountName 账号名
     * @param accountNum  账户号
     * @param accountBank 开户行
     */
    private String checkJrcbBankCard(String accountName, String accountNum, String accountBank) throws IOException {
        boolean jrcb = Arrays.stream(jiangYinConfig.getBankName().split(",")).anyMatch(name -> accountBank.contains(name));
        if (!jrcb) {
            // 通过ruban获取银行卡号
            return getBankNoByBankName(accountBank);
        }
        if (Boolean.TRUE.equals(jiangYinConfig.getEnable())) {
            // 江阴接口通过账号获取开户行信息
            WXZHCXReq req = new WXZHCXReq();
            req.setAcctno(accountNum);
            ResponseFormat<WXZHCXRes> responseFormat = jrBankInvokeService.jrAccountQuery(req);
            Assert.isTrue(responseFormat.isSuccess(), ReturnCode.JRCB_ACCOUNT_QUERY_FAILED);
            WXZHCXRes wxzhcxRes = responseFormat.getData();
            Assert.notNull(wxzhcxRes, ReturnCode.JRCB_ACCOUNT_QUERY_FAILED);
            Assert.isTrue(accountName.equals(wxzhcxRes.getAcctName()), ReturnCode.JRCB_ACCOUNT_NAME_ERROR);
            Assert.isTrue(accountBank.equals(wxzhcxRes.getBrchna()), ReturnCode.JRCB_ACCOUNT_BANK_ERROR);
            return wxzhcxRes.getBrcode();
        }
        return null;
    }

    private BankCardPara getBankInfoByBusinessType(BankCardPara bankCardPara) {
        BankCardQueryPara bankCardQueryPara = new BankCardQueryPara();
        bankCardQueryPara.setRelationFi(bankCardPara.getRelationFi());
        bankCardQueryPara.setCompanyUuid(bankCardPara.getCompanyUuid());
        List<BankCardPara> dbBankCards = bankCardDao.selectBankCardByCompanyUuidAndFi(bankCardQueryPara);
        if (CollectionUtils.isEmpty(dbBankCards)) {
            return null;
        }
        List<BankCardPara> dbSpecialCards;
        if (CardType.GENERAL.equals(bankCardPara.getCardType())) {
            dbSpecialCards = dbBankCards.stream()
                    .filter(x -> CardType.GENERAL.equals(x.getCardType()) && !x.getId().equals(bankCardPara.getId()))
                    .collect(Collectors.toList());
        } else {
            dbSpecialCards = dbBankCards.stream()
                    .filter(x -> CardType.SPECIAL.equals(x.getCardType())
                            && (Arrays.stream(x.getBusinessType().split(",")).anyMatch(bankCardPara.getBusinessType()::contains))
                            && !x.getId().equals(bankCardPara.getId()))
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(dbSpecialCards)) {
            return null;
        }

        return dbSpecialCards.get(0);
    }

    private String getBankNoByBankName(String bankName) throws IOException {
        BankCodeQueryPara queryPara = new BankCodeQueryPara();
        queryPara.setBankName(bankName);
        BankCodeQueryResponse bankCodeQueryResponse = bankBranchService.queryBankCodeByName(queryPara);
        if (Objects.isNull(bankCodeQueryResponse)) {
            return null;
        }
        return bankCodeQueryResponse.getBankNo();
    }

    public BaseResponse<WXZHCXRes> jrAccountQuery(WXZHCXReq req) {
        if (jiangYinConfig.getEnable()) {
            ResponseFormat<WXZHCXRes> responseFormat = null;
            responseFormat = jrBankInvokeService.jrAccountQuery(req);

            if (!responseFormat.isSuccess()) {
                responseFormat.setReturnDesc(ExceptionConstant.JRCB_BANK_ACCOUNT_QUERY_FAILED);
            }
            return responseFormat;
        }
        return ResponseFormat.success();
    }

    public List<CompanyBankCardResponse> queryOrgBankCard(Long orgId) {
        BankCardQueryPara bankCardQueryPara = new BankCardQueryPara();
        bankCardQueryPara.setCompanyUuid(String.valueOf(orgId));
        List<BankCardPara> bankCardParaList = bankCardDao.selectBankCardByCompanyUuid(bankCardQueryPara);

        return bankCardParaList.stream().map(e -> {
            CompanyBankCardResponse companyBankCardResponse = new CompanyBankCardResponse();
            companyBankCardResponse.setAccountBank(e.getAccountBank());
            companyBankCardResponse.setAccountName(e.getAccountName());
            companyBankCardResponse.setAccountNum(e.getAccountNum());
            companyBankCardResponse.setJrcbFlag(Arrays.stream(jiangYinConfig.getBankName().split(",")).anyMatch(name -> e.getAccountBank().contains(name)));
            return companyBankCardResponse;
        }).collect(Collectors.toList());
    }
}
