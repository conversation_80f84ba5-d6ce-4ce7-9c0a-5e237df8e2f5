package com.wxbc.rhine3.service;

import com.wxbc.base.util.JsonUtil;
import com.wxbc.base.util.StringUtil;
import com.wxbc.cou.manager.api.bean.AvailableLimitMoneyPara;
import com.wxbc.cou.manager.api.bean.UpdateCompanyKey;
import com.wxbc.cou.manager.api.constant.url.ApiUrl;
import com.wxbc.cou.manager.api.requestPara.*;
import com.wxbc.cou.manager.api.response.AvailableSumListResponse;
import com.wxbc.cou.manager.api.response.CompanyResponse;
import com.wxbc.cou.manager.api.response.DestroyAuthorizeResponse;
import com.wxbc.cou.manager.api.service.AccessKeyApiService;
import com.wxbc.cou.manager.api.service.CompanySetApiService;
import com.wxbc.cou.manager.api.service.CouQueryApiService;
import com.wxbc.cou.manager.api.service.CouTransferApiService;
import com.wxbc.cou.manager.api.util.KeylessAESUtil;
import com.wxbc.rhine3.bean.FinanceBusinessConfig;
import com.wxbc.rhine3.bean.QueryDestroyAuth;
import com.wxbc.rhine3.bean.ThirdInvokeLogEntity;
import com.wxbc.rhine3.constants.ReturnCode;
import com.wxbc.rhine3.constants.ThirdInvokeLogConstants;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.util.FeignClientUtil;
import com.wxbc.venus.user.dto.AccountUserDTO;
import com.wxbc.venus.user.util.UserUtil;
import com.wxbc.rhine3.utils.ECCUtils;
import com.wxbc.rhine3.utils.TradeVerifiableFactory;
import com.wxbc.scaffold.common.definition.response.ResponseFormat;
import com.wxbc.venus.user.api.feign.CompanyFeignClient;
import com.wxbc.venus.user.dto.OrgExtDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

import static com.wxbc.rhine3.constants.CommonConst.PROJECT;

@Service
@Slf4j
public class SarahService {

    @Autowired
    private CompanySetApiService companySetApiService;

    @Autowired
    private CouQueryApiService couQueryApiService;

    @Autowired
    private AccessKeyApiService accessKeyApiService;

    @Autowired
    private CouTransferApiService couTransferApiService;

    @Autowired
    private ThirdInvokeLogService thirdInvokeLogService;

    @Autowired
    private CompanyFeignClient companyFeignClient;

    public void correctQuotaAndUsedNum(String pubKey, BigInteger issueQuota,
                                       BigInteger usedNum, FinanceBusinessConfig financeBusinessConfig) {
        QuotaAndUsedNumByChainPara para = new QuotaAndUsedNumByChainPara();
        para.setProject(PROJECT);
        para.setPubKey(pubKey);
        para.setIssueQuota(issueQuota);
        para.setUsedNum(usedNum);
        para.setAccessKey(financeBusinessConfig.getChainAccessKey());

        ThirdInvokeLogEntity thirdInvokeLogEntity = thirdInvokeLogService.generalThirdInvoiceLogEntity(
                ApiUrl.CORRECT_QUOTA_AND_USED_NUM_BY_CHAIN,
                ThirdInvokeLogConstants.COU_MANAGER, pubKey, JsonUtil.object2String(para));
        try {
            couTransferApiService.quotaAndUsedNumByChain(para);
            thirdInvokeLogService.fillReturnInfo(null, StringUtil.valueOf(ReturnCode.REQUEST_SUCCESS.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
        } catch (Exception e) {
            log.error("correctQuotaAndUsedNum", e);
            thirdInvokeLogService.fillReturnInfo(e.getMessage(), StringUtil.valueOf(ReturnCode.REQUEST_FAILED.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
        }

    }

    public void correctAvailableSumByChain(String pubKey, String issue,
                                       BigInteger num, FinanceBusinessConfig financeBusinessConfig) {
        AvailableSumByChainPara para = new AvailableSumByChainPara();
        para.setProject(PROJECT);
        para.setPubKey(pubKey);
        para.setIssue(issue);
        para.setNum(num);
        para.setAccessKey(financeBusinessConfig.getChainAccessKey());

        ThirdInvokeLogEntity thirdInvokeLogEntity = thirdInvokeLogService.generalThirdInvoiceLogEntity(
                ApiUrl.CORRECT_AVAILABLE_SUM_BY_CHAIN,
                ThirdInvokeLogConstants.COU_MANAGER, pubKey, JsonUtil.object2String(para));
        try {
            couTransferApiService.availableSumByChain(para);
            thirdInvokeLogService.fillReturnInfo(null, StringUtil.valueOf(ReturnCode.REQUEST_SUCCESS.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
        } catch (Exception e) {
            log.error("correctAvailableSumByChain", e);
            thirdInvokeLogService.fillReturnInfo(e.getMessage(), StringUtil.valueOf(ReturnCode.REQUEST_FAILED.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
        }

    }

    public AvailableSumListResponse getAvailableSumListByCreditAndIssue(String holderPubKey){
        AvailableSumListPara para = new AvailableSumListPara();
        para.setProject(PROJECT);
        para.setHolderPubKey(holderPubKey);

        try {
            return couQueryApiService.availableSumListByCreditAndIssue(para);
        } catch (Exception e) {
            log.error("getAvailableSumListByCreditAndIssue,para={}",para, e);
        }

        return null;
    }


    public void setCompanyQuota(Long newSumQuotaInCent, OrgExtDTO loginCompany,
                                FinanceBusinessConfig financeBusinessConfig,
                                String toPubKey, Long oldSumQuota) throws ParameterException {
        SetCenterQuota setCenterQuota = new SetCenterQuota();

        setCenterQuota.setQuotaInCent(newSumQuotaInCent);
        setCenterQuota.setPubKeyC(toPubKey);
        setCenterQuota.setOldQuotaInCent(oldSumQuota);

        setCenterQuota.setTradeVerifiable(TradeVerifiableFactory.genSetCompanyCouQuota(
                loginCompany, toPubKey, newSumQuotaInCent));

        setCenterQuota.setProject(PROJECT);
        setCenterQuota.setAccessKey(financeBusinessConfig.getChainAccessKey());
        ThirdInvokeLogEntity thirdInvokeLogEntity = thirdInvokeLogService.generalThirdInvoiceLogEntity(ApiUrl.COMPANY_SET_QUOTA,
                ThirdInvokeLogConstants.COU_MANAGER, setCenterQuota.getPubKeyC(), JsonUtil.object2String(setCenterQuota));
        try {
            companySetApiService.updateCompanyQuota(setCenterQuota);
            thirdInvokeLogService.fillReturnInfo(null, StringUtil.valueOf(ReturnCode.REQUEST_SUCCESS.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
        } catch (Exception e) {
            log.error("setCompanyQuota", e);
            thirdInvokeLogService.fillReturnInfo(e.getMessage(), StringUtil.valueOf(ReturnCode.REQUEST_FAILED.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
            throw new ParameterException(e.getMessage());
        }
    }

    public BigDecimal getUsedAmount(String userUuid) throws ParameterException {
        AvailableLimitMoneyPara para = new AvailableLimitMoneyPara();
        para.setOperateUuid(userUuid);
        try {
            return couQueryApiService.queryUsedMoney(para);
        } catch (Exception e) {
            log.error("getUsedAmount", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public void destroyAuth(String fromPubKey, String toPubKey, FinanceBusinessConfig financeBusinessConfig) throws ParameterException {
        AuthorizeDestroyCouRequestPara para = new AuthorizeDestroyCouRequestPara();
        para.setProject(PROJECT);
        para.setIsAuthorize(1);

        para.setAccessKey(financeBusinessConfig.getChainAccessKey());

        OrgExtDTO fromCompany = null;
        if (StringUtils.isEmpty(fromPubKey)) {
            AccountUserDTO user = UserUtil.get();
            fromCompany = user.getCompany();
        } else {
            ResponseFormat<OrgExtDTO> companyResponse = companyFeignClient.getCompanyByPubKey(fromPubKey);
            FeignClientUtil.checkResponse(companyResponse);
            fromCompany=companyResponse.getData();
        }

        AuthorizeDestroyCouRequestPara.AuthorizeDestroyCou authDestroy = new AuthorizeDestroyCouRequestPara.AuthorizeDestroyCou();
        authDestroy.setToPubKey(toPubKey);
        authDestroy.setTradeVerifiable(TradeVerifiableFactory.genDestroyAuth(fromCompany, toPubKey));

        List<AuthorizeDestroyCouRequestPara.AuthorizeDestroyCou> authorizeDestroyCouList = new ArrayList<>();
        authorizeDestroyCouList.add(authDestroy);

        para.setAuthorizeDestroyCouList(authorizeDestroyCouList);

        ThirdInvokeLogEntity thirdInvokeLogEntity = thirdInvokeLogService.generalThirdInvoiceLogEntity(ApiUrl.DESTROY_AUTHORIZE,
                ThirdInvokeLogConstants.COU_MANAGER, null, JsonUtil.object2String(para));
        try {
            couTransferApiService.authorizeDestroyCou(para);
            thirdInvokeLogService.fillReturnInfo(null, StringUtil.valueOf(ReturnCode.REQUEST_SUCCESS.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
        } catch (Exception e) {
            log.error("destroyAuth.couTransferApiService.authorizeDestroyCou", e);
            thirdInvokeLogService.fillReturnInfo(e.getMessage(), StringUtil.valueOf(ReturnCode.REQUEST_FAILED.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
            throw new ParameterException(e.getMessage());
        }
    }

    public Boolean isDestroyAuth(QueryDestroyAuth para) throws ParameterException {
        QueryDestroyAuthorizePara queryPara = new QueryDestroyAuthorizePara();
        BeanUtils.copyProperties(para, queryPara);
        queryPara.setProject(PROJECT);
        List<DestroyAuthorizeResponse> destroyResList = null;
        try {
            destroyResList = couQueryApiService.queryAuthorizeDestroyCou(queryPara);
        } catch (Exception e) {
            log.error("isDestroyAuth.couQueryApiService.queryAuthorizeDestroyCou", e);
            throw new ParameterException(e.getMessage());
        }
        return !CollectionUtils.isEmpty(destroyResList);
    }


    public void registerToCou(String socialCreditCode, String companyPubKey) throws ParameterException {
        CompanyRegisterPara bean = new CompanyRegisterPara();
        bean.setBlnHash(ECCUtils.sha256(socialCreditCode));
        bean.setProject(PROJECT);
        bean.setTradeVerifiable(TradeVerifiableFactory.genRegisterCompany(companyPubKey));
        ThirdInvokeLogEntity thirdInvokeLogEntity = thirdInvokeLogService.generalThirdInvoiceLogEntity(ApiUrl.COMPANY_REGISTER,
                ThirdInvokeLogConstants.COU_MANAGER, companyPubKey, JsonUtil.object2String(bean));
        try {
            companySetApiService.registerCompany(bean);
            thirdInvokeLogService.fillReturnInfo(null, StringUtil.valueOf(ReturnCode.REQUEST_SUCCESS.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
        } catch (Exception e) {
            log.error("registerToCou", e);
            thirdInvokeLogService.fillReturnInfo(e.getMessage(), StringUtil.valueOf(ReturnCode.REQUEST_FAILED.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
            throw new ParameterException(e.getMessage());
        }
    }

    public void changeKeyToCou(String socialCreditCode, String companyPubKey, String accessKey) throws ParameterException {
        UpdateCompanyKey bean = new UpdateCompanyKey();
        bean.setBlnHash(ECCUtils.sha256(socialCreditCode));
        bean.setProject(PROJECT);
        bean.setTradeVerifiable(TradeVerifiableFactory.genRegisterCompany(companyPubKey));
        bean.setAccessKey(accessKey);
        ThirdInvokeLogEntity thirdInvokeLogEntity = thirdInvokeLogService.generalThirdInvoiceLogEntity(ApiUrl.COMPANY_KEY_CHANGE,
                ThirdInvokeLogConstants.COU_MANAGER, companyPubKey, JsonUtil.object2String(bean));
        try {
            companySetApiService.updateCompanyKey(bean);
            thirdInvokeLogService.fillReturnInfo(null, StringUtil.valueOf(ReturnCode.REQUEST_SUCCESS.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
        } catch (Exception e) {
            log.error("changeKeyToCou", e);
            thirdInvokeLogService.fillReturnInfo(e.getMessage(), StringUtil.valueOf(ReturnCode.REQUEST_FAILED.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
            throw new ParameterException(e.getMessage());
        }
    }


    public CompanyResponse companyQueryOne(String companyPubKey) throws ParameterException {
        QueryOneCompanyPara para = new QueryOneCompanyPara();
        para.setPubKey(companyPubKey);
        para.setProject(PROJECT);
        try {
            return couQueryApiService.companyQueryOne(para);
        } catch (Exception e) {
            log.error("changeKeyToCou", e);
            throw new ParameterException(e.getMessage());
        }
    }

    public void updateSarahCompanyBlnHash(String newSocialCreditCode, String pubKey,String oldSocialCreditCode) throws ParameterException {
        if (StringUtils.equals(oldSocialCreditCode, newSocialCreditCode)) {
            return;
        }

        CompanyUpdatePara para = new CompanyUpdatePara();
        para.setBlnHash(ECCUtils.sha256(newSocialCreditCode));
        para.setTargetPubKey(pubKey);
        para.setProject(PROJECT);

        ThirdInvokeLogEntity thirdInvokeLogEntity = thirdInvokeLogService.generalThirdInvoiceLogEntity(ApiUrl.COMPANY_UPDATE,
                ThirdInvokeLogConstants.COU_MANAGER, para.getTargetPubKey(), JsonUtil.object2String(para));
        try {
            companySetApiService.updateCompany(para);
            thirdInvokeLogService.fillReturnInfo(null, StringUtil.valueOf(ReturnCode.REQUEST_SUCCESS.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
        } catch (Exception e) {
            log.error("updateCompany", e);
            thirdInvokeLogService.fillReturnInfo(e.getMessage(), StringUtil.valueOf(ReturnCode.REQUEST_FAILED.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
            throw new ParameterException(e.getMessage());
        }
    }

    public Boolean checkAccessKey(String preAccessKey, String accessKey) throws ParameterException {
        CheckAccessKeyPara para = new CheckAccessKeyPara();
        para.setPreAccessKey(preAccessKey);
        para.setProject(PROJECT);
        para.setAccessKey(accessKey);
        ThirdInvokeLogEntity thirdInvokeLogEntity = thirdInvokeLogService.generalThirdInvoiceLogEntity(ApiUrl.CHAIN_CHECK_ACCESS_KEY,
                ThirdInvokeLogConstants.COU_MANAGER, accessKey, JsonUtil.object2String(para));
        Boolean res = null;
        try {
            res = accessKeyApiService.updateAccessKey(para);
            thirdInvokeLogService.fillReturnInfo(StringUtil.valueOf(res, ""),
                    StringUtil.valueOf(ReturnCode.REQUEST_SUCCESS.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
        } catch (Exception e) {
            log.error("checkAccessKey", e);
            thirdInvokeLogService.fillReturnInfo(e.getMessage(),
                    StringUtil.valueOf(ReturnCode.REQUEST_FAILED.getValue(), ""), thirdInvokeLogEntity);
            thirdInvokeLogService.saveThirdInvokeLog(thirdInvokeLogEntity);
            throw new ParameterException(e.getMessage());
        }
        return res;
    }

}
