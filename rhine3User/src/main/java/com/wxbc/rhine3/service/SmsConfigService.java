package com.wxbc.rhine3.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Splitter;
import com.wxbc.rhine3.bean.Role;
import com.wxbc.rhine3.bean.SendMessageByConfigDTO;
import com.wxbc.rhine3.bean.SmsHistoryEntity;
import com.wxbc.rhine3.bean.User;
import com.wxbc.rhine3.bean.request.SmsConfigAddPara;
import com.wxbc.rhine3.bean.request.SmsConfigDeletePara;
import com.wxbc.rhine3.bean.request.SmsConfigListPara;
import com.wxbc.rhine3.bean.request.SmsConfigQueryPara;
import com.wxbc.rhine3.bean.request.SmsConfigUpdatePara;
import com.wxbc.rhine3.bean.request.oo.QueryOoUsersPara;
import com.wxbc.rhine3.common.Response;
import com.wxbc.rhine3.common.lob.Project;
import com.wxbc.rhine3.constant.UserStatus;
import com.wxbc.rhine3.constants.ReturnCode;
import com.wxbc.rhine3.constants.SmsConfigType;
import com.wxbc.rhine3.constants.SmsContextEnum;
import com.wxbc.rhine3.constants.SmsManagerTypeEnum;
import com.wxbc.rhine3.constants.SmsSendStatusEnum;
import com.wxbc.rhine3.constants.StatusConst;
import com.wxbc.rhine3.exception.BusinessException;
import com.wxbc.rhine3.feign.AdminFeignClient;
import com.wxbc.rhine3.repository.entity.SmsConfigEntity;
import com.wxbc.rhine3.repository.mapper.SmsConfigDao;
import com.wxbc.rhine3.repository.mapper.SmsHistoryDao;
import com.wxbc.rhine3.repository.mapper.UserDao;
import com.wxbc.rhine3.repository.table.SmsConfig;
import com.wxbc.rhine3.service.jrbank.JrBankInvokeService;
import com.wxbc.rhine3.util.FeignClientUtil;
import com.wxbc.rhine3.util.PageUtil;
import com.wxbc.scaffold.common.definition.response.ResponseFormat;
import com.wxbc.scaffold.common.definition.util.Assert;
import com.wxbc.security.modules.sys.dao.SysRoleDao;
import com.wxbc.security.modules.sys.dao.SysUserRoleDao;
import com.wxbc.security.modules.sys.entity.SysRoleEntity;
import com.wxbc.security.modules.sys.entity.SysUserRoleEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class SmsConfigService {


    @Resource
    private SmsConfigDao smsConfigDao;
    @Resource
    private SysRoleDao sysRoleDao;
    @Resource
    private SysUserRoleDao sysUserRoleDao;
    @Resource
    private UserDao userDao;
    @Resource
    private AdminFeignClient adminFeignClient;

    @Autowired
    private JrBankInvokeService jrBankInvokeService;

    @Autowired
    private SmsHistoryDao smsHistoryDao;

    public List<String> getSceneSmsMobiles(SmsConfigQueryPara smsConfigQueryPara) {
        SmsConfigEntity entity = querySmsConfig(smsConfigQueryPara.getCode());
        if (entity.getType() == SmsConfigType.FIXED) {
            return Splitter.on(",").splitToList(entity.getValues());
        }
        if (entity.getType() == SmsConfigType.ROLE) {
            List<User> users = getSceneSmsRoleUsers(smsConfigQueryPara);
            return users.stream().map(User::getMobile).distinct().collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public List<User> getSceneSmsRoleUsers(SmsConfigQueryPara smsConfigQueryPara) {
        SmsConfigEntity entity = querySmsConfig(smsConfigQueryPara.getCode());

        List<User> userList = new ArrayList<>();
        if (entity.getType() == SmsConfigType.FIXED) {
            return userList;
        }
        if (entity.getType() == SmsConfigType.ROLE) {
            String[] roles = entity.getValues().split(",");
            //查角色信息
            List<SysRoleEntity> sysRoleList = queryRoleList(roles);
            if (CollectionUtils.isEmpty(sysRoleList))
                return Collections.emptyList();
            //查用户角色对应关系
            List<SysUserRoleEntity> sysUserRoleList = queryUserRoleList(sysRoleList.stream().map(SysRoleEntity::getId).collect(Collectors.toList()));

            List<SysUserRoleEntity> ooUserRoleList = new ArrayList<>();
            Iterator<SysUserRoleEntity> iterator = sysUserRoleList.iterator();
            while (iterator.hasNext()) {
                SysUserRoleEntity item = iterator.next();
                if (item.getProjectCode().equals(Project.OO.code())) {
                    ooUserRoleList.add(item);
                    iterator.remove();
                }
            }
            List<User> bizUsers = queryUserList(sysUserRoleList, smsConfigQueryPara.getCompanyUuids());
            List<User> ooUsers = queryOoUserList(ooUserRoleList, smsConfigQueryPara.getCompanyUuids());
            if (!CollectionUtils.isEmpty(bizUsers)) {
                userList.addAll(bizUsers);
            }
            if (!CollectionUtils.isEmpty(ooUsers)) {
                userList.addAll(ooUsers);
            }
        }
        return userList;
    }


    public PageInfo<SmsConfigEntity> queryList(SmsConfigListPara para) {
        IPage<SmsConfigEntity> page = PageUtil.requestToIPage(para);
        IPage<SmsConfigEntity> retPage = smsConfigDao.selectPage(page, new QueryWrapper<SmsConfigEntity>()
                .eq(!StringUtils.isEmpty(para.getCode()), SmsConfig.CODE, para.getCode())
                .eq(SmsConfig.STATUS, StatusConst.VALID)
                .orderByDesc(SmsConfig.UPDATE_TIME));
        PageInfo<SmsConfigEntity> respPage = new PageInfo<>();
        PageUtil.copyPaginationInfo(retPage, respPage);

        List<SmsConfigEntity> smsConfigEntityList = retPage.getRecords();
        List<String> roleIdList = extractAndDeduplicateValueByType(smsConfigEntityList, SmsConfigType.ROLE);
        Map<Long, String> roleMap = getRoleMap(roleIdList);

        smsConfigEntityList.stream().filter(e -> StringUtils.hasLength(e.getValues())).forEach(e -> {
            if (SmsConfigType.ROLE.equals(e.getType()) && !CollectionUtils.isEmpty(roleMap)) {
                enrichRoleNames(e, roleMap);
            }
            if (SmsConfigType.MANAGER.equals(e.getType())) {
                enrichManagerTypes(e);
            }
        });

        respPage.setList(smsConfigEntityList);
        return respPage;
    }

    /**
     * 提取并去重短信配置实体中指定类型value
     *
     * @param list 短信配置实体
     * @param type 短信配置类型
     * @return 角色id列表
     */
    private List<String> extractAndDeduplicateValueByType(List<SmsConfigEntity> list, SmsConfigType type) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream()
                .filter(entity -> type.equals(entity.getType()))
                .flatMap(entity -> Stream.of(entity.getValues().split(",")))
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取角色的id-角色名映射表
     *
     * @param roleIdList 角色id列表
     * @return 角色的id-角色名映射表
     */
    private Map<Long, String> getRoleMap(List<String> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            return Collections.emptyMap();
        }
        List<Role> roleList = userDao.queryRoleListByRoleIdList(roleIdList);
        return roleList.stream()
                .collect(Collectors.toMap(Role::getId, Role::getRoleName));
    }

    /**
     * 填充角色名称
     *
     * @param entity  短信配置实体
     * @param roleMap 角色的id-角色名映射表
     */
    private void enrichRoleNames(SmsConfigEntity entity, Map<Long, String> roleMap) {
        List<String> roleNameList = Splitter
                .on(",")
                .splitToList(entity.getValues())
                .stream()
                .map(Long::valueOf)
                .map(roleMap::get)
                .collect(Collectors.toList());
        entity.setRoleNameList(roleNameList);
    }

    /**
     * 填充客户经理名称
     *
     * @param entity 短信配置实体
     */
    private void enrichManagerTypes(SmsConfigEntity entity) {
        List<String> managerTypeNameList = Splitter
                .on(",")
                .splitToList(entity.getValues())
                .stream()
                .map(Integer::valueOf)
                .map(SmsManagerTypeEnum::getNameByCode)
                .collect(Collectors.toList());
        entity.setRoleNameList(managerTypeNameList);
    }

    public void add(SmsConfigAddPara para) {
        SmsConfigEntity entity = new SmsConfigEntity();
        BeanUtils.copyProperties(para, entity);
        smsConfigDao.insert(entity);
    }

    public void update(SmsConfigUpdatePara para) {
        SmsConfigEntity entity = new SmsConfigEntity();
        BeanUtils.copyProperties(para, entity);
        smsConfigDao.updateById(entity);
    }

    public void delete(SmsConfigDeletePara para) {
        UpdateWrapper<SmsConfigEntity> uw = new UpdateWrapper<>();
        uw.set(SmsConfig.STATUS, StatusConst.INVALID)
                .eq(SmsConfig.ID, para.getId());
        smsConfigDao.update(null, uw);
    }


    private List<SysUserRoleEntity> queryUserRoleList(List<Long> roleIds) {
        QueryWrapper<SysUserRoleEntity> userRoleQw = new QueryWrapper<>();
        userRoleQw.eq("status", "1");
        userRoleQw.in("role_id", roleIds);
        return sysUserRoleDao.selectList(userRoleQw);
    }

    private List<SysRoleEntity> queryRoleList(String[] roles) {
        QueryWrapper<SysRoleEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", "1");
        queryWrapper.in("role_name", Arrays.asList(roles));
        return sysRoleDao.selectList(queryWrapper);
    }

    private List<User> queryUserList(List<SysUserRoleEntity> list, List<String> companyUuids) {
        if (!CollectionUtils.isEmpty(list)) {
            List<User> users = userDao.queryUserListByIds(list.stream().map(SysUserRoleEntity::getUserId).collect(Collectors.toList()), UserStatus.ACTIVE.getVal());
            if (!CollectionUtils.isEmpty(companyUuids)) {
                return users.stream().filter(x -> companyUuids.contains(x.getCompanyUuid())).collect(Collectors.toList());
            } else {
                return users;
            }
        }
        return Collections.emptyList();
    }

    private List<User> queryOoUserList(List<SysUserRoleEntity> list, List<String> companyUuids) {
        if (!CollectionUtils.isEmpty(list)) {
            Response<List<User>> resp = adminFeignClient.getUserListByUserIds(new QueryOoUsersPara(list.stream().map(SysUserRoleEntity::getUserId).collect(Collectors.toList())));
            FeignClientUtil.checkResponse(resp);
            if (!CollectionUtils.isEmpty(companyUuids)) {
                return resp.getData().stream().filter(x -> companyUuids.contains(x.getCompanyUuid())).collect(Collectors.toList());
            } else {
                return resp.getData();
            }
        }
        return Collections.emptyList();

    }

    private SmsConfigEntity querySmsConfig(String code) {
        SmsConfigEntity entity = smsConfigDao.selectOne(new QueryWrapper<SmsConfigEntity>().eq(SmsConfig.CODE, code).eq(SmsConfig.STATUS, StatusConst.VALID));
        if (Objects.isNull(entity)) {
            throw new BusinessException("未找到短信配置数据");
        }
        return entity;
    }

    /**
     * 通过模版号获取发送短信手机号列表
     *
     * @param smsConfig 短信配置实体
     * @param orgId     公司ID
     * @return 发送短信手机号列表
     */
    private List<String> getSceneSmsMobilesNew(SmsConfigEntity smsConfig, Long orgId) {
        List<String> result = Collections.emptyList();

        SmsConfigType type = smsConfig.getType();
        String values = smsConfig.getValues();
        // 判断手机号或者角色不能为空
        Assert.isTrue(StringUtils.hasLength(values), ReturnCode.SEND_MESSAGE_VALUES_IS_NULL);

        if (type == SmsConfigType.FIXED) {
            return Splitter.on(",").splitToList(values);
        }
        // 如果不是固定手机号，则orgId为必输
        Assert.notNull(orgId, ReturnCode.SEND_MESSAGE_ORG_ID_IS_NULL, smsConfig.getCode());

        List<String> companyUuidList = Collections.singletonList(String.valueOf(orgId));
        switch (type) {
            case ROLE:
                List<Long> userIdList = userDao.queryUserIdsByRoleIdsAndOrgIds(Splitter.on(",").splitToList(values), companyUuidList);
                if (!CollectionUtils.isEmpty(userIdList)) {
                    result = userDao.queryRoleUserMobilesByUserIds(userIdList);
                }
                break;
            case MANAGER:
                result = userDao.queryManagerMobileByOrgIdsAndCreditTypes(Splitter.on(",").splitToList(values), companyUuidList);
                break;
            default:
                break;
        }

        return result;
    }

    /**
     * 通过模版配置发送短信
     *
     * @param dto 通过模版配置发送短信参数
     */
    public void sendMessageByConfig(SendMessageByConfigDTO dto) {
        SmsContextEnum smsContextEnum = dto.getSmsContextEnum();
        List<String> varList = dto.getVarList();

        if (CollectionUtils.isEmpty(varList)) {
            log.info("sendMessageByConfig has empty varList, smsContextEnum:{}", smsContextEnum);
            return;
        }

        Long orgId = dto.getOrgId();
        String content = String.format(smsContextEnum.getContext(), varList.toArray());

        SmsConfigEntity smsConfig = querySmsConfig(smsContextEnum.getCode());
        List<String> mobileList = getSceneSmsMobilesNew(smsConfig, orgId);

        if (CollectionUtils.isEmpty(mobileList)) {
            log.warn("sendMessageByConfig has no valid mobile passed by code:{}, value:{}, orgId:{}",
                    smsContextEnum.getCode(), String.join(",", varList), orgId);
            return;
        }
        log.info("smsContextEnum:{},sendMessageByConfig mobile:{}", smsContextEnum, String.join(",", mobileList));

        // 如果flag为true需要过滤今天是否发送过短信
        if (dto.getSaveFlag()) {
            List<String> succeedMobileList = smsHistoryDao.queryMobileByCondition(orgId, SmsSendStatusEnum.SUCCEED.getValue(), LocalDate.now(), dto.getSmsContextEnum());
            mobileList = mobileList.stream().filter(mobile -> !succeedMobileList.contains(mobile)).collect(Collectors.toList());
        }

        for (String mobile : mobileList) {
            sendMessageByJrcb(dto, content, mobile, smsConfig.getChannel());
        }
    }

    /**
     * 通过江阴银行接口发送短信
     *
     * @param dto     通过模版配置发送短信参数
     * @param content 短信内容
     * @param mobile  短信号码
     * @param channel 短信渠道
     */
    public void sendMessageByJrcb(SendMessageByConfigDTO dto, String content, String mobile, Integer channel) {
        ResponseFormat response = jrBankInvokeService.jrSendMsgByChannel(content, mobile, channel, false);
        // 短信发送保存所有通知短信记录
        boolean ifSucceed = Objects.nonNull(response) && response.isSuccess();
        saveOrUpdateSmsHistory(dto, mobile, ifSucceed);
    }

    /**
     * 保存或更新短信发送记录
     *
     * @param dto       通过模版配置发送短信参数
     * @param mobile    发送手机号
     * @param ifSucceed 是否成功
     */
    private void saveOrUpdateSmsHistory(SendMessageByConfigDTO dto, String mobile, boolean ifSucceed) {
        SmsHistoryEntity entity = new SmsHistoryEntity();
        SmsContextEnum smsContextEnum = dto.getSmsContextEnum();
        entity.setStatus(ifSucceed ? 1 : 0);
        entity.setOrgId(Objects.isNull(dto.getOrgId()) ? 0 : dto.getOrgId());
        entity.setTemplateCode(smsContextEnum);
        entity.setMobile(mobile);
        entity.setParas(String.join(",", dto.getVarList()));
        entity.setSendDate(LocalDate.now());

        LambdaQueryWrapper<SmsHistoryEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SmsHistoryEntity::getOrgId, dto.getOrgId())
                .eq(SmsHistoryEntity::getMobile, mobile)
                .eq(SmsHistoryEntity::getTemplateCode, smsContextEnum)
                .eq(SmsHistoryEntity::getSendDate, LocalDate.now());
        SmsHistoryEntity exist = dto.getSaveFlag() ? smsHistoryDao.selectOne(wrapper) : null;

        if (Objects.nonNull(exist)) {
            entity.setId(exist.getId());
            smsHistoryDao.updateById(entity);
        } else {
            smsHistoryDao.insert(entity);
        }
    }
}
