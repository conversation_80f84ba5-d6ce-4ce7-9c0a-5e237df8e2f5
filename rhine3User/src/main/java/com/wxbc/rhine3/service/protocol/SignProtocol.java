package com.wxbc.rhine3.service.protocol;

import com.wxbc.rhine3.bean.ProtocolPara;
import com.wxbc.rhine3.bean.ProtocolSign;
import com.wxbc.rhine3.common.Response;
import com.wxbc.rhine3.constant.NacosConfigPara;
import com.wxbc.rhine3.feign.AssetFeignClient;
import com.wxbc.rhine3.repository.mapper.CompanyDao;
import com.wxbc.rhine3.repository.mapper.ProtocolSignDao;
import com.wxbc.rhine3.util.FeignClientUtil;
import com.wxbc.venus.user.api.feign.CompanyFeignClient;
import com.wxbc.venus.user.dto.AccountUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.Future;

@Slf4j
@Service
public class SignProtocol {
    @Autowired
    private NacosConfigPara nacosConfigPara;
    @Autowired
    private AssetFeignClient assetFeignClient;
    @Autowired
    private ProtocolSignDao protocolSignDao;
    @Resource
    private CompanyDao companyDao;

    @Autowired
    private CompanyFeignClient companyFeignClient;

    @Async("cfcaThreadPool")
    public Future<Boolean> signProtocolByCfca(String fileUrl, Integer protocolVersion, AccountUserDTO user, String protocolType, String projectCode) {
        ProtocolPara protocolPara = new ProtocolPara();
        protocolPara.setFileUrl(fileUrl);
        protocolPara.setSignType(nacosConfigPara.getSignatureUserKeySignType());
        protocolPara.setCoordinateX(nacosConfigPara.getSignatureUserKeyCoordinateX());
        protocolPara.setCoordinateY(nacosConfigPara.getSignatureUserKeyCoordinateY());
        protocolPara.setKeyword(nacosConfigPara.getSignatureUserKeyword());
        protocolPara.setPageIndex(nacosConfigPara.getSignatureUserKeyPageIndex());
        protocolPara.setCompanyUuid(user.getCompanyUuid());

        //改为通过Feign调用
//        String OrgName = companyFeignClient.getCompanyById(String.valueOf(user.getOrgId())).getData().getOrgName();
        String OrgName = companyDao.selectCompanyName(user.getOrgId());
        protocolPara.setCompanyName(OrgName);


        Response response = assetFeignClient.signatureFile(protocolPara);
        FeignClientUtil.checkResponse(response);

        ProtocolSign protocolSign = new ProtocolSign();
        protocolSign.setUserId(Long.valueOf(user.getId()));
        protocolSign.setCompanyUuid(user.getCompanyUuid());
        protocolSign.setProtocolUrl(fileUrl);
        protocolSign.setProtocolType(protocolType);
        protocolSign.setVersionId(protocolVersion);
        protocolSign.setProjectCode(projectCode);
        protocolSignDao.insertProtocolSign(protocolSign);
        return AsyncResult.forValue(Boolean.TRUE);

    }
}
