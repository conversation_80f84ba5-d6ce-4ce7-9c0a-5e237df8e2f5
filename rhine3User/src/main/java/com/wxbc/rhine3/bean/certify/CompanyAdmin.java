package com.wxbc.rhine3.bean.certify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import static com.wxbc.rhine3.common.OoRegExceptionConstant.*;

@Data
@ApiModel
public class CompanyAdmin {
    @NotNull
    @ApiModelProperty(value = "企业管理员id", required = true)
    private Long adminId;

    @NotBlank(message = ADMIN_EMAIL_BLANK)
    @Size(max = 64, message = ADMIN_EMAIL_LEN_FAIL)
    @Pattern(regexp = EMAIL_REGEX, message = ADMIN_EMAIL_PATTERN_FAIL)
    @ApiModelProperty(value = "企业管理员邮箱", required = true)
    private String email;

    @NotBlank(message = ADMIN_MOBILE_BLANK)
    @Size(max = 11, message = ADMIN_MOBILE_LEN_FAIL)
    @Pattern(regexp = TEL_REGEX, message = ADMIN_MOBILE_PATTERN_FAIL)
    @ApiModelProperty(value = "手机号", required = true)
    private String mobile;

}
