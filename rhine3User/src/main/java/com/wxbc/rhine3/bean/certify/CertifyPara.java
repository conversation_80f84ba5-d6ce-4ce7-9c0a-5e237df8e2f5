package com.wxbc.rhine3.bean.certify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;


@Data
@ApiModel(value = "公司认证参数")
public class CertifyPara {
    @Valid
    @NotNull
    @ApiModelProperty(value = "公司全称、统一社会信用代码等基本信息",required = true)
    private CompanyBasic companyBasic;

    @Valid
    @NotNull
    @ApiModelProperty(value = "公司法人相关信息",required = true)
    private CompanyLegalPerson companyLegalPerson;

    @Valid
    @NotNull
    @ApiModelProperty(value = "公司地址、规模相关信息",required = true)
    private CompanyAddr companyAddr;
}
