package com.wxbc.rhine3.bean.invoice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("推送发票request")
@Data
public class CompanyInvoiceNotifyRequest {

    @ApiModelProperty(value = "发票数据ID",required = true)
    private List<Long> idList;

    @ApiModelProperty("开票开始日期(yyyy-MM-dd)")
    private String invoiceDateFrom;

    @ApiModelProperty("开票截止日期(yyyy-MM-dd)")
    private String invoiceDateTo;
}
