package com.wxbc.rhine3.bean;

import com.wxbc.rhine3.common.PagePara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@ApiModel
@ToString(callSuper = true)
public class CompanyQueryPara  extends PagePara {
    @ApiModelProperty(value = "企业id")
    private Long id;
    @ApiModelProperty(value = "企业uuid")
    private String uuid;
    @ApiModelProperty(value = "企业名称")
    private String companyName;
    @ApiModelProperty(value = "公司全称校验状态：CompanyNameStatusEnum:1-未校验，2-正常，3-异常")
    private String companyNameStatus;

    @ApiModelProperty(value = "FI(金融机构), FP(金融机构母公司),C(核心企业),S(供应商),B(票据普通企业) 逗号分隔，可以有多个类型")
    private String type;

    @ApiModelProperty(value = "认证状态")
    private String status;

    @ApiModelProperty(value = "interest状态值,ps:发现此字段后端并未使用")
    private String interestStatus;

    @ApiModelProperty(value = "保理业务状态")
    private String businessStatus;

    @ApiModelProperty(value = "票据业务状态")
    private String billBusinessStatus;

    @ApiModelProperty(value = "订单业务状态")
    private String orderBusinessStatus;

    @ApiModelProperty(value = "工商校验状态")
    private String commerceStatus;
    private Boolean commerceVerified;

    @ApiModelProperty("运营机构id")
    private Long operatingOrgId;
    @ApiModelProperty(value = "运营机构Uuid")
    private String operatingOrganizationUuid;


    @ApiModelProperty(value = "typeList集合,ps:发现此字段后端并未使用")
    private List<String> typeList;

    @ApiModelProperty(value = "融资付息方式(supplier-供应商付息 center-核心企业付息)")
    private String interestPayWay;

    @ApiModelProperty(value = "创建日期开始的13位时间戳")
    private Date startDate;

    @ApiModelProperty(value = "创建日期结束的13位时间戳")
    private Date endDate;

    @ApiModelProperty(value = "注册来源/所属项目")
    private String projectCode;
}
