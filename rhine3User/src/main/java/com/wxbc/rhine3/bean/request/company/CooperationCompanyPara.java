package com.wxbc.rhine3.bean.request.company;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @company: 万向区块链
 * @description 合作企业参数实体
 * @author: lixingxing
 * @create: 2023-01-04 10:22
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("合作企业参数实体")
public class CooperationCompanyPara  extends  CooperationCompanyBasePara{

    /**
     * 合作公司名称
     */
    @NotBlank(message = "合作公司名称不能为空")
    @ApiModelProperty(value = "合作公司名称",required = true)
    private String cooperationCompanyName;

    /**
     * 合作企业uuid(如果是平台企业时则有值,否则为空)
     */
    @ApiModelProperty(value = "合作企业uuid，(如果是平台企业时则有值,否则为空)")
    private String cooperationCompanyUuid;

    /**
     * 合作公司社会信用代码
     */
    @NotBlank(message = "合作公司社会信用代码不能为空")
    @ApiModelProperty(value = "合作公司社会信用代码",required = true)
    private String socialCreditCode;

    /**
     * 合作企业法人姓名
     */
    @NotBlank(message = "合作企业法人姓名不能为空")
    @ApiModelProperty(value = "合作企业法人姓名",required = true)
    private String legalPersonName;

    /**
     * 交易关系 1-买方  2-卖方 3-买卖双方
     */
    @NotNull
    @Max(value = 3,message = "交易关系传值不正确")
    @Min(value = 1, message = "交易关系传值不正确")
    @ApiModelProperty(value = "交易关系 1-买方  2-卖方 3-买卖双方",required = true)
    private Integer transactionRelationship;


}
