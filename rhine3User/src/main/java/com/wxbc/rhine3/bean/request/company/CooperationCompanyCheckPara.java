package com.wxbc.rhine3.bean.request.company;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @company: 万向区块链
 * @description 合作企业验重参数实体
 * @author: lixingxing
 * @create: 2023-01-04 10:22
 **/
@Data
@ApiModel("合作企业验重参数实体")
public class CooperationCompanyCheckPara {

    @ApiModelProperty(value = "$可非必填$,记录id,如果是修改操作的时候必传")
    private Integer id;

    /**
     * 合作公司名称
     */
    @ApiModelProperty(value = "合作公司名称")
    private String cooperationCompanyName;

    /**
     * 合作公司社会信用代码
     */
    @ApiModelProperty(value = "合作公司社会信用代码")
    private String socialCreditCode;




}
