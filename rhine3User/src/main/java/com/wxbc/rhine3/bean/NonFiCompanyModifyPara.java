package com.wxbc.rhine3.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel
public class NonFiCompanyModifyPara {
    @NotNull
    @ApiModelProperty(value = "企业基本信息")
    private NonFiBaseCompany baseCompany;

    @NotNull
    @ApiModelProperty(value = "companyUuid")
    private String uuid;
}
