package com.wxbc.rhine3.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @company: 万向区块链
 * @description 企业三要素认证返回对象
 * @author: lixingxing
 * @create: 2022-09-05 14:22
 **/
@Data
@ApiModel
public class CompanyThreeElementsRes {

    @ApiModelProperty("返回的接口调用描述")
    private String msg;

    @ApiModelProperty("返回的接口调用状态")
    private String success;

    @ApiModelProperty("返回的接口调用状态码 200-成功")
    private String code;

    @ApiModelProperty("返回的认证实体对象")
    private CompanyThreeElementsDataRes data;

}
