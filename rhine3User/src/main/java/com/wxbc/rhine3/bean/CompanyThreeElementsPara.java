package com.wxbc.rhine3.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @company: 万向区块链
 * @description 企业三要素认证参数
 * @author: lixingxing
 * @create: 2022-09-05 14:18
 **/
@Data
@ApiModel("企业三要素认证参数")
public class CompanyThreeElementsPara {

    @NotBlank
    @ApiModelProperty(value = "企业名称",required = true)
    private String companyName;

    @NotBlank
    @ApiModelProperty(value = "企业社会信用代码",required = true)
    private String creditNo;

    @NotBlank
    @ApiModelProperty(value = "企业法人姓名",required = true)
    private String legalPerson;
}
