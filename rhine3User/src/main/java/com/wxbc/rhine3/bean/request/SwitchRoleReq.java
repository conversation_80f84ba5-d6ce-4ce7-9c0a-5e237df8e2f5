package com.wxbc.rhine3.bean.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * service-vena
 *
 * <AUTHOR> chen cheng
 * Date         : 2023/4/19 9:47
 * Jdk          : 1.8
 * Description  :
 */
@Data
@ApiModel
public class SwitchRoleReq {

    @NotBlank(message = "角色名称必填")
    @ApiModelProperty(value = "角色名称",required = true)
    private String roleName;

    @NotBlank(message = "业务端必填")
    @ApiModelProperty(value = "业务端",required = true)
    private String projectCode;
}
