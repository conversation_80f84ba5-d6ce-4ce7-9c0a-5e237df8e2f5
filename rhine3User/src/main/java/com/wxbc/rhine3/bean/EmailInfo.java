package com.wxbc.rhine3.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Getter
@Setter
@ToString
@ApiModel
public class EmailInfo {
    @NotBlank
    @ApiModelProperty(value = "邮箱地址",required = true)
    private String emailAddress;
    @NotBlank
    @ApiModelProperty(value = "邮件主题",required = true)
    private String subject;
    @ApiModelProperty(value = "邮件内容",required = true)
    private String messageText;
    @ApiModelProperty(value = "是否忽略邮件敏感内容")
    private Boolean needIgnore;
    @ApiModelProperty(value = "敏感内容字符串列表")
    private List<String> ignoreStr;
    @ApiModelProperty(value = "抄送地址")
    private String ccAddress;
    @ApiModelProperty(value = "密送地址")
    private String bccAddress;
    @ApiModelProperty(value = "项目名称")
    private String projectName;
}
