package com.wxbc.rhine3.bean;

import com.wxbc.verification.vo.CompanyThreeElementsPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class CompanyCommercePara extends CompanyThreeElementsPara {

    @NotBlank(message = "企业uuid不能为空")
    @ApiModelProperty(value = "企业uuid", required = true)
    private String uuid;
}
