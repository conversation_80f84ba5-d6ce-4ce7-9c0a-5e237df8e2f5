package com.wxbc.rhine3.schedule;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.wxbc.base.util.JsonUtil;
import com.wxbc.cou.manager.api.response.AvailableSumGroup;
import com.wxbc.cou.manager.api.response.AvailableSumListResponse;
import com.wxbc.rhine3.bean.Company;
import com.wxbc.rhine3.bean.FinanceBusinessConfig;
import com.wxbc.rhine3.constant.CorrectStatusEnum;
import com.wxbc.rhine3.constant.RedisLockConstant;
import com.wxbc.rhine3.redis.RedisService;
import com.wxbc.rhine3.repository.entity.CorrectSLogEntity;
import com.wxbc.rhine3.repository.mapper.CorrectSLogDao;
import com.wxbc.rhine3.service.FinanceBusinessConfigService;
import com.wxbc.rhine3.service.SarahService;
import com.wxbc.rhine3.service.company.CompanyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> zhengjiao
 * @Date : 2023-06-30 15:38
 * @Description :
 */
@Slf4j
@Component
public class CorrectS {
    @Autowired
    private SarahService sarahService;
    @Resource
    private CompanyService companyService;
    @Autowired
    private FinanceBusinessConfigService financeBusinessConfigService;
    @Autowired
    private CorrectSLogDao correctSLogDao;
    @Autowired
    private RedisService redisService;

    /***
     *   183版本新增供应商余额对账定时任务 每天凌晨1点都执行一次
     */
    //@Scheduled(cron = "0 0 1 * * ?")
    public void correctAvailableSum() {
        log.info("begin correctAvailableSum schedule ....");

        if (!redisService.setNxLock(RedisLockConstant.CORRECT_S_REDIS_LOCK_KEY, RedisLockConstant.MIN_10)) {
            log.info("correctAvailableSum end! no Lock!");
            return;
        }

        List<String> pubKeys = companyService.selectPubKeyAll4Correct();
        for (String pubKey : pubKeys) {
            try {
                singleCompanyCorrectAvailableSum(pubKey);
            } catch (Exception e) {
                log.error("correctAvailableSum: error! pubKey={}", pubKey, e);
            }
        }

        log.info("end correctAvailableSum schedule ....");
    }

    private void singleCompanyCorrectAvailableSum(String companyPubKey) {
        AvailableSumListResponse sumList = sarahService.getAvailableSumListByCreditAndIssue(companyPubKey);
        if (null == sumList || CollectionUtils.isEmpty(sumList.getAvailableSumGroups())) {
            log.info("singleCompanyCorrectAvailableSum:companyPubKey={} is empty!", companyPubKey);
            return;
        }

        CorrectSLogEntity cLogEntity = new CorrectSLogEntity();
        cLogEntity.setCompanyPubKey(companyPubKey);
        cLogEntity.setAccDb(JsonUtil.object2String(sumList));
        cLogEntity.setStatus(CorrectStatusEnum.OK.getValue());

        for (AvailableSumGroup group : sumList.getAvailableSumGroups()) {
            try {
                //查询金融机构配置
                FinanceBusinessConfig fiBusinessConfig = getFiBusinessConfig(group);
                if (fiBusinessConfig != null) {
                    sarahService.correctAvailableSumByChain(companyPubKey, group.getPublishPubKey(),
                            group.getSumCouAmountInCent().toBigInteger(), fiBusinessConfig);
                } else {
                    cLogEntity.setStatus(CorrectStatusEnum.ERROR.getValue());
                    log.error("singleCompanyCorrectAvailableSum:fiBusinessConfig is null! companyPubKey={},group={}",
                            companyPubKey,group);
                }
            }catch (Exception e){
                cLogEntity.setStatus(CorrectStatusEnum.ERROR.getValue());
                log.error("singleCompanyCorrectAvailableSum: error!companyPubKey={}, group={}",
                        companyPubKey,group,e);
            }

        }

        correctSLogDao.insert(cLogEntity);
    }

    private FinanceBusinessConfig getFiBusinessConfig(AvailableSumGroup group) {

        Company fi = companyService.selectCompanyByPubKey(group.getCreditPubKey());
        if (null == fi) {
            log.error("singleCompanyCorrectAvailableSum:fi is null! group={}", group);
            return null;
        }

        return financeBusinessConfigService.checkFinanceConfig(fi.getUuid());
    }

}
