package com.wxbc.rhine3.constant;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date : 2023-06-09 14:01
 * @Description :
 */
@Getter
public enum CompanyNameStatusEnum {
    MATCH("1","正常"),
    MISMATCH("2","异常");

    @Getter
    @Setter
    private String value;
    @Getter
    @Setter
    private String desc;

    CompanyNameStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
