package com.wxbc.rhine3.constant.returnCode;

import com.wxbc.scaffold.common.definition.response.IReturnCode;
import lombok.AllArgsConstructor;

/**
 * financing-service
 *
 * <AUTHOR>
 * Date         : 2024/04/01
 * Description  :
 */
@AllArgsConstructor
public enum PledgeConfigReturnCode implements IReturnCode {

    PLE_SMS_BOMBING(70001, "短信重复发送"),


    FACE_PHOTO_UPLOAD_JRCB_ERROR(70002, "人脸识别文件上传失败"),

    FACE_PHOTO_JRCB_REQUEST_ERROR(70003, "人脸识别请求失败"),

    ;


    private final int code;

    private final String desc;

    @Override
    public int getValue() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
