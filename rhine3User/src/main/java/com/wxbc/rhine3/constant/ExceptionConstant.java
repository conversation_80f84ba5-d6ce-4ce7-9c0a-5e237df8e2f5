package com.wxbc.rhine3.constant;

import lombok.Getter;

@Getter
public class ExceptionConstant {
    private ExceptionConstant() {
        //do nothing
    }

    public static final String PARA_FAIL = "参数错误";
    public static final String CREDIT_NOTFOUND_COMPANY = "企业无效。请检查企业全称是否有误，并确认该企业已完成认证流程";

    public static final String KEY_CREATE_FAIL = "密钥生成异常，请联系平台方";
    public static final String KEY_UPDATE_FAIL = "密钥升级异常请重新登录尝试，或联系平台方";
    public static final String MATCH_FAIL = "邮箱或手机号错误";
    public static final String USER_MATCH_FAIL = "账号或密码错误";
    public static final String KEY_ENC_FAIL = "您的企业还未密钥升级，无法使用此功能";
    public static final String COMPANY_STATUS_INIT = "您的企业账户还在审核中，如有疑问请联系平台";
    public static final String COMPANY_STATUS_REJECT = "您的企业账户审核未通过，如有疑问请联系平台";
    public static final String COMPANY_EXIT_CHECK = "管理员邮箱已绑定其他企业";
    public static final String COMPANY_SOCIAL_CODE_CHECK = "企业信息已被注册使用";
    public static final String EMAIL_EXIT_CHECK = "邮箱已使用";
    public static final String EMAIL_EXIT_REGISTER = "邮箱已注册";
    public static final String SEAL_NOT_FOUND = "企业未申请CFCA印章，请联系平台运营申请";
    public static final String NOT_SIGN_NEW_PROTOCOL = "当前企业未签署最新的平台协议，请联系管理员登录平台签署";
    public static final String SIGN_PROTOCOL_ERROR = "当前企业未签署最新的平台协议，请联系平台运营";


    public static final String FILE_TOO_LONG = "文件列表内容过长";
    public static final String DATA_NOT_FOUND = "数据不存在";
    public static final String DATA_EXISTS = "数据已存在";

    public static final String SMS_CODE_BLANK = "短信验证码为空";
    public static final String SMS_CODE_NOT_MATCH = "短信验证码错误";
    public static final String SMS_CODE_NOT_FOUND = "短信验证码错误";
    public static final String SMS_CODE_SEND_FAIL = "发送验证码失败";
    public static final String EMAIL_CODE_NOT_MATCH = "邮箱验证码错误";

    public static final String BANK_CODE_NO_FOUND = "未查到此开户行";
    public static final String ACCOUNT_NAME_FAIL = "户名不能为空";
    public static final String ACCOUNT_NUM_FAIL = "账号不能为空";
    public static final String ACCOUNT_BANK_FAIL = "开户行不能为空";

    public static final String CALL_API_BANK_QUERY_FAILED_BY_NAME = "未查询到此开户行，请填写完整的开户行名称";

    public static final String JRCB_BANK_ACCOUNT_QUERY_FAILED = "未查询到开户行，请手动填写";
    public static final String CALL_API_BANK_QUERY_FAILED = "查银行开户行失败";
    public static final String SAVE_INVOICING_INFO_REPEATED_ERROR = "重复保存开票信息";

    public static final String ADMIN_MOBILE_BLANK = "管理员手机号不能为空";
    public static final String ADMIN_EMAIL_BLANK = "管理员电子邮箱不能为空";
    public static final String ADMIN_EMAIL_LEN_FAIL = "管理员电子邮箱长度有误";

    public static final String MOBILE_BLANK = "手机号不能为空";
    public static final String EMAIL_BLANK = "用户邮箱不能为空";
    public static final String EMAIL_LEN_FAIL = "电子邮箱长度有误";

    public static final String INDUSTRY_BLANK= "企业所属行业不能为空";
    public static final String CORPORATION_SCALE_BLANK = "企业规模不能为空";

    public static final String PROTOCOL_BLANK = "协议必须勾选";
    public static final String UUID_BLANK = "uuid不能为空";
    public static final String USER_NO_FOUND = "用户不存在";
    public static final String TO_DEL_OTHER_COMPANY_USER = "只能删除本公司用户";
    public static final String ADMIN_CANNOT_TO_DEL = "不能删除管理员";
    public static final String USER_NAME_BLANK = "用户名不能为空";
    public static final String USER_NAME_LEN_FAIL = "用户名长度有误";
    public static final String ROLE_BLANK = "角色权限不能为空";
    public static final String USER_LOGIN_TYPE_BLANK = "用户登录企业类型获取失败";
    public static final String ROLE_FAIL = "没有权限执行此操作";

    public static final String COMPANY_NAME_BLANK = "企业名称不能为空";
    public static final String SOCIAL_CREDIT_CODE_BLANK = "统一社会信用代码不能为空";
    public static final String NATIONALITY_BLANK = "所属国家不能为空";
    public static final String PROVINCE_BLANK = "省份不能为空";
    public static final String CITY_BLANK = "城市不能为空";
    public static final String PROVINCE_CITY_NOT_MATCH = "省市不匹配";

    public static final String ADDRESS_BLANK = "营业执照登记地址不能为空";
    public static final String ADDRESS_LEN_FAIL = "营业执照登记地址长度有误";
    public static final String LEGAL_PERSON_BLANK = "企业法律代表人/负责人不能为空";
    public static final String LEGAL_PERSON_LEN_FAIL = "企业法律代表人/负责人长度有误";
    public static final String LEGAL_PERSON_CODE_BLANK = "证件号码不能为空";
    public static final String LEGAL_PERSON_CODE_LEN_FAIL = "证件号码不合法";
    public static final String CERTIFICATE_TYPE_BLANK = "证件类型不能为空";
    public static final String CERTIFICATE_TYPE_INCORRECT = "证件类型不正确";

    public static final String FILE_CONTENT_BLANK = "上传附件不能为空";
    public static final String FILE_TYPE_ERROR = "上传附件类型有误";
    public static final String MIN_LEN = "最小长度不能小于";
    public static final String MAX_LEN = "最大长度不能超过";

    public static final String PAGE_NUM_BLANK = "起始页不能为空";
    public static final String PAGE_SIZE_BLANK = "每页条数不能为空";

    public static final String COMPANY_NOTFOUND_BY_FULL_NAME = "企业无效请检查企业全称是否有误，并确认该企业已完成注册流程且具有相关业务权限";

    public static final String COMPANY_ACTIVE_CHECK = "企业信息还未完善";

    public static final String SET_QUOTA_ONLY_COMPANY_BY_C = "该企业不支持授信设置!";

    public static final String QUOTA_UPDATE_ERROR = "您本次操作未修改任何数据";
    public static final String QUOTA_UPDATE_FAIL = "授信更新失败";
    public static final String CREDIT_FLAG_WRONG = "错误的审核状态";
    public static final String CREDIT_FLAG_ERROR = "重复操作，该笔授信已完成处理";

    public static final  String DUPLICATE_KEY_ERROR = "系统数据异常，请联系运营人员";


    public static final String CREDIT_NOT_FOUND = "授信信息未找到";

    public static final String HAS_BEEN_CREDIT = "已有此授信";

    public static final String IS_FULL_FINANCE_CONFIG = "该金融机构的配置信息不完善，请联系运营处理";
    public static final String ACCESS_KEY_ERROR = "链AccessKey校验失败";

    public static final String UPDATE_FAIL = "更新失败,请检查参数是否正确";

    public static final String SERVICE_FEE_HAS_BEEN_EXISTS = "费率信息已存在";

    public static final String COMPANY_TYPE_NOT_BLANK = "企业类型不能为空";

    public static final String PROTOCOL_TYPE_ERROR = "协议类型不支持";
    public static final String WORD_TO_PDF_SERVER_ERROR = "协议转pdf失败";

    public static final String GET_USER_BY_CACHE_ERROR = "请重新登录";

    public static final String COMPANY_THREE_ELEMENTS_AUTH_ERROR = "企业信息校验接口异常，请重试或联系运营人员";

    public static final String SPECIAL_RATE_EXISTS_S = "供应商已存在";
    public static final String SPECIAL_RATE_IS_NOT_S = "供应商无效。请检查供应商全称是否有误，并确认该供应商已完成注册流程且具有相关业务权限";

    public static final String DATA_STATUS_ALL_NULL = "保理业务状态和票据业务状态有且只有一个有值";


    public static final String COOPERATION_COMPANY_NAME_EXIST = "合作企业名称在合作企业中已经存在";
    public static final String COOPERATION_COMPANY_SOCIAL_CREDIT_CODE_EXIST = "信用代码在合作企业中已经存在";

    public static final String FINANCE_CREDIT_DISABLE_ERROR = "融信开立方的授信已被禁用，请联系平台运营处理";

    public static final String TEAM_NAME_HAS_BEEN_EXISTS = "项目组名称重复";
    public static final String TEAM_USER_NOT_FOUND = "当前用户未关联项目组";
    public static final String TEAM_USER_HAS_BEEN_EXISTS = "当前用户已经关联项目组";
    public static final String TEAM_SUM_QUOTA_NOT_LT_USED_QUOTA = "所有额度的总和不可小于该项目组已用的额度总和";

    public static final String DO_NOT_EXCEED_YOUR_AUTHORITY= "请勿越权操作";




}
