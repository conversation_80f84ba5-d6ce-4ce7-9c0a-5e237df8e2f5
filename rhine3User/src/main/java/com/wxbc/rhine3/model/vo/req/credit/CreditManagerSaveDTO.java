package com.wxbc.rhine3.model.vo.req.credit;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 注释
 *
 * <AUTHOR>
 * @date 2024/3/15 15:25
 */
@Data
@ApiModel(description = "客户经理DTO")
public class CreditManagerSaveDTO {

    @ApiModelProperty(value = "客户经理名称", required = true)
    @NotEmpty(message = "客户经理名称不能为空")
    private String managerName;

    @ApiModelProperty("客户经理编号")
    @NotEmpty(message = "客户经理编号不能为空")
    private String managerNo;

    @ApiModelProperty("手机号")
    @NotEmpty(message = "手机号不能为空")
    private String mobile;
}
