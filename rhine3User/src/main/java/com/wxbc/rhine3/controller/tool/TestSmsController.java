package com.wxbc.rhine3.controller.tool;

import com.wxbc.rhine3.bean.request.SendMessageRequest;
import com.wxbc.rhine3.constants.CommonConst;
import com.wxbc.rhine3.service.jrbank.JrBankInvokeService;
import com.wxbc.rhine3.utils.SendMessageUtil;
import com.wxbc.scaffold.common.definition.exception.BizException;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.util.ArrayList;

import static com.wxbc.rhine3.constant.returnCode.PledgeConfigReturnCode.PLE_SMS_BOMBING;

/**
 * <AUTHOR> zheng<PERSON><PERSON>
 * @Date : 2023-06-13 10:20
 * @Description :
 */
@RestController
@Validated
@Slf4j
@Api(tags = "自测短信接口")
public class TestSmsController {
    @Autowired
    private SendMessageUtil messageUtil;

    @Autowired
    private JrBankInvokeService jrBankInvokeService;

    @PostMapping(value = "/test/mesage/send", produces = {CommonConst.PRODUCE})
    public void sendMesage(@NotBlank @RequestParam("mobile") String mobile, @NotBlank @RequestParam("templateCode") String templateCode, @Valid @RequestParam("parameter") String parameter) throws IOException {
        SendMessageRequest loginCodeInfo = new SendMessageRequest();
        loginCodeInfo.setTemplateCode(templateCode);
        loginCodeInfo.setPhoneNumber(mobile);
        ArrayList<String> smsParams = new ArrayList<>();
        smsParams.add(parameter);
        loginCodeInfo.setSmsParams(smsParams);
        messageUtil.sendMessage(loginCodeInfo);
    }

    @GetMapping(value = "/test/mesage/can/send", produces = {CommonConst.PRODUCE})
    public void sendMesage(@NotBlank @RequestParam("mobile") String mobile)  {
        if(jrBankInvokeService.canSendSms(mobile)){
            throw new BizException(PLE_SMS_BOMBING, "短信发送频次过高,请稍后再试");
        }
    }

}
