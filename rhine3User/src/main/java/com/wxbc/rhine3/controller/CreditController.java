package com.wxbc.rhine3.controller;

import com.github.pagehelper.PageInfo;
import com.wxbc.rhine3.bean.CoreAgreementFieldsVO;
import com.wxbc.rhine3.bean.Credit;
import com.wxbc.rhine3.bean.CreditModifyPara;
import com.wxbc.rhine3.bean.request.*;
import com.wxbc.rhine3.common.Response;
import com.wxbc.rhine3.constant.ApiDesConst;
import com.wxbc.rhine3.constant.url.CreditUrl;
import com.wxbc.rhine3.constants.CommonConst;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.service.credit.CreditService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.IOException;

/**
 * Created by lijun on 6/18/19
 */
@RestController
@Validated
@Slf4j
@Api(tags = "企业端授信相关接口")
public class CreditController {

    @Autowired
    private CreditService creditService;

    @ApiOperation(value = ApiDesConst.API_URL_CREDIT_QUERY, notes = ApiDesConst.API_URL_CREDIT_QUERY)
    @PostMapping(value = CreditUrl.API_URL_CREDIT_QUERY)
    public Response<PageInfo<Credit>> creditQueryList(@RequestBody CreditQueryPara creditQueryPara) throws ParameterException {
        //queryFromRole是支付发起方的角色类型:0-普通操作员 1-限额操作员 2-项目操作员
        return Response.success(creditService.queryCreditList(creditQueryPara, "0"));
    }

    @ApiOperation(value = ApiDesConst.API_URL_CREDIT_QUERY_FOR_BUILD, notes = ApiDesConst.API_URL_CREDIT_QUERY_FOR_BUILD)
    @PostMapping(value = CreditUrl.API_URL_CREDIT_QUERY_FOR_BUILD)
    public Response<PageInfo<Credit>> creditQueryListForBuild(@RequestBody CreditQueryPara creditQueryPara) throws ParameterException {
        //queryFromRole是支付发起方的角色类型:0-普通操作员 1-限额操作员 2-项目操作员
        return Response.success(creditService.queryCreditList(creditQueryPara, "1"));
    }


    @ApiOperation(value = ApiDesConst.API_URL_CREDIT_QUERY_FOR_TEAM, notes = ApiDesConst.API_URL_CREDIT_QUERY_FOR_TEAM)
    @PostMapping(value = CreditUrl.API_URL_CREDIT_QUERY_FOR_TEAM)
    public Response<PageInfo<Credit>> creditQueryListForTeam(@RequestBody CreditQueryPara creditQueryPara) throws ParameterException {
        //queryFromRole是支付发起方的角色类型:0-普通操作员 1-限额操作员 2-项目操作员
        return Response.success(creditService.queryCreditList(creditQueryPara, "2"));
    }

    @ApiOperation(value = ApiDesConst.API_URL_CREDIT_AUDIT, notes = ApiDesConst.API_URL_CREDIT_AUDIT)
    @PostMapping(value = CreditUrl.API_URL_CREDIT_AUDIT)
    public Response<Void> auditCredit(@RequestBody CreditAuditPara para) throws ParameterException {
        creditService.auditCredit(para);
        return Response.success();
    }


    /**
     * cou额度设置接口
     */
    @ApiOperation(value = ApiDesConst.API_URL_CREDIT_MODIFY, notes = ApiDesConst.API_URL_CREDIT_MODIFY)
    @PostMapping(value = CreditUrl.API_URL_CREDIT_MODIFY)
    public Response<Void> setCompanyQuota(@Valid @RequestBody CreditModifyPara para) throws ParameterException {
        creditService.updateCompanyQuota(para);
        return Response.success();
    }

    @ApiOperation(value = ApiDesConst.API_URL_CREDIT_QUERY_BY_COMPANY, notes = ApiDesConst.API_URL_CREDIT_QUERY_BY_COMPANY)
    @PostMapping(value = CreditUrl.API_URL_CREDIT_QUERY_BY_COMPANY)
    public Response<Credit> creditQueryByCompany2FI(@NotBlank @RequestParam("companyUuid") String companyUuid, @NotBlank @RequestParam("fiUuid") String fiUuid,@RequestParam("type") int type) {
        Response<Credit> response = Response.success();
        response.setData(creditService.queryCreditByCompany2FI(companyUuid, fiUuid,true,type));
        return response;
    }

    /**
     * 企业母公司查询子公司
     *
     * @param para
     * @return
     */
    @ApiOperation(value = ApiDesConst.API_URL_QUERY_CHILDRENS_CREDIT_BY_PARENT_COMPANY, notes = ApiDesConst.API_URL_QUERY_CHILDRENS_CREDIT_BY_PARENT_COMPANY)
    @PostMapping(value = CreditUrl.API_URL_QUERY_CHILDREN_CREDIT_BY_PARENT_COMPANY)
    public Response<PageInfo<Credit>> queryChildCreditByParent(@Valid @RequestBody CreditChildrensQueryPara para) {
        Response<PageInfo<Credit>> response = Response.success();
        response.setData(creditService.queryChildrenCreditListByParent(para));
        return response;
    }


    @ApiOperation(value = ApiDesConst.API_URL_CREDIT_LIST_EXPORT, notes = ApiDesConst.API_URL_CREDIT_LIST_EXPORT)
    @PostMapping(value = CreditUrl.API_URL_CREDIT_LIST_EXPORT, produces = {CommonConst.PRODUCE})
    public void exportCreditList(@Valid @RequestBody CreditExportPara creditExportPara, HttpServletResponse response) throws IOException {
        creditService.exportCreditList(creditExportPara, response);
    }

    @ApiOperation(value = ApiDesConst.API_URL_CREDIT_ENABLE_MODIFY, notes = ApiDesConst.API_URL_CREDIT_ENABLE_MODIFY)
    @PostMapping(value = CreditUrl.API_URL_CREDIT_ENABLE_MODIFY)
    public Response<Boolean> modifyCreditEnable(@RequestBody CreditEnableModifyPara creditEnableModifyPara) {
        creditService.modifyCreditEnable(creditEnableModifyPara);
        return Response.success(true);
    }

    @ApiOperation(value = ApiDesConst.API_URL_GET_CORE_AGREEMENT_FIELDS, notes = ApiDesConst.API_URL_GET_CORE_AGREEMENT_FIELDS)
    @GetMapping(value = CreditUrl.API_URL_GET_CORE_AGREEMENT_FIELDS)
    public Response<CoreAgreementFieldsVO> getCoreAgreementFields(@RequestParam("showFlag") Boolean showFlag) {
        return Response.success(creditService.getCoreAgreementFields(showFlag,null));
    }

    @ApiOperation(value = ApiDesConst.API_URL_CREDIT_SIGN_STATUS_MODIFY, notes = ApiDesConst.API_URL_CREDIT_SIGN_STATUS_MODIFY)
    @PostMapping(value = CreditUrl.API_URL_CREDIT_SIGN_STATUS_MODIFY)
    public Response<Boolean> modifyCreditSignStatus(@Valid @RequestBody CreditSignStatusModifyPara para) {
        creditService.modifyCreditSignStatus(para);
        return Response.success(true);
    }
}
