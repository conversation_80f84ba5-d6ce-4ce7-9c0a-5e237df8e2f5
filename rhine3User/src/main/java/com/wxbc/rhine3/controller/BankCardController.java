package com.wxbc.rhine3.controller;

import cn.hutool.core.util.DesensitizedUtil;
import com.wxbc.rhine3.bean.BankCardPara;
import com.wxbc.rhine3.bean.BankCardQueryDTO;
import com.wxbc.rhine3.bean.BankCardQueryPara;
import com.wxbc.rhine3.bean.DeleteBankCardPara;
import com.wxbc.rhine3.bean.SupplementCompanyPara;
import com.wxbc.rhine3.bean.jrbank.WXZHCXReq;
import com.wxbc.rhine3.bean.jrbank.WXZHCXRes;
import com.wxbc.rhine3.bean.CompanyBankCardResponse;
import com.wxbc.rhine3.common.Response;
import com.wxbc.rhine3.constant.ApiDesConst;
import com.wxbc.rhine3.constant.url.BankCardUrl;
import com.wxbc.rhine3.constants.CommonConst;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.service.SecureService;
import com.wxbc.rhine3.service.card.BankBranchService;
import com.wxbc.rhine3.service.card.BankCardService;
import com.wxbc.scaffold.common.definition.response.BaseResponse;
import com.wxbc.verification.vo.BankConditionQueryPara;
import com.wxbc.verification.vo.BankInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;

/**
 * Created by Zhaochen on 4/13/18
 *
 * <AUTHOR>
 */
@RestController
@Validated
@Slf4j
@Api(tags = "银行卡相关接口")
public class BankCardController {

    @Resource
    private BankCardService bankCardService;
    @Resource
    private SecureService secureService;
    @Resource
    private BankBranchService bankBranchService;

    @ApiOperation(value = ApiDesConst.API_BANKCARD_SUPPLEMENT, notes = ApiDesConst.API_BANKCARD_SUPPLEMENT)
    @PostMapping(value = BankCardUrl.API_URL_BANKCARD_SUPPLEMENT, produces = {CommonConst.PRODUCE})
    public Response<Boolean> supplementCompany(@Valid @RequestBody SupplementCompanyPara para) throws ParameterException, IOException {
        // 银行卡 是Rsa加密过的，需要先解密
        if (StringUtils.isNotEmpty(para.getAccountNum())) {
            para.setAccountNum(secureService.decryptByRsa(para.getAccountNum()));
        }
        bankCardService.supplementBankCard(para);
        return Response.success(Boolean.TRUE);
    }


    @ApiOperation(value = ApiDesConst.API_URL_MODIFY_BANKCARD, notes = ApiDesConst.API_URL_MODIFY_BANKCARD)
    @PostMapping(value = BankCardUrl.API_URL_MODIFY_BANKCARD, produces = {CommonConst.PRODUCE})
    public Response<Boolean> modifyBankCard(@Valid @RequestBody BankCardPara bankCardPara) throws ParameterException, IOException {
        // 银行卡 是Rsa加密过的，需要先解密
        if (StringUtils.isNotEmpty(bankCardPara.getAccountNum())) {
            bankCardPara.setAccountNum(secureService.decryptByRsa(bankCardPara.getAccountNum()));
        }
        bankCardService.modifyBankCard(bankCardPara);
        return Response.success(Boolean.TRUE);
    }


    @ApiOperation(value = ApiDesConst.API_URL_DELETE_BANKCARD, notes = ApiDesConst.API_URL_DELETE_BANKCARD)
    @PostMapping(value = BankCardUrl.API_URL_DELETE_BANKCARD, produces = {CommonConst.PRODUCE})
    public Response<Boolean> deleteBankCard(@Valid @RequestBody DeleteBankCardPara para) {
        bankCardService.deleteBankCard(para.getId());
        return Response.success(Boolean.TRUE);
    }

    @ApiOperation(value = ApiDesConst.API_URL_QUERY_BANKCARD, notes = ApiDesConst.API_URL_QUERY_BANKCARD)
    @PostMapping(value = BankCardUrl.API_URL_QUERY_BANKCARD, produces = {CommonConst.PRODUCE})
    public Response<List<BankCardPara>> queryBankCardList(@Valid @RequestBody BankCardQueryPara bankCardQueryPara) throws ParameterException {
        List<BankCardPara> data = bankCardService.selectBankCardListByCompanyUuid(bankCardQueryPara);
        // 银行卡号脱敏
        if (CollectionUtils.isNotEmpty(data)) {
            for (BankCardPara cardPara : data) {
                if (StringUtils.isNotEmpty(cardPara.getAccountNum())) {
                    cardPara.setAccountNum(DesensitizedUtil.bankCard(cardPara.getAccountNum()));
                }
            }
        }
        return Response.success(data);
    }

    @ApiOperation(value = ApiDesConst.API_URL_QUERY_AVAILABLE_CARD_BY_BUSINESSTYPE, notes = ApiDesConst.API_URL_QUERY_AVAILABLE_CARD_BY_BUSINESSTYPE)
    @PostMapping(value = BankCardUrl.API_URL_QUERY_AVAILABLE_CARD_BY_BUSINESSTYPE, produces = {CommonConst.PRODUCE})
    public Response<BankCardPara> queryAvailableBankCardByCompanyAndBusinessType(@Valid @RequestBody BankCardQueryPara bankCardQueryPara) throws ParameterException {
        BankCardPara data = bankCardService.queryAvailableBankCardByCompanyAndBusinessType(bankCardQueryPara);
        return Response.success(data);
    }

    /**
     * 查询银行卡-江阴银行
     *
     * @param bankCardQueryDTO BankCardQueryDTO
     * @return BankCardPara
     */
    @ApiOperation(value = "根据企业Id和银行名字查询企业的可用卡", notes = "根据企业Id和银行名字查询企业的可用卡")
    @PostMapping(value = "/bankcard/query/available-by-name", produces = {CommonConst.PRODUCE})
    public Response<BankCardPara> queryAvailableBankCardByCompanyAndBankName(@Valid @RequestBody BankCardQueryDTO bankCardQueryDTO) {
        BankCardPara data = bankCardService.queryAvailableBankCardByCompanyAndBankName(bankCardQueryDTO);
        return Response.success(data);
    }

    /**
     * 根据账号获取江阴账户详情
     *
     * @param req
     * @return
     */
    @ApiOperation(value = "根据账号获取江阴账户详情", notes = "根据账号获取江阴账户详情")
    @PostMapping(value = "/bankcard/jr-account/by-name", produces = {CommonConst.PRODUCE})
    public BaseResponse<WXZHCXRes> queryJrAccountInfo(@Valid @RequestBody WXZHCXReq req) {
        return bankCardService.jrAccountQuery(req);
    }

    @ApiOperation(value = "根据公司id获取银行账户详情", notes = "根据公司id获取银行账户详情")
    @GetMapping(value = "/bankcard/account/by-org-id", produces = {CommonConst.PRODUCE})
    public Response<List<CompanyBankCardResponse>> queryOrgBankCard(@NotNull @RequestParam("orgId") Long orgId) {
        return Response.success(bankCardService.queryOrgBankCard(orgId));
    }

    @ApiOperation(value = "根据条件查询开户行", notes = "根据条件查询开户行")
    @PostMapping(value = "/bank/condition/query", produces = {CommonConst.PRODUCE})
    public Response<List<BankInfoDTO>> bankConditionQuery(@RequestBody @Valid BankConditionQueryPara para) throws Exception {
        return Response.success(bankBranchService.bankConditionQuery(para));
    }
}

