package com.wxbc.rhine3.controller;

import com.wxbc.rhine3.bean.request.GeneralConfigPara;
import com.wxbc.rhine3.bean.request.GeneralConfigQueryPara;
import com.wxbc.rhine3.common.Response;
import com.wxbc.rhine3.constant.ApiDesConst;
import com.wxbc.rhine3.constant.url.GeneralConfigUrl;
import com.wxbc.rhine3.constants.CommonConst;
import com.wxbc.rhine3.service.GeneralConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @company: 万向区块链
 * @description
 * @author: lixingxing
 * @create: 2021-10-29 11:28
 **/
@RestController
@Validated
@Slf4j
@Api(tags = "通用配置控制类")
public class GeneralConfigController {

    @Resource
    private GeneralConfigService generalConfigService;

    @ApiOperation(value = ApiDesConst.API_URL_GENERAL_CONFIG_MODIFY, notes = ApiDesConst.API_URL_GENERAL_CONFIG_MODIFY)
    @PostMapping(value = GeneralConfigUrl.API_URL_GENERAL_CONFIG_MODIFY, produces = {CommonConst.PRODUCE})
    public Response<Boolean> modifyGeneralConfig(@Valid @RequestBody GeneralConfigPara generalConfigPara) {
        generalConfigService.updateGeneralConfig(generalConfigPara.getConfigKey(),generalConfigPara.getConfigValue());
        return Response.success(Boolean.TRUE);
    }

    @ApiOperation(value = ApiDesConst.API_URL_QUERY_GENERAL_CONFIG_BY_KEYS, notes = ApiDesConst.API_URL_QUERY_GENERAL_CONFIG_BY_KEYS)
    @PostMapping(value = GeneralConfigUrl.API_URL_QUERY_GENERAL_CONFIG_BY_KEYS, produces = {CommonConst.PRODUCE})
    public Response<List<GeneralConfigPara>> queryGeneralConfig(@Valid @RequestBody GeneralConfigQueryPara generalConfigQueryPara) {
        List<GeneralConfigPara> configParaList = generalConfigService.queryGeneralConfigByKeys(generalConfigQueryPara.getConfigKeys());
        return Response.success(configParaList);
    }

}
