package com.wxbc.rhine3.controller;

import com.wxbc.rhine3.bean.Company;
import com.wxbc.rhine3.bean.StatisticalCorePara;
import com.wxbc.rhine3.bean.StatisticalQueryPara;
import com.wxbc.rhine3.bean.response.StatisticalCompanyResponse;
import com.wxbc.rhine3.common.Response;
import com.wxbc.rhine3.constant.ApiDesConst;
import com.wxbc.rhine3.constant.url.StatisticalUrl;
import com.wxbc.rhine3.constants.CommonConst;
import com.wxbc.rhine3.service.StatisticalUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@Validated
@Slf4j
@Api(tags = "User模块图表统计相关")
public class StatisticalUserController {

    @Autowired
    private StatisticalUserService statisticalUserService;


    @ApiOperation(value = ApiDesConst.API_URL_QUERY_COMPANY_COUNT, notes = ApiDesConst.API_URL_QUERY_COMPANY_COUNT)
    @GetMapping(value = StatisticalUrl.API_URL_QUERY_COMPANY_COUNT, produces = {CommonConst.PRODUCE})
    public Response queryCompanyCount(){
        return Response.success(statisticalUserService.queryCompanyQuantity());
    }

    @ApiOperation(value = ApiDesConst.API_URL_QUERY_COMPANY_FOR_CHART, notes = ApiDesConst.API_URL_QUERY_COMPANY_FOR_CHART)
    @PostMapping(value = StatisticalUrl.API_URL_QUERY_COMPANY_FOR_CHART, produces = {CommonConst.PRODUCE})
    public Response queryCompanyChartCount(@Valid  @RequestBody StatisticalQueryPara statisticalQueryPara){
        return Response.success(statisticalUserService.queryCompanyQuantityChartData(statisticalQueryPara));
    }


    @ApiOperation(value = ApiDesConst.API_QUERY_ALL_COMPANY, notes = ApiDesConst.API_QUERY_ALL_COMPANY)
    @GetMapping(value = StatisticalUrl.API_QUERY_ALL_COMPANY, produces = {CommonConst.PRODUCE})
    public Response<List<Company>> queryAllCompany(){
        return Response.success(statisticalUserService.queryCompanyAndParent());
    }


    @ApiOperation(value = ApiDesConst.API_QUERY_STATISTICAL_CORE_COMPANY, notes = ApiDesConst.API_QUERY_STATISTICAL_CORE_COMPANY)
    @PostMapping(value = StatisticalUrl.API_QUERY_STATISTICAL_CORE_COMPANY, produces = {CommonConst.PRODUCE})
    public Response<StatisticalCompanyResponse> queryStatisticalCompany(@Valid @RequestBody StatisticalCorePara para){
        return Response.success(statisticalUserService.queryCompanyStatisticalDetail(para));
    }
    
}
