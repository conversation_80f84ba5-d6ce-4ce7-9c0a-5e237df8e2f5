package com.wxbc.rhine3.controller.credit;

import com.github.pagehelper.PageInfo;
import com.wxbc.rhine3.bean.request.credit.OrderCreditApplicationPara;
import com.wxbc.rhine3.bean.request.credit.OrderCreditApplicationQueryPara;
import com.wxbc.rhine3.bean.response.credit.OrderCreditApplicationRes;
import com.wxbc.rhine3.common.Response;
import com.wxbc.rhine3.constants.CommonConst;
import com.wxbc.rhine3.service.credit.OrderCreditApplicationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @company: 万向区块链
 * @description 订单平台授信申请管理
 * @author: lixingxing
 * @create: 2023-01-05 13:57
 **/
@RestController
@Slf4j
@Validated
@RequestMapping(produces = {CommonConst.PRODUCE}, method = RequestMethod.POST)
@Api(tags = "订单平台授信申请管理")
public class OrderCreditApplicationController {

    @Autowired
    private OrderCreditApplicationService orderCreditApplicationService;

    @ApiOperation(value = "新增授信申请", notes = "新增授信申请")
    @PostMapping(value = "/order/credit/apply", produces = {CommonConst.PRODUCE})
    public Response<Void> orderCreditApply(@Valid @RequestBody OrderCreditApplicationPara para) {
        Response response=Response.success();
        orderCreditApplicationService.saveOrderCreditApplication(para);
        return response;

    }

    @ApiOperation(value = "订单授信申请列表", notes = "订单授信申请列表")
    @PostMapping(value = "/order/credit/apply/list", produces = {CommonConst.PRODUCE})
    public Response<PageInfo<OrderCreditApplicationRes>> orderCreditApplyList(@Valid @RequestBody OrderCreditApplicationQueryPara para) {
        return Response.success(orderCreditApplicationService.queryOrderCreditApplicationList(para));
    }

    @ApiOperation(value = "订单授信申请详情", notes = "订单授信申请详情")
    @PostMapping(value = "/order/credit/apply/detail", produces = {CommonConst.PRODUCE})
    public Response<OrderCreditApplicationRes> orderCreditApplyDetail(@Valid @RequestBody OrderCreditApplicationQueryPara para) {
        return Response.success(orderCreditApplicationService.queryOrderCreditApplicationDetail(para));
    }
}
