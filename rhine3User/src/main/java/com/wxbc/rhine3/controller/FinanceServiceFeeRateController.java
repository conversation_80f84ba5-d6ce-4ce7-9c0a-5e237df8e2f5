package com.wxbc.rhine3.controller;

import com.wxbc.rhine3.bean.FinancePrepareServiceFee;
import com.wxbc.rhine3.bean.request.FinanceServiceFeeQueryPara;
import com.wxbc.rhine3.common.Response;
import com.wxbc.rhine3.constant.ApiDesConst;
import com.wxbc.rhine3.constant.url.ServiceFeeUrl;
import com.wxbc.rhine3.constants.CommonConst;
import com.wxbc.rhine3.service.FinanceFeeRateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

@RestController
@Validated
@Slf4j
@Api(tags = "金融服务费率控制类")
public class FinanceServiceFeeRateController {
    @Autowired
    private FinanceFeeRateService financeFeeRateService;

    /**
     * 根据 fi center supplier
     * 三个公司的uuid查询
     * @param para
     * @return
     */
    @ApiOperation(value = ApiDesConst.API_URL_SP_QUERY_FINANCE_SUPPLIER_SERVICE_FEE_RATE, notes = ApiDesConst.API_URL_SP_QUERY_FINANCE_SUPPLIER_SERVICE_FEE_RATE)
    @PostMapping(value = ServiceFeeUrl.API_URL_FINANCE_GET_SERVICE_FEE_RATE, produces = {CommonConst.PRODUCE})
    public Response getSupplierServiceFee(@Valid @RequestBody FinancePrepareServiceFee para)  {
        Response response = Response.success();
        response.setData(financeFeeRateService.searchServiceFee(para));
        return response;
    }


    @ApiOperation(value = ApiDesConst.API_OO_QUERY_FINANCE_EXPORT, notes = ApiDesConst.API_OO_QUERY_FINANCE_EXPORT)
    @PostMapping(value = ServiceFeeUrl.API_URL_OO_SERVICE_FEE_RATE_EXPORT)
    public Response<Void> exportServiceFeeRate(@Valid @RequestBody FinanceServiceFeeQueryPara queryPara, HttpServletResponse response) throws IOException {
        financeFeeRateService.exportServiceFeeList(queryPara,response);
        return Response.success();
    }



}
