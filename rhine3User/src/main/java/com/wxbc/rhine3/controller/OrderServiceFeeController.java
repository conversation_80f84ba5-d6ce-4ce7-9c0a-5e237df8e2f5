package com.wxbc.rhine3.controller;

import com.wxbc.rhine3.bean.QueryListFCompanyFeeVo;
import com.wxbc.rhine3.bean.QueryOrderFeeProductListPara;
import com.wxbc.rhine3.bean.UpdateOrderServiceFeePara;
import com.wxbc.rhine3.common.BaseInfo;
import com.wxbc.rhine3.common.Response;
import com.wxbc.rhine3.constants.CommonConst;
import com.wxbc.rhine3.exception.ParameterException;
import com.wxbc.rhine3.service.OrderFeeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * User: yanhengfu
 * Date: 2023/1/5
 * Time: 11:41
 * Description: des
 */
@RestController
@Validated
@Slf4j
@Api(tags = "订单端费率配置")
public class OrderServiceFeeController {

    @Autowired
    private OrderFeeService orderFeeService;


    @ApiOperation(value = "查询金融产品服务费率(产品)", notes = "查询金融产品服务费率(产品)")
    @PostMapping(value = "/oo/order/service/productFee/list", produces = {CommonConst.PRODUCE})
    public Response queryOrderFeeProductList(@Valid @RequestBody QueryOrderFeeProductListPara para) {
        Response response = Response.success();
        response.setData(orderFeeService.queryListFProduct(para));
        return response;
    }

    @ApiOperation(value = "查询金融产品服务费率(公司)", notes = "查询金融产品服务费率(公司)")
    @PostMapping(value = "/oo/order/service/companyFee/list", produces = {CommonConst.PRODUCE})
    public Response queryOrderFeeCompanyList(@Valid @RequestBody QueryListFCompanyFeeVo para) {
        Response response = Response.success();
        response.setData(orderFeeService.queryListFCompany(para));
        return response;
    }



    @ApiOperation(value = "修改产品服务费率", notes = "修改产品服务费率")
    @PostMapping(value = "/oo/order/service/fee/modify", produces = {CommonConst.PRODUCE})
    public Response updateOrderFee(@Valid @RequestBody UpdateOrderServiceFeePara para) throws ParameterException {
        Response response = Response.success();
        orderFeeService.update(para);
        return response;
    }

    @ApiOperation(value = "删除产品服务费率", notes = "删除产品服务费率")
    @PostMapping(value = "/oo/order/service/fee/del", produces = {CommonConst.PRODUCE})
    public Response deleteFee(@Valid @RequestBody BaseInfo para) {
        Response response = Response.success();
        orderFeeService.delete(para.getId());
        return response;
    }







}