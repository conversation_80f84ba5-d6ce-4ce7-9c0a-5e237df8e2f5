package com.wxbc.rhine3.controller.rbac;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.wxbc.scaffold.common.definition.response.BaseResponse;
import com.wxbc.scaffold.common.definition.response.ResponseFormat;
import com.wxbc.security.modules.sys.entity.SysMenuEntity;
import com.wxbc.security.modules.sys.service.SysMenuService;
import io.swagger.annotations.Api;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * service-vena
 *
 * <AUTHOR> chen cheng
 * Date         : 2023/4/13 15:53
 * Jdk          : 1.8
 * Description  :
 */
@Api(tags = "AAAAA")
@RestController
public class RbacUpdateController {

    @Resource
    private SysMenuService sysMenuService;

    @GetMapping("/rbac/test/test/update")
    public BaseResponse<Boolean> updateMenu(@RequestParam("id")Long id,
                                            @RequestParam("lob") String lob,
                                            @RequestParam("projectCode") String projectCode) {
        updateChild(lob, projectCode, Lists.newArrayList(id));
        return ResponseFormat.success();
    }

    @GetMapping("/rbac/test/test/update2")
    public BaseResponse<Boolean> updateMenu(String name) {
        final List<SysMenuEntity> list = sysMenuService.list(new QueryWrapper<SysMenuEntity>().in("ext_type", "web", "default", name));
        if(CollectionUtils.isEmpty(list)) {
            return ResponseFormat.success();
        }

        final List<Long> collect = list.stream().map(SysMenuEntity::getParentId).collect(Collectors.toList());
        updateFromParent(collect);
        return ResponseFormat.success();
    }


    private void updateFromParent(List<Long> idList) {
        final List<SysMenuEntity> entityList = sysMenuService.list(new QueryWrapper<SysMenuEntity>().in("id", idList));
        if(CollectionUtils.isEmpty(entityList)) {
            return;
        }
        List<Long> list = Lists.newArrayList();
        for(SysMenuEntity entity : entityList) {
            if(entity.getParentId() == 0) {
                updateChild(entity.getExtType(), entity.getProjectCode(), Lists.newArrayList(entity.getId()));
            } else {
                list.add(entity.getParentId());
            }
        }
        if(CollectionUtils.isEmpty(list)) {
            return;
        }

        final List<Long> collect = list.stream().distinct().collect(Collectors.toList());
        updateFromParent(collect);
    }

    private void updateChild(String lob, String projectCode, List<Long> idList) {
        sysMenuService.update(new UpdateWrapper<SysMenuEntity>()
                .set("project_code", projectCode)
                .set("ext_type", lob)
                .in("id", idList));

        final List<SysMenuEntity> entityList = sysMenuService.list(new QueryWrapper<SysMenuEntity>().select("id").in("parent_id", idList));
        if(CollectionUtils.isEmpty(entityList)) {
            return;
        }
        final List<Long> menuIdList = entityList.stream().map(SysMenuEntity::getId).collect(Collectors.toList());
        updateChild(lob, projectCode, menuIdList);
    }
}
