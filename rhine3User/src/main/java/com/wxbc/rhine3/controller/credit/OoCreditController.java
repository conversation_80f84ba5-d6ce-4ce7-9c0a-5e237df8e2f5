package com.wxbc.rhine3.controller.credit;

import com.github.pagehelper.PageInfo;
import com.wxbc.rhine3.bean.request.CreditQueryListPara;
import com.wxbc.rhine3.bean.response.OoCoreCreditListResponse;
import com.wxbc.rhine3.bean.response.OoProviderCreditDetailResponse;
import com.wxbc.rhine3.bean.response.OoProviderCreditListResponse;
import com.wxbc.rhine3.common.Response;
import com.wxbc.rhine3.model.vo.req.credit.QueryOrderCreditVo;
import com.wxbc.rhine3.model.vo.res.credit.OrderCreditVo;
import com.wxbc.rhine3.service.credit.CreditService;
import com.wxbc.rhine3.service.credit.OoOrderCreditService;
import com.wxbc.scaffold.common.definition.response.ResponseFormat;
import com.wxbc.venus.user.util.UserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * User: yanhengfu
 * Date: 2022/11/7
 * Time: 10:53
 * Description: des
 */
@Slf4j
@RestController
@Api(tags = "运营机构授信管理")
public class OoCreditController {

    @Resource
    private OoOrderCreditService ooOrderCreditService;

    @Autowired
    private CreditService creditService;

    @ApiOperation(value = "运管机构查询企业授信列表", notes = "运管机构查询企业授信列表")
    @PostMapping(value = "/oo/credit/list")
    public ResponseFormat<PageInfo<OrderCreditVo>> ooCreateCredit(@Valid @RequestBody QueryOrderCreditVo para) {
        return ResponseFormat.success(ooOrderCreditService.queryCreditList(para));
    }

    @ApiOperation(value = "运管机构查询企业授信详情", notes = "运管机构查询企业授信详情")
    @PostMapping(value = "/oo/credit/detail")
    public ResponseFormat<OrderCreditVo> ooQueryCreditDetail(@Valid @RequestBody QueryOrderCreditVo para) {
        return ResponseFormat.success(ooOrderCreditService.queryDetail(para));
    }


    @ApiOperation(value = "运营机构查询核心企业开立额度列表", notes = "运营机构查询核心企业开立额度列表")
    @PostMapping(value = "/oo/corer/credit/list")
    public Response<PageInfo<OoCoreCreditListResponse>> queryCorerCreditList(@Valid @RequestBody CreditQueryListPara para) {
        para.setOperatingOrgId(UserUtil.get().getOrgId());
        //return Response.success(creditService.queryCreditListByOoUuid(para));
        return Response.success(creditService.queryCorerCreditList(para));
    }


    @ApiOperation(value = "运营机构查询供应商授信列表", notes = "运营机构查询供应商授信列表")
    @PostMapping(value = "/oo/provider/credit/list")
    public Response<PageInfo<OoProviderCreditListResponse>> queryProviderCreditList(@Valid @RequestBody CreditQueryListPara para) {
        para.setOperatingOrgId(UserUtil.get().getOrgId());
        return Response.success(creditService.queryProviderCreditList(para));
    }


    @ApiOperation(value = "运营机构查询供应商授信详情", notes = "运营机构查询供应商授信详情")
    @GetMapping(value = "/oo/provider/credit/{creditId}")
    public Response<OoProviderCreditDetailResponse> queryProviderCreditDetail(@PathVariable Long creditId) {
        return Response.success(creditService.queryProviderCreditDetail(creditId));
    }
}