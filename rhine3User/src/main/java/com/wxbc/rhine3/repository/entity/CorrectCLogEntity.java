package com.wxbc.rhine3.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * Description : auto generated entity
 * JDK         : 1.8
 * ProjectName : vena
 * Date        : 2023-06-30 15:31:52
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 */
@Data
@TableName("`correct_c_log`")
public class CorrectCLogEntity {

    /**
     * wave id
     */
    @TableId(value = "`id`", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建时间
     */
    @TableField("`create_time`")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("`update_time`")
    private LocalDateTime updateTime;

    /**
     * 1-已对账，2-未对账
     */
    @TableField("`status`")
    private Integer status;

    /**
     * company表的uuid
     */
    @TableField("`company_uuid`")
    private String companyUuid;

    /**
     * 数据库账户信息快照
     */
    @TableField("`acc_db`")
    private String accDb;


}