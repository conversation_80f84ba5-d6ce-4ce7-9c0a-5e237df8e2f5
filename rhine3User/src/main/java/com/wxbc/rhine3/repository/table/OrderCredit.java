package com.wxbc.rhine3.repository.table;

/**
 * Description : auto generated table constants
 * JDK         : 1.8
 * ProjectName : yan-gen-code
 * Date        : 2022-11-04 10:13:05
 * <AUTHOR> yanh<PERSON>fu
 */
public class OrderCredit {
    private OrderCredit() {
        //do nothing
    }

    public static final String CREDIT_TABLE = " order_credit ";
    public static final String TABLE_HEAD = " id,create_time,update_time,company_uuid,fi_uuid,operating_organization_uuid,uuid ";

    public static final String CREDIT_COLUMNS = " ,used_in_yuan,total_in_yuan,credit_due_date,interest_rate,finance_term,finance_discount";

    public static final  String ALL_COLUMNS = TABLE_HEAD + CREDIT_COLUMNS;

    public static final  String CREDIT_COLUMNS_PARA = " #{totalInYuan},#{creditDueDate},#{interestRate}," +
            "#{financeTerm} ";


    /**
     *
     */
    public static final String ID = "id";

    /**
     *
     */
    public static final String CREATE_TIME = "create_time";

    /**
     *
     */
    public static final String UPDATE_TIME = "update_time";

    /**
     * 状态
     */
    public static final String STATUS = "status";

    /**
     * 授信uuid
     */
    public static final String UUID = "uuid";

    /**
     * 授信编号
     */
    public static final String CREDIT_NO = "credit_no";

    /**
     * 授信总额度(单位:元)
     */
    public static final String TOTAL_IN_YUAN = "total_in_yuan";

    /**
     * 授信已用额度(单位:元)
     */
    public static final String USED_IN_YUAN = "used_in_yuan";

    /**
     * 融资期限
     */
    public static final String FINANCE_TERM = "finance_term";

    /**
     * 授信到期日
     */
    public static final String CREDIT_DUE_DATE = "credit_due_date";

    /**
     * 年化融资利率
     */
    public static final String INTEREST_RATE = "interest_rate";

    /**
     * 授信企业uuid
     */
    public static final String COMPANY_UUID = "company_uuid";

    /**
     * 金融机构uuid
     */
    public static final String FI_UUID = "fi_uuid";

    /**
     * 融资比例
     */
    public static final String FINANCE_DISCOUNT = "finance_discount";

    /**
     * 关联金融产品编号
     */
    public static final String PRODUCT_NO = "product_no";

    /**
     * 运营机构uuid
     */
    public static final String OPERATING_ORGANIZATION_UUID = "operating_organization_uuid";

}