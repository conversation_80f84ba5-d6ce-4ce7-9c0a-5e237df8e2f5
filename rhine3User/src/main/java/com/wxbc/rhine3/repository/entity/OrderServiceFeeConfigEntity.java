package com.wxbc.rhine3.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * Description : auto generated entity
 * JDK         : 1.8
 * ProjectName : yan-gen-code
 * Date        : 2023-01-05 10:18:12
 * <AUTHOR> yanhengfu
 */
@Data
@TableName("`order_service_fee_config`")
public class OrderServiceFeeConfigEntity {

    /**
     * 
     */
    @TableId(value = "`id`", type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    @TableField("`create_time`")
    private LocalDateTime createTime;

    /**
     * 
     */
    @TableField("`update_time`")
    private LocalDateTime updateTime;

    /**
     * 
     */
    @TableField("`status`")
    private String status;

    /**
     * 金融产品编号
     */
    @TableField("`serial_no`")
    private String serialNo;

    /**
     * 关联的企业
     */
    @TableField("`company_uuid`")
    private String companyUuid;


    /**
     * 服务费率  ps:使用时需要除以100
     */
    @TableField("`service_fee_rate`")
    private BigDecimal serviceFeeRate;

    /**
     * 运营机构uuid
     */
    @TableField("`operating_organization_uuid`")
    private String operatingOrganizationUuid;

    /**
     * 业务模式：1-年化 0-单笔
     */
    @TableField("`type`")
    private Integer type;

}