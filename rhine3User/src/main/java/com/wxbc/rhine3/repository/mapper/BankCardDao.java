package com.wxbc.rhine3.repository.mapper;


import com.wxbc.rhine3.bean.BankCardPara;
import com.wxbc.rhine3.bean.BankCardQueryPara;
import org.apache.ibatis.annotations.*;

import java.util.List;

public interface BankCardDao {
    String BANK_CARD_TABLE = " bank_card ";

    String TABLE_HEAD = " id,create_time,update_time,status, ";

    String COLUMNS = " account_name,account_num,account_bank,account_bank_code,company_uuid, " +
            "relation_fi,card_type,business_type ";

    String ALL_COLUMNS = TABLE_HEAD + COLUMNS;

    @Update("<script> " +
            "update " + BANK_CARD_TABLE + " set " +
            "account_name = #{accountName}," +
            "<if test=\"accountNum != null\">account_num=#{accountNum}, </if>" +
            "account_bank=#{accountBank}," +
            "account_bank_code=#{accountBankCode}," +
            "card_type=#{cardType}," +
            "business_type=#{businessType}," +
            "relation_fi=#{relationFi}" +
            "  where id=#{id} "+
            "</script>")
    int updateBankCard(BankCardPara bankCardPara);

    @Delete("delete  from" + BANK_CARD_TABLE +
            " where id=#{id} and company_uuid=#{companyUuid} ")
    int deleteBankCardById(@Param("id") int id,@Param("companyUuid")String companyUuid);

    @Insert("insert into " + BANK_CARD_TABLE + " ( status,account_name,account_num,account_bank,account_bank_code,company_uuid,relation_fi,card_type,business_type ) " +
            "VALUES (#{status},#{accountName},#{accountNum},#{accountBank},#{accountBankCode},#{companyUuid}," +
            "#{relationFi},#{cardType},#{businessType})")
    int insertBankCard(BankCardPara bankCardPara);


    @Select("<script> " +
            "select "+ ALL_COLUMNS +" from "+ BANK_CARD_TABLE +" where  " +
            " company_uuid=#{companyUuid} "+
            "<if test=\"relationFi != null and relationFi != ''\"> and relation_fi=#{relationFi} </if>" +
            "</script>")
    List<BankCardPara> selectBankCardByCompanyUuidAndFi(BankCardQueryPara bankCardQueryPara);

    @Select("<script> " +
            "select "+ ALL_COLUMNS +" from "+ BANK_CARD_TABLE +" where  " +
            " company_uuid=#{companyUuid} "+
            "</script>")
    List<BankCardPara> selectBankCardByCompanyUuid(BankCardQueryPara bankCardQueryPara);

    @Select("select "+ ALL_COLUMNS +" from "+ BANK_CARD_TABLE +
            " where company_uuid=#{companyUuid} and card_type = #{cardType} ")
    List<BankCardPara> selectBankCardByCompanyUuidAndType(@Param("companyUuid") String companyUuid,
                                                          @Param("cardType")String cardType);
    @Select("<script> " +
            "select "+ ALL_COLUMNS +" from "+ BANK_CARD_TABLE +" where  " +
            " company_uuid=#{companyUuid} and card_type = #{cardType} and " +
            "    <foreach collection=\"bankNameList\" item=\"bankName\" open=\"(\" separator=\"OR\" close=\")\">" +
            "          account_bank like concat('%',#{bankName},'%')" +
            "    </foreach>" +
            " limit 1"+
            "</script>")
    BankCardPara selectBankCardByCompanyUuidAndBankName(@Param("companyUuid") String companyUuid,@Param("cardType") String cardType, @Param("bankNameList") List<String> bankNameList);

    @Select("<script> " +
            "select "+ ALL_COLUMNS +" from "+ BANK_CARD_TABLE +" where  " +
            " id=#{id} "+
            "</script>")
    BankCardPara selectBankCardById(@Param("id") Long id);
}
