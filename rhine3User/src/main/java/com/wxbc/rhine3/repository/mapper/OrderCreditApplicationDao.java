package com.wxbc.rhine3.repository.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wxbc.rhine3.bean.request.credit.OrderCreditApplicationQueryPara;
import com.wxbc.rhine3.repository.entity.OrderCreditApplicationEntity;
import com.wxbc.rhine3.repository.table.OrderCreditApplication;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface OrderCreditApplicationDao extends BaseMapper<OrderCreditApplicationEntity> {

    @Select("<script>" +
            "select "+ OrderCreditApplication.ALL_COLUMNS +" from "+ OrderCreditApplication.TABLE +
            " where company_uuid=#{companyUuid} " +
            " order by " + OrderCreditApplication.CREATE_TIME + " desc " +
            "</script>")
    List<OrderCreditApplicationEntity> selectByCompanyUuid(OrderCreditApplicationQueryPara para);


    @Select("<script>" +
            "select "+ OrderCreditApplication.ALL_COLUMNS +" from "+ OrderCreditApplication.TABLE +
            " where fi_uuid=#{fiUuid} "+
            " order by " + OrderCreditApplication.CREATE_TIME + " desc " +
            "</script>")
    List<OrderCreditApplicationEntity> selectByFiUuid(OrderCreditApplicationQueryPara para);


}
