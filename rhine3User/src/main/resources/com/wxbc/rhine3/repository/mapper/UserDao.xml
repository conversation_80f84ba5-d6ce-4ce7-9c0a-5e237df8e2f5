<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wxbc.rhine3.repository.mapper.UserDao">
    <resultMap id="roleResultMap" type="com.wxbc.rhine3.bean.User">
        <result property="id" column="id" javaType="Integer"/>
        <collection property="roleList" ofType="com.wxbc.rhine3.bean.Role"
                    column="id" javaType="ArrayList"
                     select="com.wxbc.rhine3.repository.mapper.VenaRoleDao.getUserRoleList" />
    </resultMap>

    <select id="queryRoleUserMobilesByUserIds" resultType="java.lang.String">
        select distinct u.mobile
        from user u
        where u.status = 1
        <if test="userIdList != null and userIdList.size()>0">
            and u.id in
            <foreach item="item" index="index" collection="userIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryUserIdsByRoleIdsAndOrgIds" resultType="java.lang.Long">
        select ra.user_id
        from rab_account ra
        inner join rab_account_role rar on rar.account_id = ra.id
        inner join rab_role rr on rar.role_id = rr.id
        where ra.status = 1
        and rar.status = 1
        and rr.status = 1
        <if test="companyIdList != null and companyIdList.size()>0">
            and ra.org_id in
            <foreach item="item" index="index" collection="companyIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="roleIdList != null and roleIdList.size()>0">
            and rr.id in
            <foreach item="item" index="index" collection="roleIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryAdminUserIdByOrgId" resultType="java.lang.Long">
        select ra.user_id
        from rab_account ra
        inner join rab_account_role rar on rar.account_id = ra.id
        inner join rab_role rr on rar.role_id = rr.id
        where rr.role_type = 1
        and rr.system_code = 'factor'
        and ra.status = 1
        and rar.status = 1
        and rr.status = 1
        and ra.org_id = #{companyId}
    </select>

    <select id="queryRoleListByRoleIdList" resultType="com.wxbc.rhine3.bean.Role">
        select id, role_name
        from rab_role
        where 1=1
        <if test="roleIdList != null and roleIdList.size()>0">
            and id in
            <foreach item="item" index="index" collection="roleIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryManagerMobileByOrgIdsAndCreditTypes" resultType="java.lang.String">
        select distinct cm.mobile
        from credit_manager cm
        inner join credit_additional ca on cm.id = ca.credit_manager_id
        inner join credit c on ca.credit_id = c.id
        where c.enable = 1
        <if test="companyIdList != null and companyIdList.size()>0">
            and c.company_uuid in
            <foreach item="item" index="index" collection="companyIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="creditTypeList != null and creditTypeList.size()>0">
            and c.type in
            <foreach item="item" index="index" collection="creditTypeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>

