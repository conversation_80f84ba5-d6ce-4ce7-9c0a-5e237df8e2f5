<?xml version="1.0" encoding="UTF-8"?>
<!--设置log4j2的自身log级别为warn -->
<configuration status="warn">
    <appenders>
        <console name="STDOUT" target="SYSTEM_OUT">
            <PatternLayout pattern="[%d] [%t] [%traceId] [%p] [${sys:hostAddress}] [%l] %m%n" />
        </console>
    </appenders>

    <loggers>
        <root level="info">
            <appender-ref ref="STDOUT" />
        </root>
        <logger name="com.wxbc.rhine3.mapper" level="DEBUG" additivity="false" >
            <appender-ref ref="STDOUT" />
        </logger>
        <logger name="com.wxbc.security.modules.sys.dao" level="DEBUG" additivity="false" >
            <appender-ref ref="STDOUT" />
        </logger>
        <logger name="com.wxbc.rhine3.repository.mapper" level="DEBUG" additivity="false" >
            <appender-ref ref="STDOUT" />
        </logger>
    </loggers>

</configuration>